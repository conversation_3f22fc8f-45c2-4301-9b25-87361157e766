# AI Assistant Chrome Extension

基于 OpenRouter API 的智能浏览器助手，参考 Kimi 插件设计，提供对话、文本分析、截图解释等功能。

## 功能特性

- 🤖 **智能对话**: 支持与 AI 进行自然语言对话
- 🔍 **文本探索**: 选择网页文本进行解释、翻译、总结
- 📸 **截图分析**: 截取屏幕内容并进行 AI 分析
- 📄 **页面总结**: 快速总结当前网页内容
- ⚡ **快捷操作**: 支持键盘快捷键和悬浮按钮
- 🎨 **双模式界面**: 支持侧边栏和浮窗两种显示模式

## 支持的 AI 模型

- Google Gemini 2.5 Pro Preview
- Anthropic Claude 3.5 Sonnet
- OpenAI GPT-4o
- 更多模型可通过 OpenRouter 访问

## 安装方法

### 1. 获取 OpenRouter API Key

1. 访问 [OpenRouter](https://openrouter.ai/keys)
2. 注册账号并获取 API Key
3. 确保账户有足够的余额

### 2. 安装插件

1. 下载或克隆此项目
2. 打开 Chrome 浏览器
3. 进入 `chrome://extensions/`
4. 开启"开发者模式"
5. 点击"加载已解压的扩展程序"
6. 选择 `new_plug` 文件夹

### 3. 配置 API Key

1. 点击插件图标打开弹窗
2. 输入您的 OpenRouter API Key
3. 点击保存

## 使用方法

### 快捷键

- `Ctrl+K`: 打开对话窗口
- `Ctrl+J`: 启动探索模式

### 基本功能

1. **开始对话**: 点击插件图标 → 开始对话
2. **探索文本**: 选择网页文本 → 点击探索按钮
3. **截图分析**: 点击插件图标 → 截图分析
4. **页面总结**: 点击插件图标 → 页面总结

### 探索模式

1. 使用快捷键 `Ctrl+J` 或点击"探索页面"
2. 选择网页上的文本，会出现快捷工具栏
3. 选择"解释"、"翻译"或"总结"
4. 或者直接点击页面进行截图分析

## 设置选项

- **AI 模型**: 选择使用的 AI 模型
- **窗口模式**: 侧边栏或浮窗模式
- **悬浮按钮**: 是否显示页面悬浮按钮
- **文字下划线**: 是否保留选中文字的下划线

## 文件结构

```
new_plug/
├── manifest.json          # 插件配置文件
├── config.js             # 配置常量
├── api-service.js        # OpenRouter API 服务
├── background.js         # 后台脚本
├── popup.html           # 弹窗页面
├── popup.js             # 弹窗脚本
├── sidepanel.html       # 侧边栏页面
├── sidepanel.js         # 侧边栏脚本
├── content-scripts/     # 内容脚本
│   ├── content.js       # 页面交互脚本
│   └── content.css      # 页面样式
├── styles/              # 样式文件
│   ├── popup.css        # 弹窗样式
│   └── sidepanel.css    # 侧边栏样式
├── icon/                # 图标文件
├── _locales/            # 国际化文件
│   ├── zh_CN/
│   └── en/
└── README.md
```

## 开发说明

### 主要技术栈

- **Manifest V3**: Chrome 扩展最新版本
- **OpenRouter API**: 统一的 AI 模型接口
- **Vanilla JavaScript**: 原生 JS，无框架依赖
- **CSS3**: 现代 CSS 特性，支持深色模式

### API 集成

插件使用 OpenRouter API 来访问各种 AI 模型：

```javascript
fetch("https://openrouter.ai/api/v1/chat/completions", {
  method: "POST",
  headers: {
    "Authorization": "Bearer YOUR_API_KEY",
    "HTTP-Referer": "chrome-extension://ai-assistant",
    "X-Title": "AI Assistant Chrome Extension",
    "Content-Type": "application/json"
  },
  body: JSON.stringify({
    "model": "google/gemini-2.5-pro-preview",
    "messages": [
      {
        "role": "user",
        "content": "Your message here"
      }
    ]
  })
});
```

### 自定义配置

可以在 `config.js` 中修改：

- API 端点
- 默认模型
- UI 文本
- 存储键名

## 注意事项

1. **API 费用**: OpenRouter 按使用量收费，请注意控制成本
2. **网络要求**: 需要稳定的网络连接访问 OpenRouter API
3. **隐私保护**: 插件不会存储您的对话内容，仅保存设置信息
4. **浏览器兼容**: 目前仅支持 Chrome 和基于 Chromium 的浏览器

## 故障排除

### 常见问题

1. **API Key 无效**: 检查 API Key 是否正确，账户是否有余额
2. **网络错误**: 检查网络连接，确认可以访问 OpenRouter
3. **功能无响应**: 刷新页面或重启浏览器
4. **快捷键冲突**: 在 `chrome://extensions/shortcuts` 中修改快捷键

### 调试方法

1. 打开开发者工具 (F12)
2. 查看 Console 面板的错误信息
3. 检查 Network 面板的 API 请求
4. 在插件管理页面查看错误日志

## 更新日志

### v1.0.0

- 初始版本发布
- 支持基本对话功能
- 实现文本探索和截图分析
- 添加侧边栏和弹窗模式
- 支持多种 AI 模型

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 相关链接

- [OpenRouter](https://openrouter.ai/)
- [Chrome Extension 开发文档](https://developer.chrome.com/docs/extensions/)
- [Manifest V3 指南](https://developer.chrome.com/docs/extensions/mv3/)
