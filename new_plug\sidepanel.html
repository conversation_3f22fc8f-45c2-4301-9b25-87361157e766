<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Assistant</title>
    <link rel="stylesheet" href="styles/sidepanel.css">
</head>
<body>
    <div id="sidepanel-container">
        <!-- Header -->
        <div class="sidepanel-header">
            <div class="header-title">
                <img src="icon/32.png" alt="AI Assistant" class="header-icon">
                <span class="header-text">AI Assistant</span>
            </div>
            <div class="header-actions">
                <button id="history-btn" class="icon-btn" title="历史记录">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M13,3A9,9 0 0,0 4,12H1L4.89,15.89L4.96,16.03L9,12H6A7,7 0 0,1 13,5A7,7 0 0,1 20,12A7,7 0 0,1 13,19C11.07,19 9.32,18.21 8.06,16.94L6.64,18.36C8.27,20 10.5,21 13,21A9,9 0 0,0 22,12A9,9 0 0,0 13,3Z"/>
                    </svg>
                </button>
                <button id="new-chat-btn" class="icon-btn" title="新对话">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                    </svg>
                </button>
                <button id="settings-btn" class="icon-btn" title="设置">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z"/>
                    </svg>
                </button>
            </div>
        </div>

        <!-- API Setup (shown when not configured) -->
        <div id="api-setup" class="setup-section" style="display: none;">
            <div class="setup-card">
                <div class="setup-icon">🔑</div>
                <h3>设置 API Key</h3>
                <p>请输入您的 OpenRouter API Key 以开始使用</p>
                <div class="input-group">
                    <input type="password" id="api-key-input" placeholder="输入 API Key" class="api-input">
                    <button id="save-api-key" class="btn btn-primary">保存</button>
                </div>
                <a href="https://openrouter.ai/keys" target="_blank" class="help-link">
                    获取 API Key →
                </a>
            </div>
        </div>

        <!-- Chat Container -->
        <div id="chat-container" class="chat-container">
            <!-- Chat Messages -->
            <div id="chat-messages" class="chat-messages">
                <!-- Welcome message -->
                <div class="message-group welcome-message">
                    <div class="message assistant-message">
                        <div class="message-content">
                            <p>👋 你好！我是 AI 助手，可以帮你：</p>
                            <ul>
                                <li>回答问题和解释概念</li>
                                <li>分析网页内容</li>
                                <li>解释截图内容</li>
                                <li>总结文章要点</li>
                            </ul>
                            <p>有什么可以帮助你的吗？</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div id="quick-actions" class="quick-actions">
                <button class="quick-action-btn" data-action="screenshot">
                    <span class="action-icon">📸</span>
                    <span class="action-text">截图分析</span>
                </button>
                <button class="quick-action-btn" data-action="summarize">
                    <span class="action-icon">📄</span>
                    <span class="action-text">页面总结</span>
                </button>
                <button class="quick-action-btn" data-action="explore">
                    <span class="action-icon">🔍</span>
                    <span class="action-text">探索模式</span>
                </button>
            </div>

            <!-- Input Area -->
            <div id="input-area" class="input-area">
                <div class="input-container">
                    <div class="input-wrapper">
                        <textarea 
                            id="message-input" 
                            placeholder="有什么问题尽管问我..." 
                            rows="1"
                            maxlength="2000"
                        ></textarea>
                        <div class="input-actions">
                            <button id="attach-btn" class="icon-btn" title="上传图片">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                                </svg>
                            </button>
                            <button id="send-btn" class="send-btn" title="发送">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M2,21L23,12L2,3V10L17,12L2,14V21Z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="input-footer">
                        <span class="char-count">0/2000</span>
                        <span class="shortcuts-hint">Ctrl+Enter 发送</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading indicator -->
        <div id="loading-indicator" class="loading-indicator" style="display: none;">
            <div class="loading-dots">
                <div class="dot"></div>
                <div class="dot"></div>
                <div class="dot"></div>
            </div>
            <span class="loading-text">AI 正在思考...</span>
        </div>

        <!-- History Panel -->
        <div id="history-panel" class="settings-panel" style="display: none;">
            <div class="settings-header">
                <h3>历史记录</h3>
                <button id="close-history" class="icon-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                    </svg>
                </button>
            </div>
            <div class="settings-content">
                <div id="history-list" class="history-list">
                    <!-- History items will be populated here -->
                </div>
                <div class="setting-actions">
                    <button id="export-history" class="btn btn-secondary">导出历史</button>
                    <button id="clear-all-history" class="btn btn-secondary">清除全部</button>
                </div>
            </div>
        </div>

        <!-- Settings Panel -->
        <div id="settings-panel" class="settings-panel" style="display: none;">
            <div class="settings-header">
                <h3>设置</h3>
                <button id="close-settings" class="icon-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                    </svg>
                </button>
            </div>
            <div class="settings-content">
                <div class="setting-group">
                    <label for="model-select">AI 模型</label>
                    <select id="model-select" class="setting-select">
                        <option value="google/gemini-2.5-pro-preview">Gemini 2.5 Pro Preview</option>
                        <option value="anthropic/claude-3.5-sonnet">Claude 3.5 Sonnet</option>
                        <option value="openai/gpt-4o">GPT-4o</option>
                    </select>
                </div>

                <div class="setting-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="enable-float-button">
                        <span class="checkmark"></span>
                        显示悬浮按钮
                    </label>
                </div>

                <div class="setting-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="enable-underline">
                        <span class="checkmark"></span>
                        保留文字下划线
                    </label>
                </div>
                
                <div class="setting-actions">
                    <button id="save-settings" class="btn btn-primary">保存设置</button>
                    <button id="clear-history" class="btn btn-secondary">清除历史</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden file input for image upload -->
    <input type="file" id="file-input" accept="image/*" style="display: none;">

    <script src="config.js"></script>
    <script src="api-service.js"></script>
    <script src="sidepanel.js"></script>
</body>
</html>
