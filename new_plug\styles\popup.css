/* Popup styles for AI Assistant Chrome Extension */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  background: #fff;
  width: 380px;
  min-height: 500px;
}

#popup-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 600px;
}

/* Header */
.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e1e5e9;
  background: #f8f9fa;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  width: 24px;
  height: 24px;
}

.logo-text {
  font-weight: 600;
  font-size: 16px;
  color: #1a73e8;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.icon-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #5f6368;
  transition: background-color 0.2s;
}

.icon-btn:hover {
  background: #f1f3f4;
}

/* Main Content */
.popup-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.section {
  padding: 20px;
  border-bottom: 1px solid #e8eaed;
}

.section:last-child {
  border-bottom: none;
}

.section h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #202124;
}

/* API Setup */
.setup-card {
  text-align: center;
  padding: 24px 16px;
}

.setup-card h3 {
  color: #1a73e8;
  margin-bottom: 12px;
}

.setup-description {
  color: #5f6368;
  margin-bottom: 20px;
  line-height: 1.6;
}

.setup-description a {
  color: #1a73e8;
  text-decoration: none;
}

.setup-description a:hover {
  text-decoration: underline;
}

.input-group {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.api-input {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #dadce0;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.api-input:focus {
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}

/* Buttons */
.btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.btn-primary {
  background: #1a73e8;
  color: white;
}

.btn-primary:hover {
  background: #1557b0;
}

.btn-secondary {
  background: #f8f9fa;
  color: #3c4043;
  border: 1px solid #dadce0;
}

.btn-secondary:hover {
  background: #f1f3f4;
}

/* Quick Actions */
.action-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 1px solid #e8eaed;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
  text-align: left;
}

.action-btn:hover {
  border-color: #1a73e8;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.action-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 8px;
}

.action-text {
  flex: 1;
}

.action-title {
  font-weight: 500;
  color: #202124;
  margin-bottom: 2px;
}

.action-desc {
  font-size: 12px;
  color: #5f6368;
}

/* Settings */
.setting-item {
  margin-bottom: 16px;
}

.setting-item label {
  display: block;
  font-weight: 500;
  margin-bottom: 6px;
  color: #202124;
}

.setting-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #dadce0;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  outline: none;
  transition: border-color 0.2s;
}

.setting-select:focus {
  border-color: #1a73e8;
}

.checkbox-label {
  display: flex !important;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-weight: normal !important;
  margin-bottom: 0 !important;
}

.checkbox-label input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #1a73e8;
}

.setting-actions {
  display: flex;
  gap: 8px;
  margin-top: 20px;
}

.setting-actions .btn {
  flex: 1;
}

/* Shortcuts */
.shortcut-list {
  margin-bottom: 16px;
}

.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f1f3f4;
}

.shortcut-item:last-child {
  border-bottom: none;
}

.shortcut-key {
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  color: #5f6368;
}

.shortcut-desc {
  color: #202124;
}

.shortcut-link {
  color: #1a73e8;
  text-decoration: none;
  font-size: 13px;
}

.shortcut-link:hover {
  text-decoration: underline;
}

/* Footer */
.popup-footer {
  padding: 16px 20px;
  border-top: 1px solid #e8eaed;
  background: #f8f9fa;
}

.footer-links {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 12px;
  color: #5f6368;
}

.footer-links a {
  color: #1a73e8;
  text-decoration: none;
}

.footer-links a:hover {
  text-decoration: underline;
}

.separator {
  color: #dadce0;
}

.version {
  color: #9aa0a6;
}

/* Loading Overlay */
#loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1a73e8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.loading-text {
  color: #5f6368;
  font-size: 14px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 400px) {
  body {
    width: 320px;
  }
  
  .popup-header {
    padding: 12px 16px;
  }
  
  .section {
    padding: 16px;
  }
  
  .action-btn {
    padding: 12px;
  }
  
  .action-icon {
    width: 32px;
    height: 32px;
    font-size: 20px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  body {
    background: #202124;
    color: #e8eaed;
  }
  
  .popup-header {
    background: #2d2e30;
    border-bottom-color: #3c4043;
  }
  
  .section {
    border-bottom-color: #3c4043;
  }
  
  .section h3 {
    color: #e8eaed;
  }
  
  .action-btn {
    background: #2d2e30;
    border-color: #3c4043;
    color: #e8eaed;
  }
  
  .action-btn:hover {
    border-color: #8ab4f8;
  }
  
  .action-icon {
    background: #3c4043;
  }
  
  .action-title {
    color: #e8eaed;
  }
  
  .api-input,
  .setting-select {
    background: #2d2e30;
    border-color: #3c4043;
    color: #e8eaed;
  }
  
  .api-input:focus,
  .setting-select:focus {
    border-color: #8ab4f8;
  }
  
  .btn-secondary {
    background: #3c4043;
    color: #e8eaed;
    border-color: #5f6368;
  }
  
  .btn-secondary:hover {
    background: #48494a;
  }
  
  .popup-footer {
    background: #2d2e30;
    border-top-color: #3c4043;
  }
  
  .shortcut-key {
    background: #3c4043;
    color: #9aa0a6;
  }
  
  .shortcut-item {
    border-bottom-color: #3c4043;
  }
}
