// OpenRouter API Configuration
const CONFIG = {
  // OpenRouter API settings
  OPENROUTER_API_URL: "https://openrouter.ai/api/v1/chat/completions",
  OPENROUTER_API_KEY: "", // Will be set by user in options
  OPENROUTER_MODEL: "google/gemini-2.5-pro-preview",
  SITE_URL: "chrome-extension://ai-assistant",
  SITE_NAME: "AI Assistant Chrome Extension",
  
  // Default settings
  DEFAULT_SETTINGS: {
    apiKey: "",
    model: "google/gemini-2.5-pro-preview",
    temperature: 0.7,
    maxTokens: 4096,
    enableUnderline: true,
    enableMarkdown: true,
    exploreActions: [
      {
        title: "解释",
        prompt: "请解释以下文本的含义：\n\n\"{text}\""
      },
      {
        title: "翻译",
        prompt: "请将以下文本翻译成中文：\n\n\"{text}\""
      },
      {
        title: "总结",
        prompt: "请总结以下文本的要点：\n\n\"{text}\""
      }
    ]
  },
  
  // UI Messages
  MESSAGES: {
    THINKING: "思考中...",
    SEARCHING: "搜索中...",
    ERROR: "抱歉，AI助手暂时无法响应，请稍后再试。",
    PLACEHOLDER: "有什么问题尽管问我",
    SCREENSHOT_PROMPT: "解释当前屏幕",
    SUMMARY_PROMPT: "总结全文"
  },
  
  // Storage keys
  STORAGE_KEYS: {
    SETTINGS: "ai_assistant_settings",
    CHAT_HISTORY: "ai_assistant_chat_history",
    USER_PREFERENCES: "ai_assistant_preferences"
  }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CONFIG;
} else if (typeof window !== 'undefined') {
  window.CONFIG = CONFIG;
}
