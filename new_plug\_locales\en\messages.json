{"name": {"message": "AI Assistant"}, "desc": {"message": "Intelligent browser assistant powered by OpenRouter API, supporting chat, text analysis, screenshot explanation and more."}, "chat_actions_copy": {"message": "Copy"}, "chat_actions_dislike": {"message": "Dislike"}, "chat_actions_like": {"message": "Like"}, "chat_actions_retry": {"message": "Retry"}, "chat_canceled": {"message": "(Stopped)"}, "chat_error": {"message": "Sorry, the AI assistant is temporarily unavailable. Please try again later."}, "chat_placeholder": {"message": "Ask me anything"}, "chat_placeholder_image": {"message": "Explain the text content in the image"}, "chat_screenshot": {"message": "Explain current screen"}, "chat_searching": {"message": "Searching..."}, "chat_summary": {"message": "Summarize"}, "chat_thinking": {"message": "Thinking..."}, "chat_title": {"message": "Untitled Chat"}, "command_chat": {"message": "Cha<PERSON>"}, "command_explore": {"message": "Explore"}, "explore_asked": {"message": "$count$ people asked", "placeholders": {"count": {"content": "$1"}}}, "explore_prompt": {"message": "Explain"}, "ocr_error": {"message": "No text found in the area."}, "ocr_loading": {"message": "Reading text in the area..."}, "popup_chat_float_button": {"message": "AI Assistant <PERSON><PERSON>"}, "popup_chat_shortcut": {"message": "Chat Shortcut"}, "popup_chat_title": {"message": "Cha<PERSON>"}, "popup_chat_window_mode": {"message": "Window Display Mode"}, "popup_chat_window_mode_modal": {"message": "Global Modal"}, "popup_chat_window_mode_sidepanel": {"message": "Side Panel"}, "popup_explore_shortcut": {"message": "Explore Shortcut (supports text selection and screenshot)"}, "popup_explore_title": {"message": "Explore"}, "popup_explore_toolbar": {"message": "Show quick buttons after text selection"}, "popup_explore_underline": {"message": "Keep text underline"}, "popup_login": {"message": "Set API Key"}, "popup_logout": {"message": "Clear Settings"}, "prompt_pdf": {"message": "Answer based on this PDF content"}, "shortcut": {"message": "Not set"}, "shortcut_settings": {"message": "Set Shortcuts"}, "upload_error": {"message": "No text extracted"}, "upload_limit": {"message": "Up to $count$ images can be uploaded", "placeholders": {"count": {"content": "$1"}}}, "visit_openrouter": {"message": "<PERSON><PERSON>t OpenRouter"}, "api_key_required": {"message": "Please set OpenRouter API Key first"}, "api_key_saved": {"message": "API Key saved successfully"}, "settings_saved": {"message": "Setting<PERSON> saved successfully"}, "screenshot_analyzing": {"message": "Analyzing screenshot..."}, "page_summarizing": {"message": "Summarizing page..."}, "explore_mode_started": {"message": "Explore mode started, please select text or click to screenshot"}, "model_gemini": {"message": "Gemini 2.5 Pro Preview"}, "model_claude": {"message": "Claude 3.5 Sonnet"}, "model_gpt4": {"message": "GPT-4o"}}