{"action": {"default_title": "AI Assistant - 点击打开侧边栏"}, "background": {"service_worker": "background.js"}, "commands": {"chat": {"description": "Open chat dialog", "suggested_key": {"default": "Ctrl+K"}}, "explore": {"description": "Explore selected content", "suggested_key": {"default": "Ctrl+J"}}}, "content_scripts": [{"js": ["content-scripts/content.js"], "matches": ["<all_urls>"]}], "default_locale": "zh_CN", "description": "AI Assistant powered by OpenRouter API", "homepage_url": "https://openrouter.ai", "icons": {"128": "icon/128.png", "16": "icon/16.png", "32": "icon/32.png", "48": "icon/48.png", "96": "icon/96.png"}, "manifest_version": 3, "name": "AI Assistant", "omnibox": {"keyword": "ai"}, "permissions": ["activeTab", "storage", "tabs", "sidePanel"], "side_panel": {"default_path": "sidepanel.html"}, "version": "1.0.0", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["content-scripts/content.css"]}]}