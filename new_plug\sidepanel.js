// Sidepanel script for AI Assistant Chrome Extension

let chatHistory = [];
let isLoading = false;

document.addEventListener('DOMContentLoaded', async () => {
  await initializeSidepanel();
  setupEventListeners();
  loadChatHistory();
});

// Initialize sidepanel
async function initializeSidepanel() {
  try {
    const settings = await getSettings();
    
    // Check if API key is configured
    if (!settings.apiKey) {
      showApiSetup();
    } else {
      showChatContainer();
    }
    
    // Update settings UI
    updateSettingsUI(settings);
    
  } catch (error) {
    console.error('Error initializing sidepanel:', error);
    showError('初始化失败，请刷新重试');
  }
}

// Setup event listeners
function setupEventListeners() {
  // Header actions
  document.getElementById('new-chat-btn').addEventListener('click', startNewChat);
  document.getElementById('settings-btn').addEventListener('click', toggleSettings);
  
  // API setup
  document.getElementById('save-api-key').addEventListener('click', saveApiKey);
  document.getElementById('api-key-input').addEventListener('keypress', (e) => {
    if (e.key === 'Enter') saveApiKey();
  });
  
  // Chat input
  const messageInput = document.getElementById('message-input');
  messageInput.addEventListener('input', handleInputChange);
  messageInput.addEventListener('keydown', handleKeyDown);
  
  // Send button
  document.getElementById('send-btn').addEventListener('click', sendMessage);
  
  // Quick actions
  document.querySelectorAll('.quick-action-btn').forEach(btn => {
    btn.addEventListener('click', handleQuickAction);
  });
  
  // File upload
  document.getElementById('attach-btn').addEventListener('click', () => {
    document.getElementById('file-input').click();
  });
  document.getElementById('file-input').addEventListener('change', handleFileUpload);
  
  // Settings
  document.getElementById('close-settings').addEventListener('click', () => {
    document.getElementById('settings-panel').style.display = 'none';
  });
  document.getElementById('save-settings').addEventListener('click', saveSettings);
  document.getElementById('clear-history').addEventListener('click', clearChatHistory);
}

// Show API setup
function showApiSetup() {
  document.getElementById('api-setup').style.display = 'block';
  document.getElementById('chat-container').style.display = 'none';
}

// Show chat container
function showChatContainer() {
  document.getElementById('api-setup').style.display = 'none';
  document.getElementById('chat-container').style.display = 'flex';
}

// Save API key
async function saveApiKey() {
  const apiKeyInput = document.getElementById('api-key-input');
  const apiKey = apiKeyInput.value.trim();
  
  if (!apiKey) {
    showError('请输入有效的 API Key');
    return;
  }
  
  try {
    const result = await updateSettings({ apiKey });
    
    if (result.success) {
      showChatContainer();
      showSuccess('API Key 保存成功');
      apiKeyInput.value = '';
    } else {
      showError(result.error || '保存失败');
    }
  } catch (error) {
    console.error('Error saving API key:', error);
    showError('保存失败，请重试');
  }
}

// Handle input change
function handleInputChange(e) {
  const input = e.target;
  const charCount = input.value.length;
  document.querySelector('.char-count').textContent = `${charCount}/2000`;
  
  // Auto-resize textarea
  input.style.height = 'auto';
  input.style.height = Math.min(input.scrollHeight, 120) + 'px';
  
  // Update send button state
  const sendBtn = document.getElementById('send-btn');
  sendBtn.disabled = !input.value.trim() || isLoading;
}

// Handle key down
function handleKeyDown(e) {
  if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
    e.preventDefault();
    sendMessage();
  }
}

// Send message
async function sendMessage() {
  const messageInput = document.getElementById('message-input');
  const message = messageInput.value.trim();
  
  if (!message || isLoading) return;
  
  // Add user message to chat
  addMessage('user', message);
  
  // Clear input
  messageInput.value = '';
  messageInput.style.height = 'auto';
  document.querySelector('.char-count').textContent = '0/2000';
  
  // Show loading
  showLoading();
  
  try {
    // Send to background script
    const response = await sendToBackground({
      action: 'sendChatMessage',
      data: {
        message: message,
        context: getRecentContext()
      }
    });
    
    if (response.success) {
      addMessage('assistant', response.data.content);
    } else {
      addMessage('assistant', response.error || CONFIG.MESSAGES.ERROR);
    }
  } catch (error) {
    console.error('Error sending message:', error);
    addMessage('assistant', CONFIG.MESSAGES.ERROR);
  } finally {
    hideLoading();
  }
}

// Handle quick actions
async function handleQuickAction(e) {
  const action = e.currentTarget.dataset.action;
  
  try {
    switch (action) {
      case 'screenshot':
        await takeScreenshot();
        break;
      case 'summarize':
        await summarizePage();
        break;
      case 'explore':
        await startExploreMode();
        break;
    }
  } catch (error) {
    console.error('Error handling quick action:', error);
    showError('操作失败，请重试');
  }
}

// Take screenshot
async function takeScreenshot() {
  showLoading('截图中...');
  
  try {
    const result = await sendToBackground({ action: 'takeScreenshot' });
    
    if (result.success) {
      addMessage('user', '📸 请分析这张截图');
      
      const response = await sendToBackground({
        action: 'analyzeScreenshot',
        data: {
          imageDataUrl: result.data.imageDataUrl,
          prompt: CONFIG.MESSAGES.SCREENSHOT_PROMPT
        }
      });
      
      if (response.success) {
        addMessage('assistant', response.data.content);
      } else {
        addMessage('assistant', response.error || '截图分析失败');
      }
    } else {
      showError(result.error || '截图失败');
    }
  } catch (error) {
    console.error('Error taking screenshot:', error);
    showError('截图失败，请重试');
  } finally {
    hideLoading();
  }
}

// Summarize page
async function summarizePage() {
  addMessage('user', '📄 请总结当前页面内容');
  showLoading();
  
  try {
    // Get page content from content script
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    const pageContent = await chrome.tabs.sendMessage(tab.id, { action: 'getPageContent' });
    
    if (pageContent) {
      const response = await sendToBackground({
        action: 'sendChatMessage',
        data: {
          message: `请总结以下网页内容：\n\n${pageContent}`,
          context: []
        }
      });
      
      if (response.success) {
        addMessage('assistant', response.data.content);
      } else {
        addMessage('assistant', response.error || '总结失败');
      }
    } else {
      addMessage('assistant', '无法获取页面内容，请确保页面已完全加载');
    }
  } catch (error) {
    console.error('Error summarizing page:', error);
    addMessage('assistant', '总结失败，请重试');
  } finally {
    hideLoading();
  }
}

// Start explore mode
async function startExploreMode() {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    await chrome.tabs.sendMessage(tab.id, { action: 'startExploreMode' });
    addMessage('assistant', '🔍 探索模式已启动，请在页面上选择文本或截图进行分析');
  } catch (error) {
    console.error('Error starting explore mode:', error);
    showError('无法启动探索模式');
  }
}

// Handle file upload
function handleFileUpload(e) {
  const file = e.target.files[0];
  if (!file) return;
  
  if (!file.type.startsWith('image/')) {
    showError('请选择图片文件');
    return;
  }
  
  const reader = new FileReader();
  reader.onload = async (event) => {
    const imageDataUrl = event.target.result;
    
    addMessage('user', '🖼️ 请分析这张图片');
    showLoading();
    
    try {
      const response = await sendToBackground({
        action: 'analyzeScreenshot',
        data: {
          imageDataUrl: imageDataUrl,
          prompt: '请分析这张图片的内容'
        }
      });
      
      if (response.success) {
        addMessage('assistant', response.data.content);
      } else {
        addMessage('assistant', response.error || '图片分析失败');
      }
    } catch (error) {
      console.error('Error analyzing image:', error);
      addMessage('assistant', '图片分析失败，请重试');
    } finally {
      hideLoading();
    }
  };
  
  reader.readAsDataURL(file);
  e.target.value = ''; // Reset file input
}

// Add message to chat
function addMessage(role, content) {
  const messagesContainer = document.getElementById('chat-messages');
  const messageGroup = document.createElement('div');
  messageGroup.className = 'message-group';
  
  const message = document.createElement('div');
  message.className = `message ${role}-message`;
  
  const messageContent = document.createElement('div');
  messageContent.className = 'message-content';
  messageContent.textContent = content;
  
  message.appendChild(messageContent);
  messageGroup.appendChild(message);
  messagesContainer.appendChild(messageGroup);
  
  // Scroll to bottom
  messagesContainer.scrollTop = messagesContainer.scrollHeight;
  
  // Save to history
  chatHistory.push({ role, content, timestamp: Date.now() });
  saveChatHistory();
}

// Get recent context for API calls
function getRecentContext() {
  return chatHistory.slice(-10).map(msg => ({
    role: msg.role,
    content: msg.content
  }));
}

// Start new chat
function startNewChat() {
  if (confirm('确定要开始新对话吗？当前对话将被清除。')) {
    chatHistory = [];
    document.getElementById('chat-messages').innerHTML = `
      <div class="message-group welcome-message">
        <div class="message assistant-message">
          <div class="message-content">
            <p>👋 你好！我是 AI 助手，有什么可以帮助你的吗？</p>
          </div>
        </div>
      </div>
    `;
    saveChatHistory();
  }
}

// Toggle settings
function toggleSettings() {
  const settingsPanel = document.getElementById('settings-panel');
  const isVisible = settingsPanel.style.display !== 'none';
  settingsPanel.style.display = isVisible ? 'none' : 'block';
}

// Save settings
async function saveSettings() {
  try {
    const settings = {
      model: document.getElementById('model-select').value,
      enableFloatButton: document.getElementById('enable-float-button').checked,
      enableUnderline: document.getElementById('enable-underline').checked
    };
    
    const result = await updateSettings(settings);
    
    if (result.success) {
      showSuccess('设置保存成功');
      document.getElementById('settings-panel').style.display = 'none';
    } else {
      showError(result.error || '保存失败');
    }
  } catch (error) {
    console.error('Error saving settings:', error);
    showError('保存失败，请重试');
  }
}

// Clear chat history
function clearChatHistory() {
  if (confirm('确定要清除所有聊天记录吗？')) {
    startNewChat();
    showSuccess('聊天记录已清除');
  }
}

// Update settings UI
function updateSettingsUI(settings) {
  document.getElementById('model-select').value = settings.model || CONFIG.DEFAULT_SETTINGS.model;
  document.getElementById('enable-float-button').checked = settings.enableFloatButton !== false;
  document.getElementById('enable-underline').checked = settings.enableUnderline !== false;
}

// Load chat history
async function loadChatHistory() {
  try {
    const result = await chrome.storage.local.get([CONFIG.STORAGE_KEYS.CHAT_HISTORY]);
    const history = result[CONFIG.STORAGE_KEYS.CHAT_HISTORY] || [];
    
    if (history.length > 0) {
      chatHistory = history;
      const messagesContainer = document.getElementById('chat-messages');
      messagesContainer.innerHTML = '';
      
      history.forEach(msg => {
        addMessageToUI(msg.role, msg.content);
      });
    }
  } catch (error) {
    console.error('Error loading chat history:', error);
  }
}

// Save chat history
async function saveChatHistory() {
  try {
    await chrome.storage.local.set({
      [CONFIG.STORAGE_KEYS.CHAT_HISTORY]: chatHistory
    });
  } catch (error) {
    console.error('Error saving chat history:', error);
  }
}

// Add message to UI only (without saving to history)
function addMessageToUI(role, content) {
  const messagesContainer = document.getElementById('chat-messages');
  const messageGroup = document.createElement('div');
  messageGroup.className = 'message-group';
  
  const message = document.createElement('div');
  message.className = `message ${role}-message`;
  
  const messageContent = document.createElement('div');
  messageContent.className = 'message-content';
  messageContent.textContent = content;
  
  message.appendChild(messageContent);
  messageGroup.appendChild(message);
  messagesContainer.appendChild(messageGroup);
  
  // Scroll to bottom
  messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Show/hide loading
function showLoading(text = 'AI 正在思考...') {
  isLoading = true;
  const loadingIndicator = document.getElementById('loading-indicator');
  const loadingText = loadingIndicator.querySelector('.loading-text');
  loadingText.textContent = text;
  loadingIndicator.style.display = 'flex';
  
  // Disable send button
  document.getElementById('send-btn').disabled = true;
}

function hideLoading() {
  isLoading = false;
  document.getElementById('loading-indicator').style.display = 'none';
  
  // Enable send button if input has content
  const messageInput = document.getElementById('message-input');
  document.getElementById('send-btn').disabled = !messageInput.value.trim();
}

// Utility functions
async function getSettings() {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ action: 'getSettings' }, resolve);
  });
}

async function updateSettings(settings) {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ action: 'updateSettings', data: settings }, resolve);
  });
}

async function sendToBackground(message) {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage(message, resolve);
  });
}

function showError(message) {
  // Simple error display - could be enhanced with a proper notification system
  console.error('Error:', message);
  // You could implement a toast notification here
}

function showSuccess(message) {
  // Simple success display - could be enhanced with a proper notification system
  console.log('Success:', message);
  // You could implement a toast notification here
}
