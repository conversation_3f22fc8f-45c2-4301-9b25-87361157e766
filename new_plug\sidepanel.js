// Sidepanel script for AI Assistant Chrome Extension

let chatHistory = [];
let isLoading = false;

document.addEventListener('DOMContentLoaded', async () => {
  await initializeSidepanel();
  setupEventListeners();
  setupMessageListener();
  loadChatHistory();
});

// Initialize sidepanel
async function initializeSidepanel() {
  try {
    const settings = await getSettings();
    
    // Check if API key is configured
    if (!settings.apiKey) {
      showApiSetup();
    } else {
      showChatContainer();
    }
    
    // Update settings UI
    updateSettingsUI(settings);
    
  } catch (error) {
    console.error('Error initializing sidepanel:', error);
    showError('初始化失败，请刷新重试');
  }
}

// Setup message listener for background script communication
function setupMessageListener() {
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.action === 'displayExploreAction') {
      handleDisplayExploreAction(message.data);
    }
  });
}

// Setup event listeners
function setupEventListeners() {
  // Header actions
  document.getElementById('history-btn').addEventListener('click', toggleHistory);
  document.getElementById('new-chat-btn').addEventListener('click', startNewChat);
  document.getElementById('settings-btn').addEventListener('click', toggleSettings);

  // API setup - check if elements exist
  const saveApiKeyBtn = document.getElementById('save-api-key');
  const apiKeyInput = document.getElementById('api-key-input');

  if (saveApiKeyBtn) {
    console.log('Setting up save-api-key event listener');
    saveApiKeyBtn.addEventListener('click', saveApiKey);
  } else {
    console.error('save-api-key button not found');
  }

  if (apiKeyInput) {
    apiKeyInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') saveApiKey();
    });
  } else {
    console.error('api-key-input not found');
  }
  
  // Chat input
  const messageInput = document.getElementById('message-input');
  messageInput.addEventListener('input', handleInputChange);
  messageInput.addEventListener('keydown', handleKeyDown);
  
  // Send button
  document.getElementById('send-btn').addEventListener('click', sendMessage);
  
  // Quick actions
  document.querySelectorAll('.quick-action-btn').forEach(btn => {
    btn.addEventListener('click', handleQuickAction);
  });
  
  // File upload
  document.getElementById('attach-btn').addEventListener('click', () => {
    document.getElementById('file-input').click();
  });
  document.getElementById('file-input').addEventListener('change', handleFileUpload);
  
  // History
  document.getElementById('close-history').addEventListener('click', () => {
    document.getElementById('history-panel').style.display = 'none';
  });
  document.getElementById('export-history').addEventListener('click', exportHistory);
  document.getElementById('clear-all-history').addEventListener('click', clearAllHistory);

  // Settings
  document.getElementById('close-settings').addEventListener('click', () => {
    document.getElementById('settings-panel').style.display = 'none';
  });
  document.getElementById('update-api-key').addEventListener('click', updateApiKey);
  document.getElementById('save-settings').addEventListener('click', saveSettings);
  document.getElementById('clear-history').addEventListener('click', clearChatHistory);

  // API Key visibility toggles
  document.getElementById('toggle-api-key-visibility').addEventListener('click', () => {
    togglePasswordVisibility('api-key-setting');
  });
  document.getElementById('toggle-initial-api-key-visibility').addEventListener('click', () => {
    togglePasswordVisibility('api-key-input');
  });

  // Model suggestions
  document.querySelectorAll('.model-suggestion').forEach(btn => {
    btn.addEventListener('click', (e) => {
      const model = e.target.dataset.model;
      document.getElementById('model-input').value = model;
    });
  });

  // Explore actions config
  document.getElementById('manage-explore-actions').addEventListener('click', openExploreActionsModal);
  document.getElementById('close-explore-modal').addEventListener('click', closeExploreActionsModal);
  document.getElementById('cancel-explore-actions').addEventListener('click', closeExploreActionsModal);
  document.getElementById('save-explore-actions').addEventListener('click', saveExploreActionsFromModal);
  document.getElementById('add-action-row').addEventListener('click', addActionRow);
  document.getElementById('reset-default-actions').addEventListener('click', resetDefaultActions);
}

// Show API setup
function showApiSetup() {
  document.getElementById('api-setup').style.display = 'block';
  document.getElementById('chat-container').style.display = 'none';
}

// Show chat container
function showChatContainer() {
  document.getElementById('api-setup').style.display = 'none';
  document.getElementById('chat-container').style.display = 'flex';
}

// Save API key
async function saveApiKey() {
  console.log('saveApiKey function called');

  const apiKeyInput = document.getElementById('api-key-input');
  if (!apiKeyInput) {
    console.error('api-key-input element not found');
    showError('页面元素未找到，请刷新页面重试');
    return;
  }

  const apiKey = apiKeyInput.value.trim();
  console.log('API Key length:', apiKey.length);

  if (!apiKey) {
    showError('请输入有效的 API Key');
    return;
  }

  // Validate API key format
  if (!/^[\x00-\x7F]+$/.test(apiKey)) {
    showError('API Key 包含无效字符，请确保只包含英文字母、数字和符号');
    return;
  }

  if (!apiKey.startsWith('sk-or-')) {
    showError('请输入有效的 OpenRouter API Key（应以 sk-or- 开头）');
    return;
  }

  try {
    const result = await updateSettings({ apiKey });

    if (result.success) {
      showChatContainer();
      showSuccess('API Key 保存成功');
      apiKeyInput.value = '';
    } else {
      showError(result.error || '保存失败');
    }
  } catch (error) {
    console.error('Error saving API key:', error);
    showError('保存失败，请重试');
  }
}

// Handle input change
function handleInputChange(e) {
  const input = e.target;
  const charCount = input.value.length;
  document.querySelector('.char-count').textContent = `${charCount}/2000`;
  
  // Auto-resize textarea
  input.style.height = 'auto';
  input.style.height = Math.min(input.scrollHeight, 120) + 'px';
  
  // Update send button state
  const sendBtn = document.getElementById('send-btn');
  sendBtn.disabled = !input.value.trim() || isLoading;
}

// Handle key down
function handleKeyDown(e) {
  if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
    e.preventDefault();
    sendMessage();
  }
}

// Send message
async function sendMessage() {
  const messageInput = document.getElementById('message-input');
  const message = messageInput.value.trim();
  
  if (!message || isLoading) return;
  
  // Add user message to chat
  addMessage('user', message);
  
  // Clear input
  messageInput.value = '';
  messageInput.style.height = 'auto';
  document.querySelector('.char-count').textContent = '0/2000';
  
  // Show loading
  showLoading();
  
  try {
    // Send to background script
    const response = await sendToBackground({
      action: 'sendChatMessage',
      data: {
        message: message,
        context: getRecentContext()
      }
    });
    
    if (response.success) {
      addMessage('assistant', response.data.content);
    } else {
      addMessage('assistant', response.error || CONFIG.MESSAGES.ERROR);
    }
  } catch (error) {
    console.error('Error sending message:', error);
    addMessage('assistant', CONFIG.MESSAGES.ERROR);
  } finally {
    hideLoading();
  }
}

// Handle quick actions
async function handleQuickAction(e) {
  const action = e.currentTarget.dataset.action;
  
  try {
    switch (action) {
      case 'screenshot':
        await takeScreenshot();
        break;
      case 'summarize':
        await summarizePage();
        break;
      case 'explore':
        await startExploreMode();
        break;
    }
  } catch (error) {
    console.error('Error handling quick action:', error);
    showError('操作失败，请重试');
  }
}

// Take screenshot
async function takeScreenshot() {
  showLoading('截图中...');

  try {
    const result = await sendToBackground({ action: 'takeScreenshot' });

    if (result.success) {
      // Add screenshot to chat with image preview
      addScreenshotMessage(result.data.imageDataUrl);

      const response = await sendToBackground({
        action: 'analyzeScreenshot',
        data: {
          imageDataUrl: result.data.imageDataUrl,
          prompt: CONFIG.MESSAGES.SCREENSHOT_PROMPT
        }
      });

      if (response.success) {
        addMessage('assistant', response.data.content);
      } else {
        addMessage('assistant', response.error || '截图分析失败');
      }
    } else {
      showError(result.error || '截图失败');
    }
  } catch (error) {
    console.error('Error taking screenshot:', error);
    showError('截图失败，请重试');
  } finally {
    hideLoading();
  }
}

// Add screenshot message with image preview
function addScreenshotMessage(imageDataUrl) {
  const messagesContainer = document.getElementById('chat-messages');
  const messageGroup = document.createElement('div');
  messageGroup.className = 'message-group';

  const message = document.createElement('div');
  message.className = 'message user-message';

  const messageContent = document.createElement('div');
  messageContent.className = 'message-content';

  const imagePreview = document.createElement('img');
  imagePreview.src = imageDataUrl;
  imagePreview.className = 'screenshot-preview';
  imagePreview.style.cssText = `
    max-width: 200px;
    max-height: 150px;
    border-radius: 8px;
    margin-bottom: 8px;
    cursor: pointer;
  `;

  const text = document.createElement('div');
  text.textContent = '📸 请分析这张截图';

  messageContent.appendChild(imagePreview);
  messageContent.appendChild(text);

  // Click to view full size
  imagePreview.addEventListener('click', () => {
    const modal = document.createElement('div');
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      cursor: pointer;
    `;

    const fullImage = document.createElement('img');
    fullImage.src = imageDataUrl;
    fullImage.style.cssText = `
      max-width: 90%;
      max-height: 90%;
      border-radius: 8px;
    `;

    modal.appendChild(fullImage);
    document.body.appendChild(modal);

    modal.addEventListener('click', () => {
      document.body.removeChild(modal);
    });
  });

  message.appendChild(messageContent);
  messageGroup.appendChild(message);
  messagesContainer.appendChild(messageGroup);

  // Scroll to bottom
  messagesContainer.scrollTop = messagesContainer.scrollHeight;

  // Save to history
  chatHistory.push({
    role: 'user',
    content: '📸 请分析这张截图',
    imageDataUrl: imageDataUrl,
    timestamp: Date.now()
  });
  saveChatHistory();
}

// Summarize page
async function summarizePage() {
  addMessage('user', '📄 请总结当前页面内容');
  showLoading();
  
  try {
    // Get page content from content script
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    const pageContent = await chrome.tabs.sendMessage(tab.id, { action: 'getPageContent' });
    
    if (pageContent) {
      const response = await sendToBackground({
        action: 'sendChatMessage',
        data: {
          message: `请总结以下网页内容：\n\n${pageContent}`,
          context: []
        }
      });
      
      if (response.success) {
        addMessage('assistant', response.data.content);
      } else {
        addMessage('assistant', response.error || '总结失败');
      }
    } else {
      addMessage('assistant', '无法获取页面内容，请确保页面已完全加载');
    }
  } catch (error) {
    console.error('Error summarizing page:', error);
    addMessage('assistant', '总结失败，请重试');
  } finally {
    hideLoading();
  }
}

// Start explore mode
async function startExploreMode() {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    await chrome.tabs.sendMessage(tab.id, { action: 'startExploreMode' });
    addMessage('assistant', '🔍 探索模式已启动，请在页面上选择文本或截图进行分析');
  } catch (error) {
    console.error('Error starting explore mode:', error);
    showError('无法启动探索模式');
  }
}

// Handle file upload
function handleFileUpload(e) {
  const file = e.target.files[0];
  if (!file) return;

  if (!file.type.startsWith('image/')) {
    showError('请选择图片文件');
    return;
  }

  const reader = new FileReader();
  reader.onload = async (event) => {
    const imageDataUrl = event.target.result;

    addMessage('user', '🖼️ 请分析这张图片');
    showLoading();

    try {
      const response = await sendToBackground({
        action: 'analyzeScreenshot',
        data: {
          imageDataUrl: imageDataUrl,
          prompt: '请分析这张图片的内容'
        }
      });

      if (response.success) {
        addMessage('assistant', response.data.content);
      } else {
        addMessage('assistant', response.error || '图片分析失败');
      }
    } catch (error) {
      console.error('Error analyzing image:', error);
      addMessage('assistant', '图片分析失败，请重试');
    } finally {
      hideLoading();
    }
  };

  reader.readAsDataURL(file);
  e.target.value = ''; // Reset file input
}

// Handle display explore action
async function handleDisplayExploreAction(data) {
  const { action, selectedText, prompt } = data;

  // Add user message showing the action and selected text
  addExploreUserMessage(action, selectedText);

  // Show loading
  showLoading();

  try {
    // Send to AI
    const response = await sendToBackground({
      action: 'sendChatMessage',
      data: {
        message: prompt,
        context: getRecentContext()
      }
    });

    if (response.success) {
      addMessage('assistant', response.data.content);
    } else {
      addMessage('assistant', response.error || '处理失败');
    }
  } catch (error) {
    console.error('Error processing explore action:', error);
    addMessage('assistant', '处理失败，请重试');
  } finally {
    hideLoading();
  }
}

// Add explore user message with action context
function addExploreUserMessage(action, selectedText) {
  const messagesContainer = document.getElementById('chat-messages');
  const messageGroup = document.createElement('div');
  messageGroup.className = 'message-group';

  const message = document.createElement('div');
  message.className = 'message user-message';

  const messageContent = document.createElement('div');
  messageContent.className = 'message-content';

  // Create action badge
  const actionBadge = document.createElement('span');
  actionBadge.className = 'action-badge';
  actionBadge.textContent = action;
  actionBadge.style.cssText = `
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    margin-bottom: 8px;
    font-weight: 500;
  `;

  // Create selected text display
  const textDisplay = document.createElement('div');
  textDisplay.className = 'selected-text-display';
  textDisplay.style.cssText = `
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    border-radius: 8px;
    border-left: 3px solid rgba(255, 255, 255, 0.3);
    font-style: italic;
    margin-top: 4px;
  `;
  textDisplay.textContent = `"${selectedText}"`;

  messageContent.appendChild(actionBadge);
  messageContent.appendChild(textDisplay);

  message.appendChild(messageContent);
  messageGroup.appendChild(message);
  messagesContainer.appendChild(messageGroup);

  // Scroll to bottom
  messagesContainer.scrollTop = messagesContainer.scrollHeight;

  // Save to history
  chatHistory.push({
    role: 'user',
    content: `${action}: "${selectedText}"`,
    timestamp: Date.now()
  });
  saveChatHistory();
}

// Simple markdown parser
function parseMarkdown(text) {
  return text
    // Headers
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    // Code blocks (must be before other formatting)
    .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
    // Inline code (must be before other formatting)
    .replace(/`([^`]+)`/g, '<code>$1</code>')
    // Bold (must be before italic)
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/__(.*?)__/g, '<strong>$1</strong>')
    // Italic (more restrictive pattern to avoid a_b_c issues)
    .replace(/\*([^*\s][^*]*[^*\s])\*/g, '<em>$1</em>')
    .replace(/\b_([^_\s][^_]*[^_\s])_\b/g, '<em>$1</em>')
    // Links
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')
    // Line breaks
    .replace(/\n\n/g, '</p><p>')
    .replace(/\n/g, '<br>')
    // Lists
    .replace(/^\* (.*$)/gim, '<li>$1</li>')
    .replace(/^- (.*$)/gim, '<li>$1</li>')
    .replace(/^(\d+)\. (.*$)/gim, '<li>$2</li>');
}

// Add message to chat
async function addMessage(role, content) {
  const messagesContainer = document.getElementById('chat-messages');
  const messageGroup = document.createElement('div');
  messageGroup.className = 'message-group';

  const message = document.createElement('div');
  message.className = `message ${role}-message`;

  const messageContent = document.createElement('div');
  messageContent.className = 'message-content';

  if (role === 'assistant') {
    // Check if markdown is enabled
    const settings = await getSettings();
    const enableMarkdown = settings.enableMarkdown !== false;

    if (enableMarkdown) {
      // Parse markdown for assistant messages
      const parsedContent = parseMarkdown(content);
      messageContent.innerHTML = `<p>${parsedContent}</p>`;
    } else {
      // Plain text with line breaks
      messageContent.innerHTML = content.replace(/\n/g, '<br>');
    }
  } else {
    // Plain text for user messages
    messageContent.textContent = content;
  }

  message.appendChild(messageContent);
  messageGroup.appendChild(message);
  messagesContainer.appendChild(messageGroup);

  // Scroll to bottom
  messagesContainer.scrollTop = messagesContainer.scrollHeight;

  // Save to history
  chatHistory.push({ role, content, timestamp: Date.now() });
  saveChatHistory();
}

// Get recent context for API calls
function getRecentContext() {
  return chatHistory.slice(-10).map(msg => ({
    role: msg.role,
    content: msg.content
  }));
}

// Start new chat
function startNewChat() {
  if (confirm('确定要开始新对话吗？当前对话将被清除。')) {
    chatHistory = [];
    document.getElementById('chat-messages').innerHTML = `
      <div class="message-group welcome-message">
        <div class="message assistant-message">
          <div class="message-content">
            <p>👋 你好！我是 AI 助手，有什么可以帮助你的吗？</p>
          </div>
        </div>
      </div>
    `;
    saveChatHistory();
  }
}

// Toggle history
function toggleHistory() {
  const historyPanel = document.getElementById('history-panel');
  const settingsPanel = document.getElementById('settings-panel');

  // Close settings if open
  settingsPanel.style.display = 'none';

  const isVisible = historyPanel.style.display !== 'none';
  historyPanel.style.display = isVisible ? 'none' : 'block';

  if (!isVisible) {
    loadHistoryList();
  }
}

// Toggle settings
function toggleSettings() {
  const settingsPanel = document.getElementById('settings-panel');
  const historyPanel = document.getElementById('history-panel');

  // Close history if open
  historyPanel.style.display = 'none';

  const isVisible = settingsPanel.style.display !== 'none';
  settingsPanel.style.display = isVisible ? 'none' : 'block';
}

// Update API Key
async function updateApiKey() {
  const apiKeyInput = document.getElementById('api-key-setting');
  const apiKey = apiKeyInput.value.trim();

  if (!apiKey) {
    showError('请输入有效的 API Key');
    return;
  }

  // Validate API key format
  if (!/^[\x00-\x7F]+$/.test(apiKey)) {
    showError('API Key 包含无效字符，请确保只包含英文字母、数字和符号');
    return;
  }

  if (!apiKey.startsWith('sk-or-')) {
    showError('请输入有效的 OpenRouter API Key（应以 sk-or- 开头）');
    return;
  }

  try {
    const result = await updateSettings({ apiKey });

    if (result.success) {
      showSuccess('API Key 更新成功');
      apiKeyInput.value = '';
    } else {
      showError(result.error || 'API Key 更新失败');
    }
  } catch (error) {
    console.error('Error updating API key:', error);
    showError('API Key 更新失败，请重试');
  }
}

// Save settings
async function saveSettings() {
  try {
    const settings = {
      model: document.getElementById('model-input').value.trim(),
      enableUnderline: document.getElementById('enable-underline').checked,
      enableMarkdown: document.getElementById('enable-markdown').checked
    };

    if (!settings.model) {
      showError('请输入有效的模型名称');
      return;
    }

    const result = await updateSettings(settings);

    if (result.success) {
      showSuccess('设置保存成功');
      document.getElementById('settings-panel').style.display = 'none';
    } else {
      showError(result.error || '保存失败');
    }
  } catch (error) {
    console.error('Error saving settings:', error);
    showError('保存失败，请重试');
  }
}

// Open explore actions modal
async function openExploreActionsModal() {
  const settings = await getSettings();
  const exploreActions = settings.exploreActions || CONFIG.DEFAULT_SETTINGS.exploreActions;

  loadExploreActionsTable(exploreActions);
  document.getElementById('explore-actions-modal').style.display = 'flex';
}

// Close explore actions modal
function closeExploreActionsModal() {
  document.getElementById('explore-actions-modal').style.display = 'none';
}

// Load explore actions into table
function loadExploreActionsTable(actions) {
  const tbody = document.getElementById('explore-actions-tbody');
  tbody.innerHTML = '';

  actions.forEach((action, index) => {
    addActionRow(action.title, action.prompt);
  });
}

// Add action row to table
function addActionRow(title = '', prompt = '') {
  const tbody = document.getElementById('explore-actions-tbody');
  const row = document.createElement('tr');

  row.innerHTML = `
    <td>
      <input type="text" class="action-title-input" value="${title}" placeholder="按钮标题">
    </td>
    <td>
      <textarea class="action-prompt-input" placeholder="提示词（使用 {text} 表示选中的文本）">${prompt}</textarea>
    </td>
    <td>
      <button type="button" class="action-delete-btn" onclick="deleteActionRow(this)">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
        </svg>
      </button>
    </td>
  `;

  tbody.appendChild(row);
}

// Delete action row (global function for onclick)
window.deleteActionRow = function(button) {
  const row = button.closest('tr');
  row.remove();
};

// Reset to default actions
function resetDefaultActions() {
  loadExploreActionsTable(CONFIG.DEFAULT_SETTINGS.exploreActions);
}

// Save explore actions from modal
async function saveExploreActionsFromModal() {
  const actions = getExploreActionsFromTable();

  if (actions.length === 0) {
    showError('至少需要一个探索动作');
    return;
  }

  try {
    const settings = await getSettings();
    settings.exploreActions = actions;

    const result = await updateSettings(settings);

    if (result.success) {
      updateExploreActionsSummary(actions);
      closeExploreActionsModal();
      showSuccess('探索动作配置已保存');
    } else {
      showError(result.error || '保存失败');
    }
  } catch (error) {
    console.error('Error saving explore actions:', error);
    showError('保存失败，请重试');
  }
}

// Get explore actions from table
function getExploreActionsFromTable() {
  const actions = [];
  const rows = document.querySelectorAll('#explore-actions-tbody tr');

  rows.forEach(row => {
    const title = row.querySelector('.action-title-input').value.trim();
    const prompt = row.querySelector('.action-prompt-input').value.trim();

    if (title && prompt) {
      actions.push({ title, prompt });
    }
  });

  return actions;
}

// Update explore actions summary
function updateExploreActionsSummary(actions) {
  const count = actions.length;
  document.getElementById('explore-actions-count').textContent = `${count} 个动作`;
}

// Clear chat history
function clearChatHistory() {
  if (confirm('确定要清除当前聊天记录吗？')) {
    startNewChat();
    showSuccess('当前聊天记录已清除');
  }
}

// Toggle password visibility
function togglePasswordVisibility(inputId) {
  const input = document.getElementById(inputId);
  const isPassword = input.type === 'password';

  input.type = isPassword ? 'text' : 'password';

  // Update icon
  const button = input.parentElement.querySelector('.toggle-visibility-btn');
  const svg = button.querySelector('svg path');

  if (isPassword) {
    // Show "eye-off" icon
    svg.setAttribute('d', 'M11.83,9L15,12.16C15,12.11 15,12.05 15,12A3,3 0 0,0 12,9C11.94,9 11.89,9 11.83,9M7.53,9.8L9.08,11.35C9.03,11.56 9,11.77 9,12A3,3 0 0,0 12,15C12.22,15 12.44,14.97 12.65,14.92L14.2,16.47C13.53,16.8 12.79,17 12,17A5,5 0 0,1 7,12C7,11.21 7.2,10.47 7.53,9.8M2,4.27L4.28,6.55L4.73,7C3.08,8.3 1.78,10 1,12C2.73,16.39 7,19.5 12,19.5C13.55,19.5 15.03,19.2 16.38,18.66L16.81,19.09L19.73,22L21,20.73L3.27,3M12,7A5,5 0 0,1 17,12C17,12.64 16.87,13.26 16.64,13.82L19.57,16.75C21.07,15.5 22.27,13.86 23,12C21.27,7.61 17,4.5 12,4.5C10.6,4.5 9.26,4.75 8,5.2L10.17,7.35C10.76,7.13 11.37,7 12,7Z');
  } else {
    // Show "eye" icon
    svg.setAttribute('d', 'M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z');
  }
}

// Update settings UI
function updateSettingsUI(settings) {
  document.getElementById('model-input').value = settings.model || CONFIG.DEFAULT_SETTINGS.model;
  document.getElementById('enable-underline').checked = settings.enableUnderline !== false;
  document.getElementById('enable-markdown').checked = settings.enableMarkdown !== false;

  // Update explore actions summary
  const exploreActions = settings.exploreActions || CONFIG.DEFAULT_SETTINGS.exploreActions;
  updateExploreActionsSummary(exploreActions);

  // Show masked API key if exists
  if (settings.apiKey) {
    const maskedKey = settings.apiKey.substring(0, 8) + '...' + settings.apiKey.substring(settings.apiKey.length - 4);
    document.getElementById('api-key-setting').placeholder = `当前: ${maskedKey}`;
  }
}

// Load chat history
async function loadChatHistory() {
  try {
    const result = await chrome.storage.local.get([CONFIG.STORAGE_KEYS.CHAT_HISTORY]);
    const history = result[CONFIG.STORAGE_KEYS.CHAT_HISTORY] || [];
    
    if (history.length > 0) {
      chatHistory = history;
      const messagesContainer = document.getElementById('chat-messages');
      messagesContainer.innerHTML = '';
      
      history.forEach(msg => {
        addMessageToUI(msg.role, msg.content);
      });
    }
  } catch (error) {
    console.error('Error loading chat history:', error);
  }
}

// Save chat history
async function saveChatHistory() {
  try {
    // Save current session
    await chrome.storage.local.set({
      [CONFIG.STORAGE_KEYS.CHAT_HISTORY]: chatHistory
    });

    // Save to historical sessions if there are messages
    if (chatHistory.length > 0) {
      await saveToHistoricalSessions();
    }
  } catch (error) {
    console.error('Error saving chat history:', error);
  }
}

// Save current session to historical sessions
async function saveToHistoricalSessions() {
  try {
    const result = await chrome.storage.local.get(['chat_sessions']);
    const sessions = result.chat_sessions || [];

    // Create session summary
    const firstUserMessage = chatHistory.find(msg => msg.role === 'user');
    const title = firstUserMessage ?
      (firstUserMessage.content.length > 30 ?
        firstUserMessage.content.substring(0, 30) + '...' :
        firstUserMessage.content) :
      '未命名对话';

    const sessionId = Date.now().toString();
    const session = {
      id: sessionId,
      title: title,
      messages: [...chatHistory],
      timestamp: Date.now(),
      messageCount: chatHistory.length
    };

    // Add to sessions (keep last 50 sessions)
    sessions.unshift(session);
    if (sessions.length > 50) {
      sessions.splice(50);
    }

    await chrome.storage.local.set({ chat_sessions: sessions });
  } catch (error) {
    console.error('Error saving to historical sessions:', error);
  }
}

// Load history list
async function loadHistoryList() {
  try {
    const result = await chrome.storage.local.get(['chat_sessions']);
    const sessions = result.chat_sessions || [];

    const historyList = document.getElementById('history-list');
    historyList.innerHTML = '';

    if (sessions.length === 0) {
      historyList.innerHTML = '<div class="no-history">暂无历史记录</div>';
      return;
    }

    sessions.forEach(session => {
      const sessionElement = document.createElement('div');
      sessionElement.className = 'history-item';
      sessionElement.innerHTML = `
        <div class="history-item-content">
          <div class="history-title">${session.title}</div>
          <div class="history-meta">
            ${new Date(session.timestamp).toLocaleString()} · ${session.messageCount} 条消息
          </div>
        </div>
        <div class="history-actions">
          <button class="history-load-btn" data-session-id="${session.id}">加载</button>
          <button class="history-delete-btn" data-session-id="${session.id}">删除</button>
        </div>
      `;

      historyList.appendChild(sessionElement);
    });

    // Add event listeners
    historyList.querySelectorAll('.history-load-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const sessionId = e.target.dataset.sessionId;
        loadHistorySession(sessionId);
      });
    });

    historyList.querySelectorAll('.history-delete-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const sessionId = e.target.dataset.sessionId;
        deleteHistorySession(sessionId);
      });
    });

  } catch (error) {
    console.error('Error loading history list:', error);
  }
}

// Load specific history session
async function loadHistorySession(sessionId) {
  try {
    const result = await chrome.storage.local.get(['chat_sessions']);
    const sessions = result.chat_sessions || [];
    const session = sessions.find(s => s.id === sessionId);

    if (session) {
      // Clear current chat
      chatHistory = [];
      const messagesContainer = document.getElementById('chat-messages');
      messagesContainer.innerHTML = '';

      // Load session messages
      session.messages.forEach(msg => {
        addMessageToUI(msg.role, msg.content);
      });

      chatHistory = [...session.messages];

      // Close history panel
      document.getElementById('history-panel').style.display = 'none';

      showSuccess('历史记录已加载');
    }
  } catch (error) {
    console.error('Error loading history session:', error);
    showError('加载历史记录失败');
  }
}

// Delete history session
async function deleteHistorySession(sessionId) {
  if (!confirm('确定要删除这条历史记录吗？')) {
    return;
  }

  try {
    const result = await chrome.storage.local.get(['chat_sessions']);
    const sessions = result.chat_sessions || [];
    const filteredSessions = sessions.filter(s => s.id !== sessionId);

    await chrome.storage.local.set({ chat_sessions: filteredSessions });
    loadHistoryList(); // Refresh the list
    showSuccess('历史记录已删除');
  } catch (error) {
    console.error('Error deleting history session:', error);
    showError('删除历史记录失败');
  }
}

// Export history
async function exportHistory() {
  try {
    const result = await chrome.storage.local.get(['chat_sessions']);
    const sessions = result.chat_sessions || [];

    if (sessions.length === 0) {
      showError('没有历史记录可导出');
      return;
    }

    const exportData = {
      exportTime: new Date().toISOString(),
      sessions: sessions
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `ai-assistant-history-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showSuccess('历史记录已导出');
  } catch (error) {
    console.error('Error exporting history:', error);
    showError('导出历史记录失败');
  }
}

// Clear all history
async function clearAllHistory() {
  if (!confirm('确定要清除所有历史记录吗？此操作不可恢复。')) {
    return;
  }

  try {
    await chrome.storage.local.set({ chat_sessions: [] });
    loadHistoryList(); // Refresh the list
    showSuccess('所有历史记录已清除');
  } catch (error) {
    console.error('Error clearing all history:', error);
    showError('清除历史记录失败');
  }
}

// Add message to UI only (without saving to history)
async function addMessageToUI(role, content) {
  const messagesContainer = document.getElementById('chat-messages');
  const messageGroup = document.createElement('div');
  messageGroup.className = 'message-group';

  const message = document.createElement('div');
  message.className = `message ${role}-message`;

  const messageContent = document.createElement('div');
  messageContent.className = 'message-content';

  if (role === 'assistant') {
    // Check if markdown is enabled
    const settings = await getSettings();
    const enableMarkdown = settings.enableMarkdown !== false;

    if (enableMarkdown) {
      // Parse markdown for assistant messages
      const parsedContent = parseMarkdown(content);
      messageContent.innerHTML = `<p>${parsedContent}</p>`;
    } else {
      // Plain text with line breaks
      messageContent.innerHTML = content.replace(/\n/g, '<br>');
    }
  } else {
    // Plain text for user messages
    messageContent.textContent = content;
  }

  message.appendChild(messageContent);
  messageGroup.appendChild(message);
  messagesContainer.appendChild(messageGroup);

  // Scroll to bottom
  messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Show/hide loading
function showLoading(text = 'AI 正在思考...') {
  isLoading = true;

  // Add loading message to chat
  addLoadingMessage(text);

  // Disable send button
  document.getElementById('send-btn').disabled = true;
}

function hideLoading() {
  isLoading = false;

  // Remove loading message
  removeLoadingMessage();

  // Enable send button if input has content
  const messageInput = document.getElementById('message-input');
  document.getElementById('send-btn').disabled = !messageInput.value.trim();
}

// Add loading message to chat
function addLoadingMessage(text) {
  const messagesContainer = document.getElementById('chat-messages');
  const messageGroup = document.createElement('div');
  messageGroup.className = 'message-group loading-message-group';
  messageGroup.id = 'loading-message';

  const message = document.createElement('div');
  message.className = 'message assistant-message';

  const messageContent = document.createElement('div');
  messageContent.className = 'message-content loading-content';
  messageContent.innerHTML = `
    <div class="loading-dots">
      <div class="dot"></div>
      <div class="dot"></div>
      <div class="dot"></div>
    </div>
    <span class="loading-text">${text}</span>
  `;

  message.appendChild(messageContent);
  messageGroup.appendChild(message);
  messagesContainer.appendChild(messageGroup);

  // Scroll to bottom
  messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Remove loading message
function removeLoadingMessage() {
  const loadingMessage = document.getElementById('loading-message');
  if (loadingMessage) {
    loadingMessage.remove();
  }
}

// Utility functions
async function getSettings() {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ action: 'getSettings' }, resolve);
  });
}

async function updateSettings(settings) {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ action: 'updateSettings', data: settings }, resolve);
  });
}

async function sendToBackground(message) {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage(message, resolve);
  });
}

function showError(message) {
  console.error('Error:', message);
  showToast(message, 'error');
}

function showSuccess(message) {
  console.log('Success:', message);
  showToast(message, 'success');
}

// Show toast notification
function showToast(message, type = 'info') {
  // Remove existing toast
  const existingToast = document.getElementById('toast-notification');
  if (existingToast) {
    existingToast.remove();
  }

  const toast = document.createElement('div');
  toast.id = 'toast-notification';
  toast.className = `toast toast-${type}`;
  toast.textContent = message;

  toast.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    z-index: 10000;
    max-width: 300px;
    word-wrap: break-word;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
    transform: translateX(100%);
  `;

  // Set background color based on type
  if (type === 'error') {
    toast.style.background = '#dc3545';
  } else if (type === 'success') {
    toast.style.background = '#28a745';
  } else {
    toast.style.background = '#17a2b8';
  }

  document.body.appendChild(toast);

  // Animate in
  setTimeout(() => {
    toast.style.transform = 'translateX(0)';
  }, 10);

  // Auto-hide after 3 seconds
  setTimeout(() => {
    if (toast.parentElement) {
      toast.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (toast.parentElement) {
          toast.remove();
        }
      }, 300);
    }
  }, 3000);
}
