(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();try{}catch(e){console.error("[wxt] Failed to initialize plugins",e)}var sm=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Ma(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var am={exports:{}},Ra={},lm={exports:{}},X={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Uo=Symbol.for("react.element"),xy=Symbol.for("react.portal"),_y=Symbol.for("react.fragment"),Sy=Symbol.for("react.strict_mode"),Ey=Symbol.for("react.profiler"),Ay=Symbol.for("react.provider"),Cy=Symbol.for("react.context"),by=Symbol.for("react.forward_ref"),Ty=Symbol.for("react.suspense"),ky=Symbol.for("react.memo"),Py=Symbol.for("react.lazy"),lh=Symbol.iterator;function Dy(e){return e===null||typeof e!="object"?null:(e=lh&&e[lh]||e["@@iterator"],typeof e=="function"?e:null)}var um={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},cm=Object.assign,fm={};function bi(e,t,n){this.props=e,this.context=t,this.refs=fm,this.updater=n||um}bi.prototype.isReactComponent={};bi.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};bi.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function hm(){}hm.prototype=bi.prototype;function kc(e,t,n){this.props=e,this.context=t,this.refs=fm,this.updater=n||um}var Pc=kc.prototype=new hm;Pc.constructor=kc;cm(Pc,bi.prototype);Pc.isPureReactComponent=!0;var uh=Array.isArray,dm=Object.prototype.hasOwnProperty,Dc={current:null},pm={key:!0,ref:!0,__self:!0,__source:!0};function mm(e,t,n){var r,i={},o=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(o=""+t.key),t)dm.call(t,r)&&!pm.hasOwnProperty(r)&&(i[r]=t[r]);var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];i.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)i[r]===void 0&&(i[r]=a[r]);return{$$typeof:Uo,type:e,key:o,ref:s,props:i,_owner:Dc.current}}function My(e,t){return{$$typeof:Uo,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Mc(e){return typeof e=="object"&&e!==null&&e.$$typeof===Uo}function Ry(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var ch=/\/+/g;function dl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Ry(""+e.key):t.toString(36)}function Os(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case Uo:case xy:s=!0}}if(s)return s=e,i=i(s),e=r===""?"."+dl(s,0):r,uh(i)?(n="",e!=null&&(n=e.replace(ch,"$&/")+"/"),Os(i,t,n,"",function(u){return u})):i!=null&&(Mc(i)&&(i=My(i,n+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(ch,"$&/")+"/")+e)),t.push(i)),1;if(s=0,r=r===""?".":r+":",uh(e))for(var a=0;a<e.length;a++){o=e[a];var l=r+dl(o,a);s+=Os(o,t,n,l,i)}else if(l=Dy(e),typeof l=="function")for(e=l.call(e),a=0;!(o=e.next()).done;)o=o.value,l=r+dl(o,a++),s+=Os(o,t,n,l,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function os(e,t,n){if(e==null)return e;var r=[],i=0;return Os(e,r,"","",function(o){return t.call(n,o,i++)}),r}function Oy(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ze={current:null},Is={transition:null},Iy={ReactCurrentDispatcher:Ze,ReactCurrentBatchConfig:Is,ReactCurrentOwner:Dc};function gm(){throw Error("act(...) is not supported in production builds of React.")}X.Children={map:os,forEach:function(e,t,n){os(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return os(e,function(){t++}),t},toArray:function(e){return os(e,function(t){return t})||[]},only:function(e){if(!Mc(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};X.Component=bi;X.Fragment=_y;X.Profiler=Ey;X.PureComponent=kc;X.StrictMode=Sy;X.Suspense=Ty;X.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Iy;X.act=gm;X.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=cm({},e.props),i=e.key,o=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,s=Dc.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)dm.call(t,l)&&!pm.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:Uo,type:e.type,key:i,ref:o,props:r,_owner:s}};X.createContext=function(e){return e={$$typeof:Cy,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Ay,_context:e},e.Consumer=e};X.createElement=mm;X.createFactory=function(e){var t=mm.bind(null,e);return t.type=e,t};X.createRef=function(){return{current:null}};X.forwardRef=function(e){return{$$typeof:by,render:e}};X.isValidElement=Mc;X.lazy=function(e){return{$$typeof:Py,_payload:{_status:-1,_result:e},_init:Oy}};X.memo=function(e,t){return{$$typeof:ky,type:e,compare:t===void 0?null:t}};X.startTransition=function(e){var t=Is.transition;Is.transition={};try{e()}finally{Is.transition=t}};X.unstable_act=gm;X.useCallback=function(e,t){return Ze.current.useCallback(e,t)};X.useContext=function(e){return Ze.current.useContext(e)};X.useDebugValue=function(){};X.useDeferredValue=function(e){return Ze.current.useDeferredValue(e)};X.useEffect=function(e,t){return Ze.current.useEffect(e,t)};X.useId=function(){return Ze.current.useId()};X.useImperativeHandle=function(e,t,n){return Ze.current.useImperativeHandle(e,t,n)};X.useInsertionEffect=function(e,t){return Ze.current.useInsertionEffect(e,t)};X.useLayoutEffect=function(e,t){return Ze.current.useLayoutEffect(e,t)};X.useMemo=function(e,t){return Ze.current.useMemo(e,t)};X.useReducer=function(e,t,n){return Ze.current.useReducer(e,t,n)};X.useRef=function(e){return Ze.current.useRef(e)};X.useState=function(e){return Ze.current.useState(e)};X.useSyncExternalStore=function(e,t,n){return Ze.current.useSyncExternalStore(e,t,n)};X.useTransition=function(){return Ze.current.useTransition()};X.version="18.3.1";lm.exports=X;var M=lm.exports;const Ly=Ma(M);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vy=M,By=Symbol.for("react.element"),Ny=Symbol.for("react.fragment"),Fy=Object.prototype.hasOwnProperty,zy=Vy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Uy={key:!0,ref:!0,__self:!0,__source:!0};function vm(e,t,n){var r,i={},o=null,s=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)Fy.call(t,r)&&!Uy.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:By,type:e,key:o,ref:s,props:i,_owner:zy.current}}Ra.Fragment=Ny;Ra.jsx=vm;Ra.jsxs=vm;am.exports=Ra;var fo=am.exports,fh={},ym={exports:{}},mt={},wm={exports:{}},xm={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(N,H){var G=N.length;N.push(H);e:for(;0<G;){var se=G-1>>>1,re=N[se];if(0<i(re,H))N[se]=H,N[G]=re,G=se;else break e}}function n(N){return N.length===0?null:N[0]}function r(N){if(N.length===0)return null;var H=N[0],G=N.pop();if(G!==H){N[0]=G;e:for(var se=0,re=N.length,Rt=re>>>1;se<Rt;){var vt=2*(se+1)-1,Yt=N[vt],rt=vt+1,Zt=N[rt];if(0>i(Yt,G))rt<re&&0>i(Zt,Yt)?(N[se]=Zt,N[rt]=G,se=rt):(N[se]=Yt,N[vt]=G,se=vt);else if(rt<re&&0>i(Zt,G))N[se]=Zt,N[rt]=G,se=rt;else break e}}return H}function i(N,H){var G=N.sortIndex-H.sortIndex;return G!==0?G:N.id-H.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var s=Date,a=s.now();e.unstable_now=function(){return s.now()-a}}var l=[],u=[],c=1,h=null,f=3,v=!1,p=!1,g=!1,S=typeof setTimeout=="function"?setTimeout:null,y=typeof clearTimeout=="function"?clearTimeout:null,w=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function _(N){for(var H=n(u);H!==null;){if(H.callback===null)r(u);else if(H.startTime<=N)r(u),H.sortIndex=H.expirationTime,t(l,H);else break;H=n(u)}}function E(N){if(g=!1,_(N),!p)if(n(l)!==null)p=!0,ee(b);else{var H=n(u);H!==null&&te(E,H.startTime-N)}}function b(N,H){p=!1,g&&(g=!1,y(C),C=-1),v=!0;var G=f;try{for(_(H),h=n(l);h!==null&&(!(h.expirationTime>H)||N&&!B());){var se=h.callback;if(typeof se=="function"){h.callback=null,f=h.priorityLevel;var re=se(h.expirationTime<=H);H=e.unstable_now(),typeof re=="function"?h.callback=re:h===n(l)&&r(l),_(H)}else r(l);h=n(l)}if(h!==null)var Rt=!0;else{var vt=n(u);vt!==null&&te(E,vt.startTime-H),Rt=!1}return Rt}finally{h=null,f=G,v=!1}}var k=!1,P=null,C=-1,D=5,R=-1;function B(){return!(e.unstable_now()-R<D)}function Y(){if(P!==null){var N=e.unstable_now();R=N;var H=!0;try{H=P(!0,N)}finally{H?J():(k=!1,P=null)}}else k=!1}var J;if(typeof w=="function")J=function(){w(Y)};else if(typeof MessageChannel<"u"){var ue=new MessageChannel,j=ue.port2;ue.port1.onmessage=Y,J=function(){j.postMessage(null)}}else J=function(){S(Y,0)};function ee(N){P=N,k||(k=!0,J())}function te(N,H){C=S(function(){N(e.unstable_now())},H)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(N){N.callback=null},e.unstable_continueExecution=function(){p||v||(p=!0,ee(b))},e.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):D=0<N?Math.floor(1e3/N):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(N){switch(f){case 1:case 2:case 3:var H=3;break;default:H=f}var G=f;f=H;try{return N()}finally{f=G}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(N,H){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var G=f;f=N;try{return H()}finally{f=G}},e.unstable_scheduleCallback=function(N,H,G){var se=e.unstable_now();switch(typeof G=="object"&&G!==null?(G=G.delay,G=typeof G=="number"&&0<G?se+G:se):G=se,N){case 1:var re=-1;break;case 2:re=250;break;case 5:re=**********;break;case 4:re=1e4;break;default:re=5e3}return re=G+re,N={id:c++,callback:H,priorityLevel:N,startTime:G,expirationTime:re,sortIndex:-1},G>se?(N.sortIndex=G,t(u,N),n(l)===null&&N===n(u)&&(g?(y(C),C=-1):g=!0,te(E,G-se))):(N.sortIndex=re,t(l,N),p||v||(p=!0,ee(b))),N},e.unstable_shouldYield=B,e.unstable_wrapCallback=function(N){var H=f;return function(){var G=f;f=H;try{return N.apply(this,arguments)}finally{f=G}}}})(xm);wm.exports=xm;var jy=wm.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gy=M,ft=jy;function L(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var _m=new Set,ho={};function Rr(e,t){fi(e,t),fi(e+"Capture",t)}function fi(e,t){for(ho[e]=t,e=0;e<t.length;e++)_m.add(t[e])}var vn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),lu=Object.prototype.hasOwnProperty,Hy=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,hh={},dh={};function Wy(e){return lu.call(dh,e)?!0:lu.call(hh,e)?!1:Hy.test(e)?dh[e]=!0:(hh[e]=!0,!1)}function Ky(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function qy(e,t,n,r){if(t===null||typeof t>"u"||Ky(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Xe(e,t,n,r,i,o,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=s}var Ne={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ne[e]=new Xe(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Ne[t]=new Xe(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ne[e]=new Xe(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ne[e]=new Xe(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ne[e]=new Xe(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ne[e]=new Xe(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ne[e]=new Xe(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ne[e]=new Xe(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ne[e]=new Xe(e,5,!1,e.toLowerCase(),null,!1,!1)});var Rc=/[\-:]([a-z])/g;function Oc(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Rc,Oc);Ne[t]=new Xe(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Rc,Oc);Ne[t]=new Xe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Rc,Oc);Ne[t]=new Xe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ne[e]=new Xe(e,1,!1,e.toLowerCase(),null,!1,!1)});Ne.xlinkHref=new Xe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ne[e]=new Xe(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ic(e,t,n,r){var i=Ne.hasOwnProperty(t)?Ne[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(qy(t,n,i,r)&&(n=null),r||i===null?Wy(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var En=Gy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ss=Symbol.for("react.element"),Nr=Symbol.for("react.portal"),Fr=Symbol.for("react.fragment"),Lc=Symbol.for("react.strict_mode"),uu=Symbol.for("react.profiler"),Sm=Symbol.for("react.provider"),Em=Symbol.for("react.context"),Vc=Symbol.for("react.forward_ref"),cu=Symbol.for("react.suspense"),fu=Symbol.for("react.suspense_list"),Bc=Symbol.for("react.memo"),Pn=Symbol.for("react.lazy"),Am=Symbol.for("react.offscreen"),ph=Symbol.iterator;function Ii(e){return e===null||typeof e!="object"?null:(e=ph&&e[ph]||e["@@iterator"],typeof e=="function"?e:null)}var _e=Object.assign,pl;function Gi(e){if(pl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);pl=t&&t[1]||""}return`
`+pl+e}var ml=!1;function gl(e,t){if(!e||ml)return"";ml=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),o=r.stack.split(`
`),s=i.length-1,a=o.length-1;1<=s&&0<=a&&i[s]!==o[a];)a--;for(;1<=s&&0<=a;s--,a--)if(i[s]!==o[a]){if(s!==1||a!==1)do if(s--,a--,0>a||i[s]!==o[a]){var l=`
`+i[s].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=s&&0<=a);break}}}finally{ml=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Gi(e):""}function $y(e){switch(e.tag){case 5:return Gi(e.type);case 16:return Gi("Lazy");case 13:return Gi("Suspense");case 19:return Gi("SuspenseList");case 0:case 2:case 15:return e=gl(e.type,!1),e;case 11:return e=gl(e.type.render,!1),e;case 1:return e=gl(e.type,!0),e;default:return""}}function hu(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Fr:return"Fragment";case Nr:return"Portal";case uu:return"Profiler";case Lc:return"StrictMode";case cu:return"Suspense";case fu:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Em:return(e.displayName||"Context")+".Consumer";case Sm:return(e._context.displayName||"Context")+".Provider";case Vc:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Bc:return t=e.displayName||null,t!==null?t:hu(e.type)||"Memo";case Pn:t=e._payload,e=e._init;try{return hu(e(t))}catch{}}return null}function Yy(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return hu(t);case 8:return t===Lc?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function $n(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Cm(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Zy(e){var t=Cm(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(s){r=""+s,o.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function as(e){e._valueTracker||(e._valueTracker=Zy(e))}function bm(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Cm(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Xs(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function du(e,t){var n=t.checked;return _e({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function mh(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=$n(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Tm(e,t){t=t.checked,t!=null&&Ic(e,"checked",t,!1)}function pu(e,t){Tm(e,t);var n=$n(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?mu(e,t.type,n):t.hasOwnProperty("defaultValue")&&mu(e,t.type,$n(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function gh(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function mu(e,t,n){(t!=="number"||Xs(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Hi=Array.isArray;function ri(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+$n(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function gu(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(L(91));return _e({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function vh(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(L(92));if(Hi(n)){if(1<n.length)throw Error(L(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:$n(n)}}function km(e,t){var n=$n(t.value),r=$n(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function yh(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Pm(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function vu(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Pm(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var ls,Dm=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(ls=ls||document.createElement("div"),ls.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ls.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function po(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Zi={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Xy=["Webkit","ms","Moz","O"];Object.keys(Zi).forEach(function(e){Xy.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Zi[t]=Zi[e]})});function Mm(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Zi.hasOwnProperty(e)&&Zi[e]?(""+t).trim():t+"px"}function Rm(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=Mm(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var Qy=_e({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function yu(e,t){if(t){if(Qy[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(L(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(L(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(L(61))}if(t.style!=null&&typeof t.style!="object")throw Error(L(62))}}function wu(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var xu=null;function Nc(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var _u=null,ii=null,oi=null;function wh(e){if(e=Ho(e)){if(typeof _u!="function")throw Error(L(280));var t=e.stateNode;t&&(t=Ba(t),_u(e.stateNode,e.type,t))}}function Om(e){ii?oi?oi.push(e):oi=[e]:ii=e}function Im(){if(ii){var e=ii,t=oi;if(oi=ii=null,wh(e),t)for(e=0;e<t.length;e++)wh(t[e])}}function Lm(e,t){return e(t)}function Vm(){}var vl=!1;function Bm(e,t,n){if(vl)return e(t,n);vl=!0;try{return Lm(e,t,n)}finally{vl=!1,(ii!==null||oi!==null)&&(Vm(),Im())}}function mo(e,t){var n=e.stateNode;if(n===null)return null;var r=Ba(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(L(231,t,typeof n));return n}var Su=!1;if(vn)try{var Li={};Object.defineProperty(Li,"passive",{get:function(){Su=!0}}),window.addEventListener("test",Li,Li),window.removeEventListener("test",Li,Li)}catch{Su=!1}function Jy(e,t,n,r,i,o,s,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Xi=!1,Qs=null,Js=!1,Eu=null,e2={onError:function(e){Xi=!0,Qs=e}};function t2(e,t,n,r,i,o,s,a,l){Xi=!1,Qs=null,Jy.apply(e2,arguments)}function n2(e,t,n,r,i,o,s,a,l){if(t2.apply(this,arguments),Xi){if(Xi){var u=Qs;Xi=!1,Qs=null}else throw Error(L(198));Js||(Js=!0,Eu=u)}}function Or(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Nm(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function xh(e){if(Or(e)!==e)throw Error(L(188))}function r2(e){var t=e.alternate;if(!t){if(t=Or(e),t===null)throw Error(L(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return xh(i),e;if(o===r)return xh(i),t;o=o.sibling}throw Error(L(188))}if(n.return!==r.return)n=i,r=o;else{for(var s=!1,a=i.child;a;){if(a===n){s=!0,n=i,r=o;break}if(a===r){s=!0,r=i,n=o;break}a=a.sibling}if(!s){for(a=o.child;a;){if(a===n){s=!0,n=o,r=i;break}if(a===r){s=!0,r=o,n=i;break}a=a.sibling}if(!s)throw Error(L(189))}}if(n.alternate!==r)throw Error(L(190))}if(n.tag!==3)throw Error(L(188));return n.stateNode.current===n?e:t}function Fm(e){return e=r2(e),e!==null?zm(e):null}function zm(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=zm(e);if(t!==null)return t;e=e.sibling}return null}var Um=ft.unstable_scheduleCallback,_h=ft.unstable_cancelCallback,i2=ft.unstable_shouldYield,o2=ft.unstable_requestPaint,Ae=ft.unstable_now,s2=ft.unstable_getCurrentPriorityLevel,Fc=ft.unstable_ImmediatePriority,jm=ft.unstable_UserBlockingPriority,ea=ft.unstable_NormalPriority,a2=ft.unstable_LowPriority,Gm=ft.unstable_IdlePriority,Oa=null,tn=null;function l2(e){if(tn&&typeof tn.onCommitFiberRoot=="function")try{tn.onCommitFiberRoot(Oa,e,void 0,(e.current.flags&128)===128)}catch{}}var Wt=Math.clz32?Math.clz32:f2,u2=Math.log,c2=Math.LN2;function f2(e){return e>>>=0,e===0?32:31-(u2(e)/c2|0)|0}var us=64,cs=4194304;function Wi(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ta(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,s=n&268435455;if(s!==0){var a=s&~i;a!==0?r=Wi(a):(o&=s,o!==0&&(r=Wi(o)))}else s=n&~i,s!==0?r=Wi(s):o!==0&&(r=Wi(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Wt(t),i=1<<n,r|=e[n],t&=~i;return r}function h2(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function d2(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var s=31-Wt(o),a=1<<s,l=i[s];l===-1?(!(a&n)||a&r)&&(i[s]=h2(a,t)):l<=t&&(e.expiredLanes|=a),o&=~a}}function Au(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Hm(){var e=us;return us<<=1,!(us&4194240)&&(us=64),e}function yl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function jo(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Wt(t),e[t]=n}function p2(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-Wt(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function zc(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Wt(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var le=0;function Wm(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Km,Uc,qm,$m,Ym,Cu=!1,fs=[],Bn=null,Nn=null,Fn=null,go=new Map,vo=new Map,Rn=[],m2="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Sh(e,t){switch(e){case"focusin":case"focusout":Bn=null;break;case"dragenter":case"dragleave":Nn=null;break;case"mouseover":case"mouseout":Fn=null;break;case"pointerover":case"pointerout":go.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":vo.delete(t.pointerId)}}function Vi(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=Ho(t),t!==null&&Uc(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function g2(e,t,n,r,i){switch(t){case"focusin":return Bn=Vi(Bn,e,t,n,r,i),!0;case"dragenter":return Nn=Vi(Nn,e,t,n,r,i),!0;case"mouseover":return Fn=Vi(Fn,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return go.set(o,Vi(go.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,vo.set(o,Vi(vo.get(o)||null,e,t,n,r,i)),!0}return!1}function Zm(e){var t=vr(e.target);if(t!==null){var n=Or(t);if(n!==null){if(t=n.tag,t===13){if(t=Nm(n),t!==null){e.blockedOn=t,Ym(e.priority,function(){qm(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ls(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=bu(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);xu=r,n.target.dispatchEvent(r),xu=null}else return t=Ho(n),t!==null&&Uc(t),e.blockedOn=n,!1;t.shift()}return!0}function Eh(e,t,n){Ls(e)&&n.delete(t)}function v2(){Cu=!1,Bn!==null&&Ls(Bn)&&(Bn=null),Nn!==null&&Ls(Nn)&&(Nn=null),Fn!==null&&Ls(Fn)&&(Fn=null),go.forEach(Eh),vo.forEach(Eh)}function Bi(e,t){e.blockedOn===t&&(e.blockedOn=null,Cu||(Cu=!0,ft.unstable_scheduleCallback(ft.unstable_NormalPriority,v2)))}function yo(e){function t(i){return Bi(i,e)}if(0<fs.length){Bi(fs[0],e);for(var n=1;n<fs.length;n++){var r=fs[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Bn!==null&&Bi(Bn,e),Nn!==null&&Bi(Nn,e),Fn!==null&&Bi(Fn,e),go.forEach(t),vo.forEach(t),n=0;n<Rn.length;n++)r=Rn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Rn.length&&(n=Rn[0],n.blockedOn===null);)Zm(n),n.blockedOn===null&&Rn.shift()}var si=En.ReactCurrentBatchConfig,na=!0;function y2(e,t,n,r){var i=le,o=si.transition;si.transition=null;try{le=1,jc(e,t,n,r)}finally{le=i,si.transition=o}}function w2(e,t,n,r){var i=le,o=si.transition;si.transition=null;try{le=4,jc(e,t,n,r)}finally{le=i,si.transition=o}}function jc(e,t,n,r){if(na){var i=bu(e,t,n,r);if(i===null)kl(e,t,r,ra,n),Sh(e,r);else if(g2(i,e,t,n,r))r.stopPropagation();else if(Sh(e,r),t&4&&-1<m2.indexOf(e)){for(;i!==null;){var o=Ho(i);if(o!==null&&Km(o),o=bu(e,t,n,r),o===null&&kl(e,t,r,ra,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else kl(e,t,r,null,n)}}var ra=null;function bu(e,t,n,r){if(ra=null,e=Nc(r),e=vr(e),e!==null)if(t=Or(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Nm(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ra=e,null}function Xm(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(s2()){case Fc:return 1;case jm:return 4;case ea:case a2:return 16;case Gm:return 536870912;default:return 16}default:return 16}}var In=null,Gc=null,Vs=null;function Qm(){if(Vs)return Vs;var e,t=Gc,n=t.length,r,i="value"in In?In.value:In.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===i[o-r];r++);return Vs=i.slice(e,1<r?1-r:void 0)}function Bs(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function hs(){return!0}function Ah(){return!1}function gt(e){function t(n,r,i,o,s){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=s,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(o):o[a]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?hs:Ah,this.isPropagationStopped=Ah,this}return _e(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=hs)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=hs)},persist:function(){},isPersistent:hs}),t}var Ti={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Hc=gt(Ti),Go=_e({},Ti,{view:0,detail:0}),x2=gt(Go),wl,xl,Ni,Ia=_e({},Go,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Wc,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Ni&&(Ni&&e.type==="mousemove"?(wl=e.screenX-Ni.screenX,xl=e.screenY-Ni.screenY):xl=wl=0,Ni=e),wl)},movementY:function(e){return"movementY"in e?e.movementY:xl}}),Ch=gt(Ia),_2=_e({},Ia,{dataTransfer:0}),S2=gt(_2),E2=_e({},Go,{relatedTarget:0}),_l=gt(E2),A2=_e({},Ti,{animationName:0,elapsedTime:0,pseudoElement:0}),C2=gt(A2),b2=_e({},Ti,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),T2=gt(b2),k2=_e({},Ti,{data:0}),bh=gt(k2),P2={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},D2={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},M2={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function R2(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=M2[e])?!!t[e]:!1}function Wc(){return R2}var O2=_e({},Go,{key:function(e){if(e.key){var t=P2[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Bs(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?D2[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Wc,charCode:function(e){return e.type==="keypress"?Bs(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Bs(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),I2=gt(O2),L2=_e({},Ia,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Th=gt(L2),V2=_e({},Go,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Wc}),B2=gt(V2),N2=_e({},Ti,{propertyName:0,elapsedTime:0,pseudoElement:0}),F2=gt(N2),z2=_e({},Ia,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),U2=gt(z2),j2=[9,13,27,32],Kc=vn&&"CompositionEvent"in window,Qi=null;vn&&"documentMode"in document&&(Qi=document.documentMode);var G2=vn&&"TextEvent"in window&&!Qi,Jm=vn&&(!Kc||Qi&&8<Qi&&11>=Qi),kh=" ",Ph=!1;function eg(e,t){switch(e){case"keyup":return j2.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function tg(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var zr=!1;function H2(e,t){switch(e){case"compositionend":return tg(t);case"keypress":return t.which!==32?null:(Ph=!0,kh);case"textInput":return e=t.data,e===kh&&Ph?null:e;default:return null}}function W2(e,t){if(zr)return e==="compositionend"||!Kc&&eg(e,t)?(e=Qm(),Vs=Gc=In=null,zr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Jm&&t.locale!=="ko"?null:t.data;default:return null}}var K2={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Dh(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!K2[e.type]:t==="textarea"}function ng(e,t,n,r){Om(r),t=ia(t,"onChange"),0<t.length&&(n=new Hc("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Ji=null,wo=null;function q2(e){dg(e,0)}function La(e){var t=Gr(e);if(bm(t))return e}function $2(e,t){if(e==="change")return t}var rg=!1;if(vn){var Sl;if(vn){var El="oninput"in document;if(!El){var Mh=document.createElement("div");Mh.setAttribute("oninput","return;"),El=typeof Mh.oninput=="function"}Sl=El}else Sl=!1;rg=Sl&&(!document.documentMode||9<document.documentMode)}function Rh(){Ji&&(Ji.detachEvent("onpropertychange",ig),wo=Ji=null)}function ig(e){if(e.propertyName==="value"&&La(wo)){var t=[];ng(t,wo,e,Nc(e)),Bm(q2,t)}}function Y2(e,t,n){e==="focusin"?(Rh(),Ji=t,wo=n,Ji.attachEvent("onpropertychange",ig)):e==="focusout"&&Rh()}function Z2(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return La(wo)}function X2(e,t){if(e==="click")return La(t)}function Q2(e,t){if(e==="input"||e==="change")return La(t)}function J2(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var qt=typeof Object.is=="function"?Object.is:J2;function xo(e,t){if(qt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!lu.call(t,i)||!qt(e[i],t[i]))return!1}return!0}function Oh(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ih(e,t){var n=Oh(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Oh(n)}}function og(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?og(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function sg(){for(var e=window,t=Xs();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Xs(e.document)}return t}function qc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function e3(e){var t=sg(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&og(n.ownerDocument.documentElement,n)){if(r!==null&&qc(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=Ih(n,o);var s=Ih(n,r);i&&s&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var t3=vn&&"documentMode"in document&&11>=document.documentMode,Ur=null,Tu=null,eo=null,ku=!1;function Lh(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ku||Ur==null||Ur!==Xs(r)||(r=Ur,"selectionStart"in r&&qc(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),eo&&xo(eo,r)||(eo=r,r=ia(Tu,"onSelect"),0<r.length&&(t=new Hc("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Ur)))}function ds(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var jr={animationend:ds("Animation","AnimationEnd"),animationiteration:ds("Animation","AnimationIteration"),animationstart:ds("Animation","AnimationStart"),transitionend:ds("Transition","TransitionEnd")},Al={},ag={};vn&&(ag=document.createElement("div").style,"AnimationEvent"in window||(delete jr.animationend.animation,delete jr.animationiteration.animation,delete jr.animationstart.animation),"TransitionEvent"in window||delete jr.transitionend.transition);function Va(e){if(Al[e])return Al[e];if(!jr[e])return e;var t=jr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in ag)return Al[e]=t[n];return e}var lg=Va("animationend"),ug=Va("animationiteration"),cg=Va("animationstart"),fg=Va("transitionend"),hg=new Map,Vh="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Qn(e,t){hg.set(e,t),Rr(t,[e])}for(var Cl=0;Cl<Vh.length;Cl++){var bl=Vh[Cl],n3=bl.toLowerCase(),r3=bl[0].toUpperCase()+bl.slice(1);Qn(n3,"on"+r3)}Qn(lg,"onAnimationEnd");Qn(ug,"onAnimationIteration");Qn(cg,"onAnimationStart");Qn("dblclick","onDoubleClick");Qn("focusin","onFocus");Qn("focusout","onBlur");Qn(fg,"onTransitionEnd");fi("onMouseEnter",["mouseout","mouseover"]);fi("onMouseLeave",["mouseout","mouseover"]);fi("onPointerEnter",["pointerout","pointerover"]);fi("onPointerLeave",["pointerout","pointerover"]);Rr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Rr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Rr("onBeforeInput",["compositionend","keypress","textInput","paste"]);Rr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Rr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Rr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ki="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),i3=new Set("cancel close invalid load scroll toggle".split(" ").concat(Ki));function Bh(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,n2(r,t,void 0,e),e.currentTarget=null}function dg(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var s=r.length-1;0<=s;s--){var a=r[s],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==o&&i.isPropagationStopped())break e;Bh(i,a,u),o=l}else for(s=0;s<r.length;s++){if(a=r[s],l=a.instance,u=a.currentTarget,a=a.listener,l!==o&&i.isPropagationStopped())break e;Bh(i,a,u),o=l}}}if(Js)throw e=Eu,Js=!1,Eu=null,e}function de(e,t){var n=t[Ou];n===void 0&&(n=t[Ou]=new Set);var r=e+"__bubble";n.has(r)||(pg(t,e,2,!1),n.add(r))}function Tl(e,t,n){var r=0;t&&(r|=4),pg(n,e,r,t)}var ps="_reactListening"+Math.random().toString(36).slice(2);function _o(e){if(!e[ps]){e[ps]=!0,_m.forEach(function(n){n!=="selectionchange"&&(i3.has(n)||Tl(n,!1,e),Tl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ps]||(t[ps]=!0,Tl("selectionchange",!1,t))}}function pg(e,t,n,r){switch(Xm(t)){case 1:var i=y2;break;case 4:i=w2;break;default:i=jc}n=i.bind(null,t,n,e),i=void 0,!Su||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function kl(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var a=r.stateNode.containerInfo;if(a===i||a.nodeType===8&&a.parentNode===i)break;if(s===4)for(s=r.return;s!==null;){var l=s.tag;if((l===3||l===4)&&(l=s.stateNode.containerInfo,l===i||l.nodeType===8&&l.parentNode===i))return;s=s.return}for(;a!==null;){if(s=vr(a),s===null)return;if(l=s.tag,l===5||l===6){r=o=s;continue e}a=a.parentNode}}r=r.return}Bm(function(){var u=o,c=Nc(n),h=[];e:{var f=hg.get(e);if(f!==void 0){var v=Hc,p=e;switch(e){case"keypress":if(Bs(n)===0)break e;case"keydown":case"keyup":v=I2;break;case"focusin":p="focus",v=_l;break;case"focusout":p="blur",v=_l;break;case"beforeblur":case"afterblur":v=_l;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":v=Ch;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":v=S2;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":v=B2;break;case lg:case ug:case cg:v=C2;break;case fg:v=F2;break;case"scroll":v=x2;break;case"wheel":v=U2;break;case"copy":case"cut":case"paste":v=T2;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":v=Th}var g=(t&4)!==0,S=!g&&e==="scroll",y=g?f!==null?f+"Capture":null:f;g=[];for(var w=u,_;w!==null;){_=w;var E=_.stateNode;if(_.tag===5&&E!==null&&(_=E,y!==null&&(E=mo(w,y),E!=null&&g.push(So(w,E,_)))),S)break;w=w.return}0<g.length&&(f=new v(f,p,null,n,c),h.push({event:f,listeners:g}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",v=e==="mouseout"||e==="pointerout",f&&n!==xu&&(p=n.relatedTarget||n.fromElement)&&(vr(p)||p[yn]))break e;if((v||f)&&(f=c.window===c?c:(f=c.ownerDocument)?f.defaultView||f.parentWindow:window,v?(p=n.relatedTarget||n.toElement,v=u,p=p?vr(p):null,p!==null&&(S=Or(p),p!==S||p.tag!==5&&p.tag!==6)&&(p=null)):(v=null,p=u),v!==p)){if(g=Ch,E="onMouseLeave",y="onMouseEnter",w="mouse",(e==="pointerout"||e==="pointerover")&&(g=Th,E="onPointerLeave",y="onPointerEnter",w="pointer"),S=v==null?f:Gr(v),_=p==null?f:Gr(p),f=new g(E,w+"leave",v,n,c),f.target=S,f.relatedTarget=_,E=null,vr(c)===u&&(g=new g(y,w+"enter",p,n,c),g.target=_,g.relatedTarget=S,E=g),S=E,v&&p)t:{for(g=v,y=p,w=0,_=g;_;_=Vr(_))w++;for(_=0,E=y;E;E=Vr(E))_++;for(;0<w-_;)g=Vr(g),w--;for(;0<_-w;)y=Vr(y),_--;for(;w--;){if(g===y||y!==null&&g===y.alternate)break t;g=Vr(g),y=Vr(y)}g=null}else g=null;v!==null&&Nh(h,f,v,g,!1),p!==null&&S!==null&&Nh(h,S,p,g,!0)}}e:{if(f=u?Gr(u):window,v=f.nodeName&&f.nodeName.toLowerCase(),v==="select"||v==="input"&&f.type==="file")var b=$2;else if(Dh(f))if(rg)b=Q2;else{b=Z2;var k=Y2}else(v=f.nodeName)&&v.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(b=X2);if(b&&(b=b(e,u))){ng(h,b,n,c);break e}k&&k(e,f,u),e==="focusout"&&(k=f._wrapperState)&&k.controlled&&f.type==="number"&&mu(f,"number",f.value)}switch(k=u?Gr(u):window,e){case"focusin":(Dh(k)||k.contentEditable==="true")&&(Ur=k,Tu=u,eo=null);break;case"focusout":eo=Tu=Ur=null;break;case"mousedown":ku=!0;break;case"contextmenu":case"mouseup":case"dragend":ku=!1,Lh(h,n,c);break;case"selectionchange":if(t3)break;case"keydown":case"keyup":Lh(h,n,c)}var P;if(Kc)e:{switch(e){case"compositionstart":var C="onCompositionStart";break e;case"compositionend":C="onCompositionEnd";break e;case"compositionupdate":C="onCompositionUpdate";break e}C=void 0}else zr?eg(e,n)&&(C="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(C="onCompositionStart");C&&(Jm&&n.locale!=="ko"&&(zr||C!=="onCompositionStart"?C==="onCompositionEnd"&&zr&&(P=Qm()):(In=c,Gc="value"in In?In.value:In.textContent,zr=!0)),k=ia(u,C),0<k.length&&(C=new bh(C,e,null,n,c),h.push({event:C,listeners:k}),P?C.data=P:(P=tg(n),P!==null&&(C.data=P)))),(P=G2?H2(e,n):W2(e,n))&&(u=ia(u,"onBeforeInput"),0<u.length&&(c=new bh("onBeforeInput","beforeinput",null,n,c),h.push({event:c,listeners:u}),c.data=P))}dg(h,t)})}function So(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ia(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=mo(e,n),o!=null&&r.unshift(So(e,o,i)),o=mo(e,t),o!=null&&r.push(So(e,o,i))),e=e.return}return r}function Vr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Nh(e,t,n,r,i){for(var o=t._reactName,s=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,i?(l=mo(n,o),l!=null&&s.unshift(So(n,l,a))):i||(l=mo(n,o),l!=null&&s.push(So(n,l,a)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var o3=/\r\n?/g,s3=/\u0000|\uFFFD/g;function Fh(e){return(typeof e=="string"?e:""+e).replace(o3,`
`).replace(s3,"")}function ms(e,t,n){if(t=Fh(t),Fh(e)!==t&&n)throw Error(L(425))}function oa(){}var Pu=null,Du=null;function Mu(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ru=typeof setTimeout=="function"?setTimeout:void 0,a3=typeof clearTimeout=="function"?clearTimeout:void 0,zh=typeof Promise=="function"?Promise:void 0,l3=typeof queueMicrotask=="function"?queueMicrotask:typeof zh<"u"?function(e){return zh.resolve(null).then(e).catch(u3)}:Ru;function u3(e){setTimeout(function(){throw e})}function Pl(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),yo(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);yo(t)}function zn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Uh(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var ki=Math.random().toString(36).slice(2),en="__reactFiber$"+ki,Eo="__reactProps$"+ki,yn="__reactContainer$"+ki,Ou="__reactEvents$"+ki,c3="__reactListeners$"+ki,f3="__reactHandles$"+ki;function vr(e){var t=e[en];if(t)return t;for(var n=e.parentNode;n;){if(t=n[yn]||n[en]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Uh(e);e!==null;){if(n=e[en])return n;e=Uh(e)}return t}e=n,n=e.parentNode}return null}function Ho(e){return e=e[en]||e[yn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Gr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(L(33))}function Ba(e){return e[Eo]||null}var Iu=[],Hr=-1;function Jn(e){return{current:e}}function me(e){0>Hr||(e.current=Iu[Hr],Iu[Hr]=null,Hr--)}function fe(e,t){Hr++,Iu[Hr]=e.current,e.current=t}var Yn={},We=Jn(Yn),et=Jn(!1),Ar=Yn;function hi(e,t){var n=e.type.contextTypes;if(!n)return Yn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function tt(e){return e=e.childContextTypes,e!=null}function sa(){me(et),me(We)}function jh(e,t,n){if(We.current!==Yn)throw Error(L(168));fe(We,t),fe(et,n)}function mg(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(L(108,Yy(e)||"Unknown",i));return _e({},n,r)}function aa(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Yn,Ar=We.current,fe(We,e),fe(et,et.current),!0}function Gh(e,t,n){var r=e.stateNode;if(!r)throw Error(L(169));n?(e=mg(e,t,Ar),r.__reactInternalMemoizedMergedChildContext=e,me(et),me(We),fe(We,e)):me(et),fe(et,n)}var un=null,Na=!1,Dl=!1;function gg(e){un===null?un=[e]:un.push(e)}function h3(e){Na=!0,gg(e)}function er(){if(!Dl&&un!==null){Dl=!0;var e=0,t=le;try{var n=un;for(le=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}un=null,Na=!1}catch(i){throw un!==null&&(un=un.slice(e+1)),Um(Fc,er),i}finally{le=t,Dl=!1}}return null}var Wr=[],Kr=0,la=null,ua=0,bt=[],Tt=0,Cr=null,cn=1,fn="";function cr(e,t){Wr[Kr++]=ua,Wr[Kr++]=la,la=e,ua=t}function vg(e,t,n){bt[Tt++]=cn,bt[Tt++]=fn,bt[Tt++]=Cr,Cr=e;var r=cn;e=fn;var i=32-Wt(r)-1;r&=~(1<<i),n+=1;var o=32-Wt(t)+i;if(30<o){var s=i-i%5;o=(r&(1<<s)-1).toString(32),r>>=s,i-=s,cn=1<<32-Wt(t)+i|n<<i|r,fn=o+e}else cn=1<<o|n<<i|r,fn=e}function $c(e){e.return!==null&&(cr(e,1),vg(e,1,0))}function Yc(e){for(;e===la;)la=Wr[--Kr],Wr[Kr]=null,ua=Wr[--Kr],Wr[Kr]=null;for(;e===Cr;)Cr=bt[--Tt],bt[Tt]=null,fn=bt[--Tt],bt[Tt]=null,cn=bt[--Tt],bt[Tt]=null}var ct=null,ut=null,ve=!1,Ut=null;function yg(e,t){var n=kt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Hh(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,ct=e,ut=zn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,ct=e,ut=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Cr!==null?{id:cn,overflow:fn}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=kt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,ct=e,ut=null,!0):!1;default:return!1}}function Lu(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Vu(e){if(ve){var t=ut;if(t){var n=t;if(!Hh(e,t)){if(Lu(e))throw Error(L(418));t=zn(n.nextSibling);var r=ct;t&&Hh(e,t)?yg(r,n):(e.flags=e.flags&-4097|2,ve=!1,ct=e)}}else{if(Lu(e))throw Error(L(418));e.flags=e.flags&-4097|2,ve=!1,ct=e}}}function Wh(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;ct=e}function gs(e){if(e!==ct)return!1;if(!ve)return Wh(e),ve=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Mu(e.type,e.memoizedProps)),t&&(t=ut)){if(Lu(e))throw wg(),Error(L(418));for(;t;)yg(e,t),t=zn(t.nextSibling)}if(Wh(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(L(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){ut=zn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}ut=null}}else ut=ct?zn(e.stateNode.nextSibling):null;return!0}function wg(){for(var e=ut;e;)e=zn(e.nextSibling)}function di(){ut=ct=null,ve=!1}function Zc(e){Ut===null?Ut=[e]:Ut.push(e)}var d3=En.ReactCurrentBatchConfig;function Fi(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(L(309));var r=n.stateNode}if(!r)throw Error(L(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(s){var a=i.refs;s===null?delete a[o]:a[o]=s},t._stringRef=o,t)}if(typeof e!="string")throw Error(L(284));if(!n._owner)throw Error(L(290,e))}return e}function vs(e,t){throw e=Object.prototype.toString.call(t),Error(L(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Kh(e){var t=e._init;return t(e._payload)}function xg(e){function t(y,w){if(e){var _=y.deletions;_===null?(y.deletions=[w],y.flags|=16):_.push(w)}}function n(y,w){if(!e)return null;for(;w!==null;)t(y,w),w=w.sibling;return null}function r(y,w){for(y=new Map;w!==null;)w.key!==null?y.set(w.key,w):y.set(w.index,w),w=w.sibling;return y}function i(y,w){return y=Hn(y,w),y.index=0,y.sibling=null,y}function o(y,w,_){return y.index=_,e?(_=y.alternate,_!==null?(_=_.index,_<w?(y.flags|=2,w):_):(y.flags|=2,w)):(y.flags|=1048576,w)}function s(y){return e&&y.alternate===null&&(y.flags|=2),y}function a(y,w,_,E){return w===null||w.tag!==6?(w=Bl(_,y.mode,E),w.return=y,w):(w=i(w,_),w.return=y,w)}function l(y,w,_,E){var b=_.type;return b===Fr?c(y,w,_.props.children,E,_.key):w!==null&&(w.elementType===b||typeof b=="object"&&b!==null&&b.$$typeof===Pn&&Kh(b)===w.type)?(E=i(w,_.props),E.ref=Fi(y,w,_),E.return=y,E):(E=Hs(_.type,_.key,_.props,null,y.mode,E),E.ref=Fi(y,w,_),E.return=y,E)}function u(y,w,_,E){return w===null||w.tag!==4||w.stateNode.containerInfo!==_.containerInfo||w.stateNode.implementation!==_.implementation?(w=Nl(_,y.mode,E),w.return=y,w):(w=i(w,_.children||[]),w.return=y,w)}function c(y,w,_,E,b){return w===null||w.tag!==7?(w=Sr(_,y.mode,E,b),w.return=y,w):(w=i(w,_),w.return=y,w)}function h(y,w,_){if(typeof w=="string"&&w!==""||typeof w=="number")return w=Bl(""+w,y.mode,_),w.return=y,w;if(typeof w=="object"&&w!==null){switch(w.$$typeof){case ss:return _=Hs(w.type,w.key,w.props,null,y.mode,_),_.ref=Fi(y,null,w),_.return=y,_;case Nr:return w=Nl(w,y.mode,_),w.return=y,w;case Pn:var E=w._init;return h(y,E(w._payload),_)}if(Hi(w)||Ii(w))return w=Sr(w,y.mode,_,null),w.return=y,w;vs(y,w)}return null}function f(y,w,_,E){var b=w!==null?w.key:null;if(typeof _=="string"&&_!==""||typeof _=="number")return b!==null?null:a(y,w,""+_,E);if(typeof _=="object"&&_!==null){switch(_.$$typeof){case ss:return _.key===b?l(y,w,_,E):null;case Nr:return _.key===b?u(y,w,_,E):null;case Pn:return b=_._init,f(y,w,b(_._payload),E)}if(Hi(_)||Ii(_))return b!==null?null:c(y,w,_,E,null);vs(y,_)}return null}function v(y,w,_,E,b){if(typeof E=="string"&&E!==""||typeof E=="number")return y=y.get(_)||null,a(w,y,""+E,b);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case ss:return y=y.get(E.key===null?_:E.key)||null,l(w,y,E,b);case Nr:return y=y.get(E.key===null?_:E.key)||null,u(w,y,E,b);case Pn:var k=E._init;return v(y,w,_,k(E._payload),b)}if(Hi(E)||Ii(E))return y=y.get(_)||null,c(w,y,E,b,null);vs(w,E)}return null}function p(y,w,_,E){for(var b=null,k=null,P=w,C=w=0,D=null;P!==null&&C<_.length;C++){P.index>C?(D=P,P=null):D=P.sibling;var R=f(y,P,_[C],E);if(R===null){P===null&&(P=D);break}e&&P&&R.alternate===null&&t(y,P),w=o(R,w,C),k===null?b=R:k.sibling=R,k=R,P=D}if(C===_.length)return n(y,P),ve&&cr(y,C),b;if(P===null){for(;C<_.length;C++)P=h(y,_[C],E),P!==null&&(w=o(P,w,C),k===null?b=P:k.sibling=P,k=P);return ve&&cr(y,C),b}for(P=r(y,P);C<_.length;C++)D=v(P,y,C,_[C],E),D!==null&&(e&&D.alternate!==null&&P.delete(D.key===null?C:D.key),w=o(D,w,C),k===null?b=D:k.sibling=D,k=D);return e&&P.forEach(function(B){return t(y,B)}),ve&&cr(y,C),b}function g(y,w,_,E){var b=Ii(_);if(typeof b!="function")throw Error(L(150));if(_=b.call(_),_==null)throw Error(L(151));for(var k=b=null,P=w,C=w=0,D=null,R=_.next();P!==null&&!R.done;C++,R=_.next()){P.index>C?(D=P,P=null):D=P.sibling;var B=f(y,P,R.value,E);if(B===null){P===null&&(P=D);break}e&&P&&B.alternate===null&&t(y,P),w=o(B,w,C),k===null?b=B:k.sibling=B,k=B,P=D}if(R.done)return n(y,P),ve&&cr(y,C),b;if(P===null){for(;!R.done;C++,R=_.next())R=h(y,R.value,E),R!==null&&(w=o(R,w,C),k===null?b=R:k.sibling=R,k=R);return ve&&cr(y,C),b}for(P=r(y,P);!R.done;C++,R=_.next())R=v(P,y,C,R.value,E),R!==null&&(e&&R.alternate!==null&&P.delete(R.key===null?C:R.key),w=o(R,w,C),k===null?b=R:k.sibling=R,k=R);return e&&P.forEach(function(Y){return t(y,Y)}),ve&&cr(y,C),b}function S(y,w,_,E){if(typeof _=="object"&&_!==null&&_.type===Fr&&_.key===null&&(_=_.props.children),typeof _=="object"&&_!==null){switch(_.$$typeof){case ss:e:{for(var b=_.key,k=w;k!==null;){if(k.key===b){if(b=_.type,b===Fr){if(k.tag===7){n(y,k.sibling),w=i(k,_.props.children),w.return=y,y=w;break e}}else if(k.elementType===b||typeof b=="object"&&b!==null&&b.$$typeof===Pn&&Kh(b)===k.type){n(y,k.sibling),w=i(k,_.props),w.ref=Fi(y,k,_),w.return=y,y=w;break e}n(y,k);break}else t(y,k);k=k.sibling}_.type===Fr?(w=Sr(_.props.children,y.mode,E,_.key),w.return=y,y=w):(E=Hs(_.type,_.key,_.props,null,y.mode,E),E.ref=Fi(y,w,_),E.return=y,y=E)}return s(y);case Nr:e:{for(k=_.key;w!==null;){if(w.key===k)if(w.tag===4&&w.stateNode.containerInfo===_.containerInfo&&w.stateNode.implementation===_.implementation){n(y,w.sibling),w=i(w,_.children||[]),w.return=y,y=w;break e}else{n(y,w);break}else t(y,w);w=w.sibling}w=Nl(_,y.mode,E),w.return=y,y=w}return s(y);case Pn:return k=_._init,S(y,w,k(_._payload),E)}if(Hi(_))return p(y,w,_,E);if(Ii(_))return g(y,w,_,E);vs(y,_)}return typeof _=="string"&&_!==""||typeof _=="number"?(_=""+_,w!==null&&w.tag===6?(n(y,w.sibling),w=i(w,_),w.return=y,y=w):(n(y,w),w=Bl(_,y.mode,E),w.return=y,y=w),s(y)):n(y,w)}return S}var pi=xg(!0),_g=xg(!1),ca=Jn(null),fa=null,qr=null,Xc=null;function Qc(){Xc=qr=fa=null}function Jc(e){var t=ca.current;me(ca),e._currentValue=t}function Bu(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function ai(e,t){fa=e,Xc=qr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Je=!0),e.firstContext=null)}function Dt(e){var t=e._currentValue;if(Xc!==e)if(e={context:e,memoizedValue:t,next:null},qr===null){if(fa===null)throw Error(L(308));qr=e,fa.dependencies={lanes:0,firstContext:e}}else qr=qr.next=e;return t}var yr=null;function ef(e){yr===null?yr=[e]:yr.push(e)}function Sg(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,ef(t)):(n.next=i.next,i.next=n),t.interleaved=n,wn(e,r)}function wn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Dn=!1;function tf(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Eg(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function dn(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Un(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,ne&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,wn(e,n)}return i=r.interleaved,i===null?(t.next=t,ef(r)):(t.next=i.next,i.next=t),r.interleaved=t,wn(e,n)}function Ns(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,zc(e,n)}}function qh(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=s:o=o.next=s,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ha(e,t,n,r){var i=e.updateQueue;Dn=!1;var o=i.firstBaseUpdate,s=i.lastBaseUpdate,a=i.shared.pending;if(a!==null){i.shared.pending=null;var l=a,u=l.next;l.next=null,s===null?o=u:s.next=u,s=l;var c=e.alternate;c!==null&&(c=c.updateQueue,a=c.lastBaseUpdate,a!==s&&(a===null?c.firstBaseUpdate=u:a.next=u,c.lastBaseUpdate=l))}if(o!==null){var h=i.baseState;s=0,c=u=l=null,a=o;do{var f=a.lane,v=a.eventTime;if((r&f)===f){c!==null&&(c=c.next={eventTime:v,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var p=e,g=a;switch(f=t,v=n,g.tag){case 1:if(p=g.payload,typeof p=="function"){h=p.call(v,h,f);break e}h=p;break e;case 3:p.flags=p.flags&-65537|128;case 0:if(p=g.payload,f=typeof p=="function"?p.call(v,h,f):p,f==null)break e;h=_e({},h,f);break e;case 2:Dn=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,f=i.effects,f===null?i.effects=[a]:f.push(a))}else v={eventTime:v,lane:f,tag:a.tag,payload:a.payload,callback:a.callback,next:null},c===null?(u=c=v,l=h):c=c.next=v,s|=f;if(a=a.next,a===null){if(a=i.shared.pending,a===null)break;f=a,a=f.next,f.next=null,i.lastBaseUpdate=f,i.shared.pending=null}}while(!0);if(c===null&&(l=h),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do s|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);Tr|=s,e.lanes=s,e.memoizedState=h}}function $h(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(L(191,i));i.call(r)}}}var Wo={},nn=Jn(Wo),Ao=Jn(Wo),Co=Jn(Wo);function wr(e){if(e===Wo)throw Error(L(174));return e}function nf(e,t){switch(fe(Co,t),fe(Ao,e),fe(nn,Wo),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:vu(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=vu(t,e)}me(nn),fe(nn,t)}function mi(){me(nn),me(Ao),me(Co)}function Ag(e){wr(Co.current);var t=wr(nn.current),n=vu(t,e.type);t!==n&&(fe(Ao,e),fe(nn,n))}function rf(e){Ao.current===e&&(me(nn),me(Ao))}var ye=Jn(0);function da(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Ml=[];function of(){for(var e=0;e<Ml.length;e++)Ml[e]._workInProgressVersionPrimary=null;Ml.length=0}var Fs=En.ReactCurrentDispatcher,Rl=En.ReactCurrentBatchConfig,br=0,xe=null,Te=null,Re=null,pa=!1,to=!1,bo=0,p3=0;function Fe(){throw Error(L(321))}function sf(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!qt(e[n],t[n]))return!1;return!0}function af(e,t,n,r,i,o){if(br=o,xe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Fs.current=e===null||e.memoizedState===null?y3:w3,e=n(r,i),to){o=0;do{if(to=!1,bo=0,25<=o)throw Error(L(301));o+=1,Re=Te=null,t.updateQueue=null,Fs.current=x3,e=n(r,i)}while(to)}if(Fs.current=ma,t=Te!==null&&Te.next!==null,br=0,Re=Te=xe=null,pa=!1,t)throw Error(L(300));return e}function lf(){var e=bo!==0;return bo=0,e}function Jt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Re===null?xe.memoizedState=Re=e:Re=Re.next=e,Re}function Mt(){if(Te===null){var e=xe.alternate;e=e!==null?e.memoizedState:null}else e=Te.next;var t=Re===null?xe.memoizedState:Re.next;if(t!==null)Re=t,Te=e;else{if(e===null)throw Error(L(310));Te=e,e={memoizedState:Te.memoizedState,baseState:Te.baseState,baseQueue:Te.baseQueue,queue:Te.queue,next:null},Re===null?xe.memoizedState=Re=e:Re=Re.next=e}return Re}function To(e,t){return typeof t=="function"?t(e):t}function Ol(e){var t=Mt(),n=t.queue;if(n===null)throw Error(L(311));n.lastRenderedReducer=e;var r=Te,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var s=i.next;i.next=o.next,o.next=s}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var a=s=null,l=null,u=o;do{var c=u.lane;if((br&c)===c)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var h={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=h,s=r):l=l.next=h,xe.lanes|=c,Tr|=c}u=u.next}while(u!==null&&u!==o);l===null?s=r:l.next=a,qt(r,t.memoizedState)||(Je=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,xe.lanes|=o,Tr|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Il(e){var t=Mt(),n=t.queue;if(n===null)throw Error(L(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var s=i=i.next;do o=e(o,s.action),s=s.next;while(s!==i);qt(o,t.memoizedState)||(Je=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Cg(){}function bg(e,t){var n=xe,r=Mt(),i=t(),o=!qt(r.memoizedState,i);if(o&&(r.memoizedState=i,Je=!0),r=r.queue,uf(Pg.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||Re!==null&&Re.memoizedState.tag&1){if(n.flags|=2048,ko(9,kg.bind(null,n,r,i,t),void 0,null),Oe===null)throw Error(L(349));br&30||Tg(n,t,i)}return i}function Tg(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=xe.updateQueue,t===null?(t={lastEffect:null,stores:null},xe.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function kg(e,t,n,r){t.value=n,t.getSnapshot=r,Dg(t)&&Mg(e)}function Pg(e,t,n){return n(function(){Dg(t)&&Mg(e)})}function Dg(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!qt(e,n)}catch{return!0}}function Mg(e){var t=wn(e,1);t!==null&&Kt(t,e,1,-1)}function Yh(e){var t=Jt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:To,lastRenderedState:e},t.queue=e,e=e.dispatch=v3.bind(null,xe,e),[t.memoizedState,e]}function ko(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=xe.updateQueue,t===null?(t={lastEffect:null,stores:null},xe.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Rg(){return Mt().memoizedState}function zs(e,t,n,r){var i=Jt();xe.flags|=e,i.memoizedState=ko(1|t,n,void 0,r===void 0?null:r)}function Fa(e,t,n,r){var i=Mt();r=r===void 0?null:r;var o=void 0;if(Te!==null){var s=Te.memoizedState;if(o=s.destroy,r!==null&&sf(r,s.deps)){i.memoizedState=ko(t,n,o,r);return}}xe.flags|=e,i.memoizedState=ko(1|t,n,o,r)}function Zh(e,t){return zs(8390656,8,e,t)}function uf(e,t){return Fa(2048,8,e,t)}function Og(e,t){return Fa(4,2,e,t)}function Ig(e,t){return Fa(4,4,e,t)}function Lg(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Vg(e,t,n){return n=n!=null?n.concat([e]):null,Fa(4,4,Lg.bind(null,t,e),n)}function cf(){}function Bg(e,t){var n=Mt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&sf(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ng(e,t){var n=Mt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&sf(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Fg(e,t,n){return br&21?(qt(n,t)||(n=Hm(),xe.lanes|=n,Tr|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Je=!0),e.memoizedState=n)}function m3(e,t){var n=le;le=n!==0&&4>n?n:4,e(!0);var r=Rl.transition;Rl.transition={};try{e(!1),t()}finally{le=n,Rl.transition=r}}function zg(){return Mt().memoizedState}function g3(e,t,n){var r=Gn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Ug(e))jg(t,n);else if(n=Sg(e,t,n,r),n!==null){var i=Ye();Kt(n,e,r,i),Gg(n,t,r)}}function v3(e,t,n){var r=Gn(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ug(e))jg(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var s=t.lastRenderedState,a=o(s,n);if(i.hasEagerState=!0,i.eagerState=a,qt(a,s)){var l=t.interleaved;l===null?(i.next=i,ef(t)):(i.next=l.next,l.next=i),t.interleaved=i;return}}catch{}finally{}n=Sg(e,t,i,r),n!==null&&(i=Ye(),Kt(n,e,r,i),Gg(n,t,r))}}function Ug(e){var t=e.alternate;return e===xe||t!==null&&t===xe}function jg(e,t){to=pa=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Gg(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,zc(e,n)}}var ma={readContext:Dt,useCallback:Fe,useContext:Fe,useEffect:Fe,useImperativeHandle:Fe,useInsertionEffect:Fe,useLayoutEffect:Fe,useMemo:Fe,useReducer:Fe,useRef:Fe,useState:Fe,useDebugValue:Fe,useDeferredValue:Fe,useTransition:Fe,useMutableSource:Fe,useSyncExternalStore:Fe,useId:Fe,unstable_isNewReconciler:!1},y3={readContext:Dt,useCallback:function(e,t){return Jt().memoizedState=[e,t===void 0?null:t],e},useContext:Dt,useEffect:Zh,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,zs(4194308,4,Lg.bind(null,t,e),n)},useLayoutEffect:function(e,t){return zs(4194308,4,e,t)},useInsertionEffect:function(e,t){return zs(4,2,e,t)},useMemo:function(e,t){var n=Jt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Jt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=g3.bind(null,xe,e),[r.memoizedState,e]},useRef:function(e){var t=Jt();return e={current:e},t.memoizedState=e},useState:Yh,useDebugValue:cf,useDeferredValue:function(e){return Jt().memoizedState=e},useTransition:function(){var e=Yh(!1),t=e[0];return e=m3.bind(null,e[1]),Jt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=xe,i=Jt();if(ve){if(n===void 0)throw Error(L(407));n=n()}else{if(n=t(),Oe===null)throw Error(L(349));br&30||Tg(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,Zh(Pg.bind(null,r,o,e),[e]),r.flags|=2048,ko(9,kg.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=Jt(),t=Oe.identifierPrefix;if(ve){var n=fn,r=cn;n=(r&~(1<<32-Wt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=bo++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=p3++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},w3={readContext:Dt,useCallback:Bg,useContext:Dt,useEffect:uf,useImperativeHandle:Vg,useInsertionEffect:Og,useLayoutEffect:Ig,useMemo:Ng,useReducer:Ol,useRef:Rg,useState:function(){return Ol(To)},useDebugValue:cf,useDeferredValue:function(e){var t=Mt();return Fg(t,Te.memoizedState,e)},useTransition:function(){var e=Ol(To)[0],t=Mt().memoizedState;return[e,t]},useMutableSource:Cg,useSyncExternalStore:bg,useId:zg,unstable_isNewReconciler:!1},x3={readContext:Dt,useCallback:Bg,useContext:Dt,useEffect:uf,useImperativeHandle:Vg,useInsertionEffect:Og,useLayoutEffect:Ig,useMemo:Ng,useReducer:Il,useRef:Rg,useState:function(){return Il(To)},useDebugValue:cf,useDeferredValue:function(e){var t=Mt();return Te===null?t.memoizedState=e:Fg(t,Te.memoizedState,e)},useTransition:function(){var e=Il(To)[0],t=Mt().memoizedState;return[e,t]},useMutableSource:Cg,useSyncExternalStore:bg,useId:zg,unstable_isNewReconciler:!1};function Ft(e,t){if(e&&e.defaultProps){t=_e({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Nu(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:_e({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var za={isMounted:function(e){return(e=e._reactInternals)?Or(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ye(),i=Gn(e),o=dn(r,i);o.payload=t,n!=null&&(o.callback=n),t=Un(e,o,i),t!==null&&(Kt(t,e,i,r),Ns(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ye(),i=Gn(e),o=dn(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Un(e,o,i),t!==null&&(Kt(t,e,i,r),Ns(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ye(),r=Gn(e),i=dn(n,r);i.tag=2,t!=null&&(i.callback=t),t=Un(e,i,r),t!==null&&(Kt(t,e,r,n),Ns(t,e,r))}};function Xh(e,t,n,r,i,o,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,s):t.prototype&&t.prototype.isPureReactComponent?!xo(n,r)||!xo(i,o):!0}function Hg(e,t,n){var r=!1,i=Yn,o=t.contextType;return typeof o=="object"&&o!==null?o=Dt(o):(i=tt(t)?Ar:We.current,r=t.contextTypes,o=(r=r!=null)?hi(e,i):Yn),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=za,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function Qh(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&za.enqueueReplaceState(t,t.state,null)}function Fu(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},tf(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=Dt(o):(o=tt(t)?Ar:We.current,i.context=hi(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Nu(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&za.enqueueReplaceState(i,i.state,null),ha(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function gi(e,t){try{var n="",r=t;do n+=$y(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function Ll(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function zu(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var _3=typeof WeakMap=="function"?WeakMap:Map;function Wg(e,t,n){n=dn(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){va||(va=!0,Zu=r),zu(e,t)},n}function Kg(e,t,n){n=dn(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){zu(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){zu(e,t),typeof r!="function"&&(jn===null?jn=new Set([this]):jn.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function Jh(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new _3;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=L3.bind(null,e,t,n),t.then(e,e))}function ed(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function td(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=dn(-1,1),t.tag=2,Un(n,t,1))),n.lanes|=1),e)}var S3=En.ReactCurrentOwner,Je=!1;function qe(e,t,n,r){t.child=e===null?_g(t,null,n,r):pi(t,e.child,n,r)}function nd(e,t,n,r,i){n=n.render;var o=t.ref;return ai(t,i),r=af(e,t,n,r,o,i),n=lf(),e!==null&&!Je?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,xn(e,t,i)):(ve&&n&&$c(t),t.flags|=1,qe(e,t,r,i),t.child)}function rd(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!yf(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,qg(e,t,o,r,i)):(e=Hs(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var s=o.memoizedProps;if(n=n.compare,n=n!==null?n:xo,n(s,r)&&e.ref===t.ref)return xn(e,t,i)}return t.flags|=1,e=Hn(o,r),e.ref=t.ref,e.return=t,t.child=e}function qg(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(xo(o,r)&&e.ref===t.ref)if(Je=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(Je=!0);else return t.lanes=e.lanes,xn(e,t,i)}return Uu(e,t,n,r,i)}function $g(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},fe(Yr,at),at|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,fe(Yr,at),at|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,fe(Yr,at),at|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,fe(Yr,at),at|=r;return qe(e,t,i,n),t.child}function Yg(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Uu(e,t,n,r,i){var o=tt(n)?Ar:We.current;return o=hi(t,o),ai(t,i),n=af(e,t,n,r,o,i),r=lf(),e!==null&&!Je?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,xn(e,t,i)):(ve&&r&&$c(t),t.flags|=1,qe(e,t,n,i),t.child)}function id(e,t,n,r,i){if(tt(n)){var o=!0;aa(t)}else o=!1;if(ai(t,i),t.stateNode===null)Us(e,t),Hg(t,n,r),Fu(t,n,r,i),r=!0;else if(e===null){var s=t.stateNode,a=t.memoizedProps;s.props=a;var l=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=Dt(u):(u=tt(n)?Ar:We.current,u=hi(t,u));var c=n.getDerivedStateFromProps,h=typeof c=="function"||typeof s.getSnapshotBeforeUpdate=="function";h||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==r||l!==u)&&Qh(t,s,r,u),Dn=!1;var f=t.memoizedState;s.state=f,ha(t,r,s,i),l=t.memoizedState,a!==r||f!==l||et.current||Dn?(typeof c=="function"&&(Nu(t,n,c,r),l=t.memoizedState),(a=Dn||Xh(t,n,a,r,f,l,u))?(h||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),s.props=r,s.state=l,s.context=u,r=a):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,Eg(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:Ft(t.type,a),s.props=u,h=t.pendingProps,f=s.context,l=n.contextType,typeof l=="object"&&l!==null?l=Dt(l):(l=tt(n)?Ar:We.current,l=hi(t,l));var v=n.getDerivedStateFromProps;(c=typeof v=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==h||f!==l)&&Qh(t,s,r,l),Dn=!1,f=t.memoizedState,s.state=f,ha(t,r,s,i);var p=t.memoizedState;a!==h||f!==p||et.current||Dn?(typeof v=="function"&&(Nu(t,n,v,r),p=t.memoizedState),(u=Dn||Xh(t,n,u,r,f,p,l)||!1)?(c||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,p,l),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,p,l)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),s.props=r,s.state=p,s.context=l,r=u):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return ju(e,t,n,r,o,i)}function ju(e,t,n,r,i,o){Yg(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return i&&Gh(t,n,!1),xn(e,t,o);r=t.stateNode,S3.current=t;var a=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=pi(t,e.child,null,o),t.child=pi(t,null,a,o)):qe(e,t,a,o),t.memoizedState=r.state,i&&Gh(t,n,!0),t.child}function Zg(e){var t=e.stateNode;t.pendingContext?jh(e,t.pendingContext,t.pendingContext!==t.context):t.context&&jh(e,t.context,!1),nf(e,t.containerInfo)}function od(e,t,n,r,i){return di(),Zc(i),t.flags|=256,qe(e,t,n,r),t.child}var Gu={dehydrated:null,treeContext:null,retryLane:0};function Hu(e){return{baseLanes:e,cachePool:null,transitions:null}}function Xg(e,t,n){var r=t.pendingProps,i=ye.current,o=!1,s=(t.flags&128)!==0,a;if((a=s)||(a=e!==null&&e.memoizedState===null?!1:(i&2)!==0),a?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),fe(ye,i&1),e===null)return Vu(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,o?(r=t.mode,o=t.child,s={mode:"hidden",children:s},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=s):o=Ga(s,r,0,null),e=Sr(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=Hu(n),t.memoizedState=Gu,e):ff(t,s));if(i=e.memoizedState,i!==null&&(a=i.dehydrated,a!==null))return E3(e,t,s,r,a,i,n);if(o){o=r.fallback,s=t.mode,i=e.child,a=i.sibling;var l={mode:"hidden",children:r.children};return!(s&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=Hn(i,l),r.subtreeFlags=i.subtreeFlags&14680064),a!==null?o=Hn(a,o):(o=Sr(o,s,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,s=e.child.memoizedState,s=s===null?Hu(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=Gu,r}return o=e.child,e=o.sibling,r=Hn(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function ff(e,t){return t=Ga({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function ys(e,t,n,r){return r!==null&&Zc(r),pi(t,e.child,null,n),e=ff(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function E3(e,t,n,r,i,o,s){if(n)return t.flags&256?(t.flags&=-257,r=Ll(Error(L(422))),ys(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=Ga({mode:"visible",children:r.children},i,0,null),o=Sr(o,i,s,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&pi(t,e.child,null,s),t.child.memoizedState=Hu(s),t.memoizedState=Gu,o);if(!(t.mode&1))return ys(e,t,s,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var a=r.dgst;return r=a,o=Error(L(419)),r=Ll(o,r,void 0),ys(e,t,s,r)}if(a=(s&e.childLanes)!==0,Je||a){if(r=Oe,r!==null){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|s)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,wn(e,i),Kt(r,e,i,-1))}return vf(),r=Ll(Error(L(421))),ys(e,t,s,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=V3.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,ut=zn(i.nextSibling),ct=t,ve=!0,Ut=null,e!==null&&(bt[Tt++]=cn,bt[Tt++]=fn,bt[Tt++]=Cr,cn=e.id,fn=e.overflow,Cr=t),t=ff(t,r.children),t.flags|=4096,t)}function sd(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Bu(e.return,t,n)}function Vl(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function Qg(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(qe(e,t,r.children,n),r=ye.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&sd(e,n,t);else if(e.tag===19)sd(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(fe(ye,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&da(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Vl(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&da(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Vl(t,!0,n,null,o);break;case"together":Vl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Us(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function xn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Tr|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(L(153));if(t.child!==null){for(e=t.child,n=Hn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Hn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function A3(e,t,n){switch(t.tag){case 3:Zg(t),di();break;case 5:Ag(t);break;case 1:tt(t.type)&&aa(t);break;case 4:nf(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;fe(ca,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(fe(ye,ye.current&1),t.flags|=128,null):n&t.child.childLanes?Xg(e,t,n):(fe(ye,ye.current&1),e=xn(e,t,n),e!==null?e.sibling:null);fe(ye,ye.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Qg(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),fe(ye,ye.current),r)break;return null;case 22:case 23:return t.lanes=0,$g(e,t,n)}return xn(e,t,n)}var Jg,Wu,e1,t1;Jg=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Wu=function(){};e1=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,wr(nn.current);var o=null;switch(n){case"input":i=du(e,i),r=du(e,r),o=[];break;case"select":i=_e({},i,{value:void 0}),r=_e({},r,{value:void 0}),o=[];break;case"textarea":i=gu(e,i),r=gu(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=oa)}yu(n,r);var s;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var a=i[u];for(s in a)a.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(ho.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in r){var l=r[u];if(a=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(s in a)!a.hasOwnProperty(s)||l&&l.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in l)l.hasOwnProperty(s)&&a[s]!==l[s]&&(n||(n={}),n[s]=l[s])}else n||(o||(o=[]),o.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(o=o||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(o=o||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(ho.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&de("scroll",e),o||a===l||(o=[])):(o=o||[]).push(u,l))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}};t1=function(e,t,n,r){n!==r&&(t.flags|=4)};function zi(e,t){if(!ve)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ze(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function C3(e,t,n){var r=t.pendingProps;switch(Yc(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ze(t),null;case 1:return tt(t.type)&&sa(),ze(t),null;case 3:return r=t.stateNode,mi(),me(et),me(We),of(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(gs(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ut!==null&&(Ju(Ut),Ut=null))),Wu(e,t),ze(t),null;case 5:rf(t);var i=wr(Co.current);if(n=t.type,e!==null&&t.stateNode!=null)e1(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(L(166));return ze(t),null}if(e=wr(nn.current),gs(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[en]=t,r[Eo]=o,e=(t.mode&1)!==0,n){case"dialog":de("cancel",r),de("close",r);break;case"iframe":case"object":case"embed":de("load",r);break;case"video":case"audio":for(i=0;i<Ki.length;i++)de(Ki[i],r);break;case"source":de("error",r);break;case"img":case"image":case"link":de("error",r),de("load",r);break;case"details":de("toggle",r);break;case"input":mh(r,o),de("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},de("invalid",r);break;case"textarea":vh(r,o),de("invalid",r)}yu(n,o),i=null;for(var s in o)if(o.hasOwnProperty(s)){var a=o[s];s==="children"?typeof a=="string"?r.textContent!==a&&(o.suppressHydrationWarning!==!0&&ms(r.textContent,a,e),i=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(o.suppressHydrationWarning!==!0&&ms(r.textContent,a,e),i=["children",""+a]):ho.hasOwnProperty(s)&&a!=null&&s==="onScroll"&&de("scroll",r)}switch(n){case"input":as(r),gh(r,o,!0);break;case"textarea":as(r),yh(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=oa)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Pm(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[en]=t,e[Eo]=r,Jg(e,t,!1,!1),t.stateNode=e;e:{switch(s=wu(n,r),n){case"dialog":de("cancel",e),de("close",e),i=r;break;case"iframe":case"object":case"embed":de("load",e),i=r;break;case"video":case"audio":for(i=0;i<Ki.length;i++)de(Ki[i],e);i=r;break;case"source":de("error",e),i=r;break;case"img":case"image":case"link":de("error",e),de("load",e),i=r;break;case"details":de("toggle",e),i=r;break;case"input":mh(e,r),i=du(e,r),de("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=_e({},r,{value:void 0}),de("invalid",e);break;case"textarea":vh(e,r),i=gu(e,r),de("invalid",e);break;default:i=r}yu(n,i),a=i;for(o in a)if(a.hasOwnProperty(o)){var l=a[o];o==="style"?Rm(e,l):o==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Dm(e,l)):o==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&po(e,l):typeof l=="number"&&po(e,""+l):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(ho.hasOwnProperty(o)?l!=null&&o==="onScroll"&&de("scroll",e):l!=null&&Ic(e,o,l,s))}switch(n){case"input":as(e),gh(e,r,!1);break;case"textarea":as(e),yh(e);break;case"option":r.value!=null&&e.setAttribute("value",""+$n(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?ri(e,!!r.multiple,o,!1):r.defaultValue!=null&&ri(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=oa)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ze(t),null;case 6:if(e&&t.stateNode!=null)t1(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(L(166));if(n=wr(Co.current),wr(nn.current),gs(t)){if(r=t.stateNode,n=t.memoizedProps,r[en]=t,(o=r.nodeValue!==n)&&(e=ct,e!==null))switch(e.tag){case 3:ms(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&ms(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[en]=t,t.stateNode=r}return ze(t),null;case 13:if(me(ye),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ve&&ut!==null&&t.mode&1&&!(t.flags&128))wg(),di(),t.flags|=98560,o=!1;else if(o=gs(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(L(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(L(317));o[en]=t}else di(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ze(t),o=!1}else Ut!==null&&(Ju(Ut),Ut=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ye.current&1?ke===0&&(ke=3):vf())),t.updateQueue!==null&&(t.flags|=4),ze(t),null);case 4:return mi(),Wu(e,t),e===null&&_o(t.stateNode.containerInfo),ze(t),null;case 10:return Jc(t.type._context),ze(t),null;case 17:return tt(t.type)&&sa(),ze(t),null;case 19:if(me(ye),o=t.memoizedState,o===null)return ze(t),null;if(r=(t.flags&128)!==0,s=o.rendering,s===null)if(r)zi(o,!1);else{if(ke!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=da(e),s!==null){for(t.flags|=128,zi(o,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,s=o.alternate,s===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return fe(ye,ye.current&1|2),t.child}e=e.sibling}o.tail!==null&&Ae()>vi&&(t.flags|=128,r=!0,zi(o,!1),t.lanes=4194304)}else{if(!r)if(e=da(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),zi(o,!0),o.tail===null&&o.tailMode==="hidden"&&!s.alternate&&!ve)return ze(t),null}else 2*Ae()-o.renderingStartTime>vi&&n!==1073741824&&(t.flags|=128,r=!0,zi(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(n=o.last,n!==null?n.sibling=s:t.child=s,o.last=s)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=Ae(),t.sibling=null,n=ye.current,fe(ye,r?n&1|2:n&1),t):(ze(t),null);case 22:case 23:return gf(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?at&1073741824&&(ze(t),t.subtreeFlags&6&&(t.flags|=8192)):ze(t),null;case 24:return null;case 25:return null}throw Error(L(156,t.tag))}function b3(e,t){switch(Yc(t),t.tag){case 1:return tt(t.type)&&sa(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return mi(),me(et),me(We),of(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return rf(t),null;case 13:if(me(ye),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(L(340));di()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return me(ye),null;case 4:return mi(),null;case 10:return Jc(t.type._context),null;case 22:case 23:return gf(),null;case 24:return null;default:return null}}var ws=!1,je=!1,T3=typeof WeakSet=="function"?WeakSet:Set,U=null;function $r(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Se(e,t,r)}else n.current=null}function Ku(e,t,n){try{n()}catch(r){Se(e,t,r)}}var ad=!1;function k3(e,t){if(Pu=na,e=sg(),qc(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var s=0,a=-1,l=-1,u=0,c=0,h=e,f=null;t:for(;;){for(var v;h!==n||i!==0&&h.nodeType!==3||(a=s+i),h!==o||r!==0&&h.nodeType!==3||(l=s+r),h.nodeType===3&&(s+=h.nodeValue.length),(v=h.firstChild)!==null;)f=h,h=v;for(;;){if(h===e)break t;if(f===n&&++u===i&&(a=s),f===o&&++c===r&&(l=s),(v=h.nextSibling)!==null)break;h=f,f=h.parentNode}h=v}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(Du={focusedElem:e,selectionRange:n},na=!1,U=t;U!==null;)if(t=U,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,U=e;else for(;U!==null;){t=U;try{var p=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(p!==null){var g=p.memoizedProps,S=p.memoizedState,y=t.stateNode,w=y.getSnapshotBeforeUpdate(t.elementType===t.type?g:Ft(t.type,g),S);y.__reactInternalSnapshotBeforeUpdate=w}break;case 3:var _=t.stateNode.containerInfo;_.nodeType===1?_.textContent="":_.nodeType===9&&_.documentElement&&_.removeChild(_.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(L(163))}}catch(E){Se(t,t.return,E)}if(e=t.sibling,e!==null){e.return=t.return,U=e;break}U=t.return}return p=ad,ad=!1,p}function no(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&Ku(t,n,o)}i=i.next}while(i!==r)}}function Ua(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function qu(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function n1(e){var t=e.alternate;t!==null&&(e.alternate=null,n1(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[en],delete t[Eo],delete t[Ou],delete t[c3],delete t[f3])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function r1(e){return e.tag===5||e.tag===3||e.tag===4}function ld(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||r1(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function $u(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=oa));else if(r!==4&&(e=e.child,e!==null))for($u(e,t,n),e=e.sibling;e!==null;)$u(e,t,n),e=e.sibling}function Yu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Yu(e,t,n),e=e.sibling;e!==null;)Yu(e,t,n),e=e.sibling}var Le=null,zt=!1;function bn(e,t,n){for(n=n.child;n!==null;)i1(e,t,n),n=n.sibling}function i1(e,t,n){if(tn&&typeof tn.onCommitFiberUnmount=="function")try{tn.onCommitFiberUnmount(Oa,n)}catch{}switch(n.tag){case 5:je||$r(n,t);case 6:var r=Le,i=zt;Le=null,bn(e,t,n),Le=r,zt=i,Le!==null&&(zt?(e=Le,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Le.removeChild(n.stateNode));break;case 18:Le!==null&&(zt?(e=Le,n=n.stateNode,e.nodeType===8?Pl(e.parentNode,n):e.nodeType===1&&Pl(e,n),yo(e)):Pl(Le,n.stateNode));break;case 4:r=Le,i=zt,Le=n.stateNode.containerInfo,zt=!0,bn(e,t,n),Le=r,zt=i;break;case 0:case 11:case 14:case 15:if(!je&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,s=o.destroy;o=o.tag,s!==void 0&&(o&2||o&4)&&Ku(n,t,s),i=i.next}while(i!==r)}bn(e,t,n);break;case 1:if(!je&&($r(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){Se(n,t,a)}bn(e,t,n);break;case 21:bn(e,t,n);break;case 22:n.mode&1?(je=(r=je)||n.memoizedState!==null,bn(e,t,n),je=r):bn(e,t,n);break;default:bn(e,t,n)}}function ud(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new T3),t.forEach(function(r){var i=B3.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Bt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,s=t,a=s;e:for(;a!==null;){switch(a.tag){case 5:Le=a.stateNode,zt=!1;break e;case 3:Le=a.stateNode.containerInfo,zt=!0;break e;case 4:Le=a.stateNode.containerInfo,zt=!0;break e}a=a.return}if(Le===null)throw Error(L(160));i1(o,s,i),Le=null,zt=!1;var l=i.alternate;l!==null&&(l.return=null),i.return=null}catch(u){Se(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)o1(t,e),t=t.sibling}function o1(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Bt(t,e),Qt(e),r&4){try{no(3,e,e.return),Ua(3,e)}catch(g){Se(e,e.return,g)}try{no(5,e,e.return)}catch(g){Se(e,e.return,g)}}break;case 1:Bt(t,e),Qt(e),r&512&&n!==null&&$r(n,n.return);break;case 5:if(Bt(t,e),Qt(e),r&512&&n!==null&&$r(n,n.return),e.flags&32){var i=e.stateNode;try{po(i,"")}catch(g){Se(e,e.return,g)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,s=n!==null?n.memoizedProps:o,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&o.type==="radio"&&o.name!=null&&Tm(i,o),wu(a,s);var u=wu(a,o);for(s=0;s<l.length;s+=2){var c=l[s],h=l[s+1];c==="style"?Rm(i,h):c==="dangerouslySetInnerHTML"?Dm(i,h):c==="children"?po(i,h):Ic(i,c,h,u)}switch(a){case"input":pu(i,o);break;case"textarea":km(i,o);break;case"select":var f=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var v=o.value;v!=null?ri(i,!!o.multiple,v,!1):f!==!!o.multiple&&(o.defaultValue!=null?ri(i,!!o.multiple,o.defaultValue,!0):ri(i,!!o.multiple,o.multiple?[]:"",!1))}i[Eo]=o}catch(g){Se(e,e.return,g)}}break;case 6:if(Bt(t,e),Qt(e),r&4){if(e.stateNode===null)throw Error(L(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(g){Se(e,e.return,g)}}break;case 3:if(Bt(t,e),Qt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{yo(t.containerInfo)}catch(g){Se(e,e.return,g)}break;case 4:Bt(t,e),Qt(e);break;case 13:Bt(t,e),Qt(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(pf=Ae())),r&4&&ud(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(je=(u=je)||c,Bt(t,e),je=u):Bt(t,e),Qt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(U=e,c=e.child;c!==null;){for(h=U=c;U!==null;){switch(f=U,v=f.child,f.tag){case 0:case 11:case 14:case 15:no(4,f,f.return);break;case 1:$r(f,f.return);var p=f.stateNode;if(typeof p.componentWillUnmount=="function"){r=f,n=f.return;try{t=r,p.props=t.memoizedProps,p.state=t.memoizedState,p.componentWillUnmount()}catch(g){Se(r,n,g)}}break;case 5:$r(f,f.return);break;case 22:if(f.memoizedState!==null){fd(h);continue}}v!==null?(v.return=f,U=v):fd(h)}c=c.sibling}e:for(c=null,h=e;;){if(h.tag===5){if(c===null){c=h;try{i=h.stateNode,u?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(a=h.stateNode,l=h.memoizedProps.style,s=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=Mm("display",s))}catch(g){Se(e,e.return,g)}}}else if(h.tag===6){if(c===null)try{h.stateNode.nodeValue=u?"":h.memoizedProps}catch(g){Se(e,e.return,g)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===e)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===e)break e;for(;h.sibling===null;){if(h.return===null||h.return===e)break e;c===h&&(c=null),h=h.return}c===h&&(c=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:Bt(t,e),Qt(e),r&4&&ud(e);break;case 21:break;default:Bt(t,e),Qt(e)}}function Qt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(r1(n)){var r=n;break e}n=n.return}throw Error(L(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(po(i,""),r.flags&=-33);var o=ld(e);Yu(e,o,i);break;case 3:case 4:var s=r.stateNode.containerInfo,a=ld(e);$u(e,a,s);break;default:throw Error(L(161))}}catch(l){Se(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function P3(e,t,n){U=e,s1(e)}function s1(e,t,n){for(var r=(e.mode&1)!==0;U!==null;){var i=U,o=i.child;if(i.tag===22&&r){var s=i.memoizedState!==null||ws;if(!s){var a=i.alternate,l=a!==null&&a.memoizedState!==null||je;a=ws;var u=je;if(ws=s,(je=l)&&!u)for(U=i;U!==null;)s=U,l=s.child,s.tag===22&&s.memoizedState!==null?hd(i):l!==null?(l.return=s,U=l):hd(i);for(;o!==null;)U=o,s1(o),o=o.sibling;U=i,ws=a,je=u}cd(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,U=o):cd(e)}}function cd(e){for(;U!==null;){var t=U;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:je||Ua(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!je)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:Ft(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&$h(t,o,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}$h(t,s,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var h=c.dehydrated;h!==null&&yo(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(L(163))}je||t.flags&512&&qu(t)}catch(f){Se(t,t.return,f)}}if(t===e){U=null;break}if(n=t.sibling,n!==null){n.return=t.return,U=n;break}U=t.return}}function fd(e){for(;U!==null;){var t=U;if(t===e){U=null;break}var n=t.sibling;if(n!==null){n.return=t.return,U=n;break}U=t.return}}function hd(e){for(;U!==null;){var t=U;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ua(4,t)}catch(l){Se(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(l){Se(t,i,l)}}var o=t.return;try{qu(t)}catch(l){Se(t,o,l)}break;case 5:var s=t.return;try{qu(t)}catch(l){Se(t,s,l)}}}catch(l){Se(t,t.return,l)}if(t===e){U=null;break}var a=t.sibling;if(a!==null){a.return=t.return,U=a;break}U=t.return}}var D3=Math.ceil,ga=En.ReactCurrentDispatcher,hf=En.ReactCurrentOwner,Pt=En.ReactCurrentBatchConfig,ne=0,Oe=null,Ce=null,Be=0,at=0,Yr=Jn(0),ke=0,Po=null,Tr=0,ja=0,df=0,ro=null,Qe=null,pf=0,vi=1/0,an=null,va=!1,Zu=null,jn=null,xs=!1,Ln=null,ya=0,io=0,Xu=null,js=-1,Gs=0;function Ye(){return ne&6?Ae():js!==-1?js:js=Ae()}function Gn(e){return e.mode&1?ne&2&&Be!==0?Be&-Be:d3.transition!==null?(Gs===0&&(Gs=Hm()),Gs):(e=le,e!==0||(e=window.event,e=e===void 0?16:Xm(e.type)),e):1}function Kt(e,t,n,r){if(50<io)throw io=0,Xu=null,Error(L(185));jo(e,n,r),(!(ne&2)||e!==Oe)&&(e===Oe&&(!(ne&2)&&(ja|=n),ke===4&&On(e,Be)),nt(e,r),n===1&&ne===0&&!(t.mode&1)&&(vi=Ae()+500,Na&&er()))}function nt(e,t){var n=e.callbackNode;d2(e,t);var r=ta(e,e===Oe?Be:0);if(r===0)n!==null&&_h(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&_h(n),t===1)e.tag===0?h3(dd.bind(null,e)):gg(dd.bind(null,e)),l3(function(){!(ne&6)&&er()}),n=null;else{switch(Wm(r)){case 1:n=Fc;break;case 4:n=jm;break;case 16:n=ea;break;case 536870912:n=Gm;break;default:n=ea}n=p1(n,a1.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function a1(e,t){if(js=-1,Gs=0,ne&6)throw Error(L(327));var n=e.callbackNode;if(li()&&e.callbackNode!==n)return null;var r=ta(e,e===Oe?Be:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=wa(e,r);else{t=r;var i=ne;ne|=2;var o=u1();(Oe!==e||Be!==t)&&(an=null,vi=Ae()+500,_r(e,t));do try{O3();break}catch(a){l1(e,a)}while(!0);Qc(),ga.current=o,ne=i,Ce!==null?t=0:(Oe=null,Be=0,t=ke)}if(t!==0){if(t===2&&(i=Au(e),i!==0&&(r=i,t=Qu(e,i))),t===1)throw n=Po,_r(e,0),On(e,r),nt(e,Ae()),n;if(t===6)On(e,r);else{if(i=e.current.alternate,!(r&30)&&!M3(i)&&(t=wa(e,r),t===2&&(o=Au(e),o!==0&&(r=o,t=Qu(e,o))),t===1))throw n=Po,_r(e,0),On(e,r),nt(e,Ae()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(L(345));case 2:fr(e,Qe,an);break;case 3:if(On(e,r),(r&130023424)===r&&(t=pf+500-Ae(),10<t)){if(ta(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){Ye(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Ru(fr.bind(null,e,Qe,an),t);break}fr(e,Qe,an);break;case 4:if(On(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-Wt(r);o=1<<s,s=t[s],s>i&&(i=s),r&=~o}if(r=i,r=Ae()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*D3(r/1960))-r,10<r){e.timeoutHandle=Ru(fr.bind(null,e,Qe,an),r);break}fr(e,Qe,an);break;case 5:fr(e,Qe,an);break;default:throw Error(L(329))}}}return nt(e,Ae()),e.callbackNode===n?a1.bind(null,e):null}function Qu(e,t){var n=ro;return e.current.memoizedState.isDehydrated&&(_r(e,t).flags|=256),e=wa(e,t),e!==2&&(t=Qe,Qe=n,t!==null&&Ju(t)),e}function Ju(e){Qe===null?Qe=e:Qe.push.apply(Qe,e)}function M3(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!qt(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function On(e,t){for(t&=~df,t&=~ja,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Wt(t),r=1<<n;e[n]=-1,t&=~r}}function dd(e){if(ne&6)throw Error(L(327));li();var t=ta(e,0);if(!(t&1))return nt(e,Ae()),null;var n=wa(e,t);if(e.tag!==0&&n===2){var r=Au(e);r!==0&&(t=r,n=Qu(e,r))}if(n===1)throw n=Po,_r(e,0),On(e,t),nt(e,Ae()),n;if(n===6)throw Error(L(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,fr(e,Qe,an),nt(e,Ae()),null}function mf(e,t){var n=ne;ne|=1;try{return e(t)}finally{ne=n,ne===0&&(vi=Ae()+500,Na&&er())}}function kr(e){Ln!==null&&Ln.tag===0&&!(ne&6)&&li();var t=ne;ne|=1;var n=Pt.transition,r=le;try{if(Pt.transition=null,le=1,e)return e()}finally{le=r,Pt.transition=n,ne=t,!(ne&6)&&er()}}function gf(){at=Yr.current,me(Yr)}function _r(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,a3(n)),Ce!==null)for(n=Ce.return;n!==null;){var r=n;switch(Yc(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&sa();break;case 3:mi(),me(et),me(We),of();break;case 5:rf(r);break;case 4:mi();break;case 13:me(ye);break;case 19:me(ye);break;case 10:Jc(r.type._context);break;case 22:case 23:gf()}n=n.return}if(Oe=e,Ce=e=Hn(e.current,null),Be=at=t,ke=0,Po=null,df=ja=Tr=0,Qe=ro=null,yr!==null){for(t=0;t<yr.length;t++)if(n=yr[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var s=o.next;o.next=i,r.next=s}n.pending=r}yr=null}return e}function l1(e,t){do{var n=Ce;try{if(Qc(),Fs.current=ma,pa){for(var r=xe.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}pa=!1}if(br=0,Re=Te=xe=null,to=!1,bo=0,hf.current=null,n===null||n.return===null){ke=1,Po=t,Ce=null;break}e:{var o=e,s=n.return,a=n,l=t;if(t=Be,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,c=a,h=c.tag;if(!(c.mode&1)&&(h===0||h===11||h===15)){var f=c.alternate;f?(c.updateQueue=f.updateQueue,c.memoizedState=f.memoizedState,c.lanes=f.lanes):(c.updateQueue=null,c.memoizedState=null)}var v=ed(s);if(v!==null){v.flags&=-257,td(v,s,a,o,t),v.mode&1&&Jh(o,u,t),t=v,l=u;var p=t.updateQueue;if(p===null){var g=new Set;g.add(l),t.updateQueue=g}else p.add(l);break e}else{if(!(t&1)){Jh(o,u,t),vf();break e}l=Error(L(426))}}else if(ve&&a.mode&1){var S=ed(s);if(S!==null){!(S.flags&65536)&&(S.flags|=256),td(S,s,a,o,t),Zc(gi(l,a));break e}}o=l=gi(l,a),ke!==4&&(ke=2),ro===null?ro=[o]:ro.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var y=Wg(o,l,t);qh(o,y);break e;case 1:a=l;var w=o.type,_=o.stateNode;if(!(o.flags&128)&&(typeof w.getDerivedStateFromError=="function"||_!==null&&typeof _.componentDidCatch=="function"&&(jn===null||!jn.has(_)))){o.flags|=65536,t&=-t,o.lanes|=t;var E=Kg(o,a,t);qh(o,E);break e}}o=o.return}while(o!==null)}f1(n)}catch(b){t=b,Ce===n&&n!==null&&(Ce=n=n.return);continue}break}while(!0)}function u1(){var e=ga.current;return ga.current=ma,e===null?ma:e}function vf(){(ke===0||ke===3||ke===2)&&(ke=4),Oe===null||!(Tr&268435455)&&!(ja&268435455)||On(Oe,Be)}function wa(e,t){var n=ne;ne|=2;var r=u1();(Oe!==e||Be!==t)&&(an=null,_r(e,t));do try{R3();break}catch(i){l1(e,i)}while(!0);if(Qc(),ne=n,ga.current=r,Ce!==null)throw Error(L(261));return Oe=null,Be=0,ke}function R3(){for(;Ce!==null;)c1(Ce)}function O3(){for(;Ce!==null&&!i2();)c1(Ce)}function c1(e){var t=d1(e.alternate,e,at);e.memoizedProps=e.pendingProps,t===null?f1(e):Ce=t,hf.current=null}function f1(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=b3(n,t),n!==null){n.flags&=32767,Ce=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ke=6,Ce=null;return}}else if(n=C3(n,t,at),n!==null){Ce=n;return}if(t=t.sibling,t!==null){Ce=t;return}Ce=t=e}while(t!==null);ke===0&&(ke=5)}function fr(e,t,n){var r=le,i=Pt.transition;try{Pt.transition=null,le=1,I3(e,t,n,r)}finally{Pt.transition=i,le=r}return null}function I3(e,t,n,r){do li();while(Ln!==null);if(ne&6)throw Error(L(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(L(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(p2(e,o),e===Oe&&(Ce=Oe=null,Be=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||xs||(xs=!0,p1(ea,function(){return li(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=Pt.transition,Pt.transition=null;var s=le;le=1;var a=ne;ne|=4,hf.current=null,k3(e,n),o1(n,e),e3(Du),na=!!Pu,Du=Pu=null,e.current=n,P3(n),o2(),ne=a,le=s,Pt.transition=o}else e.current=n;if(xs&&(xs=!1,Ln=e,ya=i),o=e.pendingLanes,o===0&&(jn=null),l2(n.stateNode),nt(e,Ae()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(va)throw va=!1,e=Zu,Zu=null,e;return ya&1&&e.tag!==0&&li(),o=e.pendingLanes,o&1?e===Xu?io++:(io=0,Xu=e):io=0,er(),null}function li(){if(Ln!==null){var e=Wm(ya),t=Pt.transition,n=le;try{if(Pt.transition=null,le=16>e?16:e,Ln===null)var r=!1;else{if(e=Ln,Ln=null,ya=0,ne&6)throw Error(L(331));var i=ne;for(ne|=4,U=e.current;U!==null;){var o=U,s=o.child;if(U.flags&16){var a=o.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(U=u;U!==null;){var c=U;switch(c.tag){case 0:case 11:case 15:no(8,c,o)}var h=c.child;if(h!==null)h.return=c,U=h;else for(;U!==null;){c=U;var f=c.sibling,v=c.return;if(n1(c),c===u){U=null;break}if(f!==null){f.return=v,U=f;break}U=v}}}var p=o.alternate;if(p!==null){var g=p.child;if(g!==null){p.child=null;do{var S=g.sibling;g.sibling=null,g=S}while(g!==null)}}U=o}}if(o.subtreeFlags&2064&&s!==null)s.return=o,U=s;else e:for(;U!==null;){if(o=U,o.flags&2048)switch(o.tag){case 0:case 11:case 15:no(9,o,o.return)}var y=o.sibling;if(y!==null){y.return=o.return,U=y;break e}U=o.return}}var w=e.current;for(U=w;U!==null;){s=U;var _=s.child;if(s.subtreeFlags&2064&&_!==null)_.return=s,U=_;else e:for(s=w;U!==null;){if(a=U,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Ua(9,a)}}catch(b){Se(a,a.return,b)}if(a===s){U=null;break e}var E=a.sibling;if(E!==null){E.return=a.return,U=E;break e}U=a.return}}if(ne=i,er(),tn&&typeof tn.onPostCommitFiberRoot=="function")try{tn.onPostCommitFiberRoot(Oa,e)}catch{}r=!0}return r}finally{le=n,Pt.transition=t}}return!1}function pd(e,t,n){t=gi(n,t),t=Wg(e,t,1),e=Un(e,t,1),t=Ye(),e!==null&&(jo(e,1,t),nt(e,t))}function Se(e,t,n){if(e.tag===3)pd(e,e,n);else for(;t!==null;){if(t.tag===3){pd(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(jn===null||!jn.has(r))){e=gi(n,e),e=Kg(t,e,1),t=Un(t,e,1),e=Ye(),t!==null&&(jo(t,1,e),nt(t,e));break}}t=t.return}}function L3(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ye(),e.pingedLanes|=e.suspendedLanes&n,Oe===e&&(Be&n)===n&&(ke===4||ke===3&&(Be&130023424)===Be&&500>Ae()-pf?_r(e,0):df|=n),nt(e,t)}function h1(e,t){t===0&&(e.mode&1?(t=cs,cs<<=1,!(cs&130023424)&&(cs=4194304)):t=1);var n=Ye();e=wn(e,t),e!==null&&(jo(e,t,n),nt(e,n))}function V3(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),h1(e,n)}function B3(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(L(314))}r!==null&&r.delete(t),h1(e,n)}var d1;d1=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||et.current)Je=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Je=!1,A3(e,t,n);Je=!!(e.flags&131072)}else Je=!1,ve&&t.flags&1048576&&vg(t,ua,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Us(e,t),e=t.pendingProps;var i=hi(t,We.current);ai(t,n),i=af(null,t,r,e,i,n);var o=lf();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,tt(r)?(o=!0,aa(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,tf(t),i.updater=za,t.stateNode=i,i._reactInternals=t,Fu(t,r,e,n),t=ju(null,t,r,!0,o,n)):(t.tag=0,ve&&o&&$c(t),qe(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Us(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=F3(r),e=Ft(r,e),i){case 0:t=Uu(null,t,r,e,n);break e;case 1:t=id(null,t,r,e,n);break e;case 11:t=nd(null,t,r,e,n);break e;case 14:t=rd(null,t,r,Ft(r.type,e),n);break e}throw Error(L(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ft(r,i),Uu(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ft(r,i),id(e,t,r,i,n);case 3:e:{if(Zg(t),e===null)throw Error(L(387));r=t.pendingProps,o=t.memoizedState,i=o.element,Eg(e,t),ha(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=gi(Error(L(423)),t),t=od(e,t,r,n,i);break e}else if(r!==i){i=gi(Error(L(424)),t),t=od(e,t,r,n,i);break e}else for(ut=zn(t.stateNode.containerInfo.firstChild),ct=t,ve=!0,Ut=null,n=_g(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(di(),r===i){t=xn(e,t,n);break e}qe(e,t,r,n)}t=t.child}return t;case 5:return Ag(t),e===null&&Vu(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,s=i.children,Mu(r,i)?s=null:o!==null&&Mu(r,o)&&(t.flags|=32),Yg(e,t),qe(e,t,s,n),t.child;case 6:return e===null&&Vu(t),null;case 13:return Xg(e,t,n);case 4:return nf(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=pi(t,null,r,n):qe(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ft(r,i),nd(e,t,r,i,n);case 7:return qe(e,t,t.pendingProps,n),t.child;case 8:return qe(e,t,t.pendingProps.children,n),t.child;case 12:return qe(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,s=i.value,fe(ca,r._currentValue),r._currentValue=s,o!==null)if(qt(o.value,s)){if(o.children===i.children&&!et.current){t=xn(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var a=o.dependencies;if(a!==null){s=o.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(o.tag===1){l=dn(-1,n&-n),l.tag=2;var u=o.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?l.next=l:(l.next=c.next,c.next=l),u.pending=l}}o.lanes|=n,l=o.alternate,l!==null&&(l.lanes|=n),Bu(o.return,n,t),a.lanes|=n;break}l=l.next}}else if(o.tag===10)s=o.type===t.type?null:o.child;else if(o.tag===18){if(s=o.return,s===null)throw Error(L(341));s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),Bu(s,n,t),s=o.sibling}else s=o.child;if(s!==null)s.return=o;else for(s=o;s!==null;){if(s===t){s=null;break}if(o=s.sibling,o!==null){o.return=s.return,s=o;break}s=s.return}o=s}qe(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,ai(t,n),i=Dt(i),r=r(i),t.flags|=1,qe(e,t,r,n),t.child;case 14:return r=t.type,i=Ft(r,t.pendingProps),i=Ft(r.type,i),rd(e,t,r,i,n);case 15:return qg(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ft(r,i),Us(e,t),t.tag=1,tt(r)?(e=!0,aa(t)):e=!1,ai(t,n),Hg(t,r,i),Fu(t,r,i,n),ju(null,t,r,!0,e,n);case 19:return Qg(e,t,n);case 22:return $g(e,t,n)}throw Error(L(156,t.tag))};function p1(e,t){return Um(e,t)}function N3(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function kt(e,t,n,r){return new N3(e,t,n,r)}function yf(e){return e=e.prototype,!(!e||!e.isReactComponent)}function F3(e){if(typeof e=="function")return yf(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Vc)return 11;if(e===Bc)return 14}return 2}function Hn(e,t){var n=e.alternate;return n===null?(n=kt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Hs(e,t,n,r,i,o){var s=2;if(r=e,typeof e=="function")yf(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case Fr:return Sr(n.children,i,o,t);case Lc:s=8,i|=8;break;case uu:return e=kt(12,n,t,i|2),e.elementType=uu,e.lanes=o,e;case cu:return e=kt(13,n,t,i),e.elementType=cu,e.lanes=o,e;case fu:return e=kt(19,n,t,i),e.elementType=fu,e.lanes=o,e;case Am:return Ga(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Sm:s=10;break e;case Em:s=9;break e;case Vc:s=11;break e;case Bc:s=14;break e;case Pn:s=16,r=null;break e}throw Error(L(130,e==null?e:typeof e,""))}return t=kt(s,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function Sr(e,t,n,r){return e=kt(7,e,r,t),e.lanes=n,e}function Ga(e,t,n,r){return e=kt(22,e,r,t),e.elementType=Am,e.lanes=n,e.stateNode={isHidden:!1},e}function Bl(e,t,n){return e=kt(6,e,null,t),e.lanes=n,e}function Nl(e,t,n){return t=kt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function z3(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=yl(0),this.expirationTimes=yl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=yl(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function wf(e,t,n,r,i,o,s,a,l){return e=new z3(e,t,n,a,l),t===1?(t=1,o===!0&&(t|=8)):t=0,o=kt(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},tf(o),e}function U3(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Nr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function m1(e){if(!e)return Yn;e=e._reactInternals;e:{if(Or(e)!==e||e.tag!==1)throw Error(L(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(tt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(L(171))}if(e.tag===1){var n=e.type;if(tt(n))return mg(e,n,t)}return t}function g1(e,t,n,r,i,o,s,a,l){return e=wf(n,r,!0,e,i,o,s,a,l),e.context=m1(null),n=e.current,r=Ye(),i=Gn(n),o=dn(r,i),o.callback=t??null,Un(n,o,i),e.current.lanes=i,jo(e,i,r),nt(e,r),e}function Ha(e,t,n,r){var i=t.current,o=Ye(),s=Gn(i);return n=m1(n),t.context===null?t.context=n:t.pendingContext=n,t=dn(o,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Un(i,t,s),e!==null&&(Kt(e,i,s,o),Ns(e,i,s)),s}function xa(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function md(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function xf(e,t){md(e,t),(e=e.alternate)&&md(e,t)}function j3(){return null}var v1=typeof reportError=="function"?reportError:function(e){console.error(e)};function _f(e){this._internalRoot=e}Wa.prototype.render=_f.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(L(409));Ha(e,t,null,null)};Wa.prototype.unmount=_f.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;kr(function(){Ha(null,e,null,null)}),t[yn]=null}};function Wa(e){this._internalRoot=e}Wa.prototype.unstable_scheduleHydration=function(e){if(e){var t=$m();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Rn.length&&t!==0&&t<Rn[n].priority;n++);Rn.splice(n,0,e),n===0&&Zm(e)}};function Sf(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ka(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function gd(){}function G3(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var u=xa(s);o.call(u)}}var s=g1(t,r,e,0,null,!1,!1,"",gd);return e._reactRootContainer=s,e[yn]=s.current,_o(e.nodeType===8?e.parentNode:e),kr(),s}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var a=r;r=function(){var u=xa(l);a.call(u)}}var l=wf(e,0,!1,null,null,!1,!1,"",gd);return e._reactRootContainer=l,e[yn]=l.current,_o(e.nodeType===8?e.parentNode:e),kr(function(){Ha(t,l,n,r)}),l}function qa(e,t,n,r,i){var o=n._reactRootContainer;if(o){var s=o;if(typeof i=="function"){var a=i;i=function(){var l=xa(s);a.call(l)}}Ha(t,s,e,i)}else s=G3(n,t,e,i,r);return xa(s)}Km=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Wi(t.pendingLanes);n!==0&&(zc(t,n|1),nt(t,Ae()),!(ne&6)&&(vi=Ae()+500,er()))}break;case 13:kr(function(){var r=wn(e,1);if(r!==null){var i=Ye();Kt(r,e,1,i)}}),xf(e,1)}};Uc=function(e){if(e.tag===13){var t=wn(e,134217728);if(t!==null){var n=Ye();Kt(t,e,134217728,n)}xf(e,134217728)}};qm=function(e){if(e.tag===13){var t=Gn(e),n=wn(e,t);if(n!==null){var r=Ye();Kt(n,e,t,r)}xf(e,t)}};$m=function(){return le};Ym=function(e,t){var n=le;try{return le=e,t()}finally{le=n}};_u=function(e,t,n){switch(t){case"input":if(pu(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Ba(r);if(!i)throw Error(L(90));bm(r),pu(r,i)}}}break;case"textarea":km(e,n);break;case"select":t=n.value,t!=null&&ri(e,!!n.multiple,t,!1)}};Lm=mf;Vm=kr;var H3={usingClientEntryPoint:!1,Events:[Ho,Gr,Ba,Om,Im,mf]},Ui={findFiberByHostInstance:vr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},W3={bundleType:Ui.bundleType,version:Ui.version,rendererPackageName:Ui.rendererPackageName,rendererConfig:Ui.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:En.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Fm(e),e===null?null:e.stateNode},findFiberByHostInstance:Ui.findFiberByHostInstance||j3,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var _s=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!_s.isDisabled&&_s.supportsFiber)try{Oa=_s.inject(W3),tn=_s}catch{}}mt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=H3;mt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Sf(t))throw Error(L(200));return U3(e,t,null,n)};mt.createRoot=function(e,t){if(!Sf(e))throw Error(L(299));var n=!1,r="",i=v1;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=wf(e,1,!1,null,null,n,!1,r,i),e[yn]=t.current,_o(e.nodeType===8?e.parentNode:e),new _f(t)};mt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(L(188)):(e=Object.keys(e).join(","),Error(L(268,e)));return e=Fm(t),e=e===null?null:e.stateNode,e};mt.flushSync=function(e){return kr(e)};mt.hydrate=function(e,t,n){if(!Ka(t))throw Error(L(200));return qa(null,e,t,!0,n)};mt.hydrateRoot=function(e,t,n){if(!Sf(e))throw Error(L(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",s=v1;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=g1(t,null,e,1,n??null,i,!1,o,s),e[yn]=t.current,_o(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Wa(t)};mt.render=function(e,t,n){if(!Ka(t))throw Error(L(200));return qa(null,e,t,!1,n)};mt.unmountComponentAtNode=function(e){if(!Ka(e))throw Error(L(40));return e._reactRootContainer?(kr(function(){qa(null,null,e,!1,function(){e._reactRootContainer=null,e[yn]=null})}),!0):!1};mt.unstable_batchedUpdates=mf;mt.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ka(n))throw Error(L(200));if(e==null||e._reactInternals===void 0)throw Error(L(38));return qa(e,t,n,!1,r)};mt.version="18.3.1-next-f1338f8080-20240426";function y1(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(y1)}catch(e){console.error(e)}}y1(),ym.exports=mt;var w1=ym.exports;const D9=Ma(w1);var vd=w1;fh.createRoot=vd.createRoot,fh.hydrateRoot=vd.hydrateRoot;var x1={exports:{}};(function(e,t){(function(n,r){r(e)})(typeof globalThis<"u"?globalThis:typeof self<"u"?self:sm,function(n){var r,i;if(!((i=(r=globalThis.chrome)==null?void 0:r.runtime)!=null&&i.id))throw new Error("This script should only be loaded in a browser extension.");if(typeof globalThis.browser>"u"||Object.getPrototypeOf(globalThis.browser)!==Object.prototype){const o="The message port closed before a response was received.",s=a=>{const l={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},getChildren:{minArgs:1,maxArgs:1},getRecent:{minArgs:1,maxArgs:1},getSubTree:{minArgs:1,maxArgs:1},getTree:{minArgs:0,maxArgs:0},move:{minArgs:2,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeTree:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}},browserAction:{disable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},enable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},getBadgeBackgroundColor:{minArgs:1,maxArgs:1},getBadgeText:{minArgs:1,maxArgs:1},getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},openPopup:{minArgs:0,maxArgs:0},setBadgeBackgroundColor:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setBadgeText:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},browsingData:{remove:{minArgs:2,maxArgs:2},removeCache:{minArgs:1,maxArgs:1},removeCookies:{minArgs:1,maxArgs:1},removeDownloads:{minArgs:1,maxArgs:1},removeFormData:{minArgs:1,maxArgs:1},removeHistory:{minArgs:1,maxArgs:1},removeLocalStorage:{minArgs:1,maxArgs:1},removePasswords:{minArgs:1,maxArgs:1},removePluginData:{minArgs:1,maxArgs:1},settings:{minArgs:0,maxArgs:0}},commands:{getAll:{minArgs:0,maxArgs:0}},contextMenus:{remove:{minArgs:1,maxArgs:1},removeAll:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},cookies:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:1,maxArgs:1},getAllCookieStores:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},devtools:{inspectedWindow:{eval:{minArgs:1,maxArgs:2,singleCallbackArg:!1}},panels:{create:{minArgs:3,maxArgs:3,singleCallbackArg:!0},elements:{createSidebarPane:{minArgs:1,maxArgs:1}}}},downloads:{cancel:{minArgs:1,maxArgs:1},download:{minArgs:1,maxArgs:1},erase:{minArgs:1,maxArgs:1},getFileIcon:{minArgs:1,maxArgs:2},open:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},pause:{minArgs:1,maxArgs:1},removeFile:{minArgs:1,maxArgs:1},resume:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},extension:{isAllowedFileSchemeAccess:{minArgs:0,maxArgs:0},isAllowedIncognitoAccess:{minArgs:0,maxArgs:0}},history:{addUrl:{minArgs:1,maxArgs:1},deleteAll:{minArgs:0,maxArgs:0},deleteRange:{minArgs:1,maxArgs:1},deleteUrl:{minArgs:1,maxArgs:1},getVisits:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1}},i18n:{detectLanguage:{minArgs:1,maxArgs:1},getAcceptLanguages:{minArgs:0,maxArgs:0}},identity:{launchWebAuthFlow:{minArgs:1,maxArgs:1}},idle:{queryState:{minArgs:1,maxArgs:1}},management:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},getSelf:{minArgs:0,maxArgs:0},setEnabled:{minArgs:2,maxArgs:2},uninstallSelf:{minArgs:0,maxArgs:1}},notifications:{clear:{minArgs:1,maxArgs:1},create:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:0},getPermissionLevel:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},pageAction:{getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},hide:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},permissions:{contains:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},request:{minArgs:1,maxArgs:1}},runtime:{getBackgroundPage:{minArgs:0,maxArgs:0},getPlatformInfo:{minArgs:0,maxArgs:0},openOptionsPage:{minArgs:0,maxArgs:0},requestUpdateCheck:{minArgs:0,maxArgs:0},sendMessage:{minArgs:1,maxArgs:3},sendNativeMessage:{minArgs:2,maxArgs:2},setUninstallURL:{minArgs:1,maxArgs:1}},sessions:{getDevices:{minArgs:0,maxArgs:1},getRecentlyClosed:{minArgs:0,maxArgs:1},restore:{minArgs:0,maxArgs:1}},storage:{local:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},managed:{get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1}},sync:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}}},tabs:{captureVisibleTab:{minArgs:0,maxArgs:2},create:{minArgs:1,maxArgs:1},detectLanguage:{minArgs:0,maxArgs:1},discard:{minArgs:0,maxArgs:1},duplicate:{minArgs:1,maxArgs:1},executeScript:{minArgs:1,maxArgs:2},get:{minArgs:1,maxArgs:1},getCurrent:{minArgs:0,maxArgs:0},getZoom:{minArgs:0,maxArgs:1},getZoomSettings:{minArgs:0,maxArgs:1},goBack:{minArgs:0,maxArgs:1},goForward:{minArgs:0,maxArgs:1},highlight:{minArgs:1,maxArgs:1},insertCSS:{minArgs:1,maxArgs:2},move:{minArgs:2,maxArgs:2},query:{minArgs:1,maxArgs:1},reload:{minArgs:0,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeCSS:{minArgs:1,maxArgs:2},sendMessage:{minArgs:2,maxArgs:3},setZoom:{minArgs:1,maxArgs:2},setZoomSettings:{minArgs:1,maxArgs:2},update:{minArgs:1,maxArgs:2}},topSites:{get:{minArgs:0,maxArgs:0}},webNavigation:{getAllFrames:{minArgs:1,maxArgs:1},getFrame:{minArgs:1,maxArgs:1}},webRequest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(Object.keys(l).length===0)throw new Error("api-metadata.json has not been included in browser-polyfill");class u extends WeakMap{constructor(D,R=void 0){super(R),this.createItem=D}get(D){return this.has(D)||this.set(D,this.createItem(D)),super.get(D)}}const c=C=>C&&typeof C=="object"&&typeof C.then=="function",h=(C,D)=>(...R)=>{a.runtime.lastError?C.reject(new Error(a.runtime.lastError.message)):D.singleCallbackArg||R.length<=1&&D.singleCallbackArg!==!1?C.resolve(R[0]):C.resolve(R)},f=C=>C==1?"argument":"arguments",v=(C,D)=>function(B,...Y){if(Y.length<D.minArgs)throw new Error(`Expected at least ${D.minArgs} ${f(D.minArgs)} for ${C}(), got ${Y.length}`);if(Y.length>D.maxArgs)throw new Error(`Expected at most ${D.maxArgs} ${f(D.maxArgs)} for ${C}(), got ${Y.length}`);return new Promise((J,ue)=>{if(D.fallbackToNoCallback)try{B[C](...Y,h({resolve:J,reject:ue},D))}catch(j){console.warn(`${C} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,j),B[C](...Y),D.fallbackToNoCallback=!1,D.noCallback=!0,J()}else D.noCallback?(B[C](...Y),J()):B[C](...Y,h({resolve:J,reject:ue},D))})},p=(C,D,R)=>new Proxy(D,{apply(B,Y,J){return R.call(Y,C,...J)}});let g=Function.call.bind(Object.prototype.hasOwnProperty);const S=(C,D={},R={})=>{let B=Object.create(null),Y={has(ue,j){return j in C||j in B},get(ue,j,ee){if(j in B)return B[j];if(!(j in C))return;let te=C[j];if(typeof te=="function")if(typeof D[j]=="function")te=p(C,C[j],D[j]);else if(g(R,j)){let N=v(j,R[j]);te=p(C,C[j],N)}else te=te.bind(C);else if(typeof te=="object"&&te!==null&&(g(D,j)||g(R,j)))te=S(te,D[j],R[j]);else if(g(R,"*"))te=S(te,D[j],R["*"]);else return Object.defineProperty(B,j,{configurable:!0,enumerable:!0,get(){return C[j]},set(N){C[j]=N}}),te;return B[j]=te,te},set(ue,j,ee,te){return j in B?B[j]=ee:C[j]=ee,!0},defineProperty(ue,j,ee){return Reflect.defineProperty(B,j,ee)},deleteProperty(ue,j){return Reflect.deleteProperty(B,j)}},J=Object.create(C);return new Proxy(J,Y)},y=C=>({addListener(D,R,...B){D.addListener(C.get(R),...B)},hasListener(D,R){return D.hasListener(C.get(R))},removeListener(D,R){D.removeListener(C.get(R))}}),w=new u(C=>typeof C!="function"?C:function(R){const B=S(R,{},{getContent:{minArgs:0,maxArgs:0}});C(B)}),_=new u(C=>typeof C!="function"?C:function(R,B,Y){let J=!1,ue,j=new Promise(H=>{ue=function(G){J=!0,H(G)}}),ee;try{ee=C(R,B,ue)}catch(H){ee=Promise.reject(H)}const te=ee!==!0&&c(ee);if(ee!==!0&&!te&&!J)return!1;const N=H=>{H.then(G=>{Y(G)},G=>{let se;G&&(G instanceof Error||typeof G.message=="string")?se=G.message:se="An unexpected error occurred",Y({__mozWebExtensionPolyfillReject__:!0,message:se})}).catch(G=>{console.error("Failed to send onMessage rejected reply",G)})};return N(te?ee:j),!0}),E=({reject:C,resolve:D},R)=>{a.runtime.lastError?a.runtime.lastError.message===o?D():C(new Error(a.runtime.lastError.message)):R&&R.__mozWebExtensionPolyfillReject__?C(new Error(R.message)):D(R)},b=(C,D,R,...B)=>{if(B.length<D.minArgs)throw new Error(`Expected at least ${D.minArgs} ${f(D.minArgs)} for ${C}(), got ${B.length}`);if(B.length>D.maxArgs)throw new Error(`Expected at most ${D.maxArgs} ${f(D.maxArgs)} for ${C}(), got ${B.length}`);return new Promise((Y,J)=>{const ue=E.bind(null,{resolve:Y,reject:J});B.push(ue),R.sendMessage(...B)})},k={devtools:{network:{onRequestFinished:y(w)}},runtime:{onMessage:y(_),onMessageExternal:y(_),sendMessage:b.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:b.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},P={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return l.privacy={network:{"*":P},services:{"*":P},websites:{"*":P}},S(a,k,l)};n.exports=s(chrome)}else n.exports=globalThis.browser})})(x1);var K3=x1.exports;const _1=Ma(K3);var Me=_1;const S1=M.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),$a=M.createContext({}),Ef=M.createContext(null),Af=typeof window<"u",q3=Af?M.useLayoutEffect:M.useEffect,E1=M.createContext({strict:!1}),Ya=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),$3="framerAppearId",A1="data-"+Ya($3),Y3={skipAnimations:!1,useManualTiming:!1};function Z3(e){let t=new Set,n=new Set,r=!1,i=!1;const o=new WeakSet;let s={delta:0,timestamp:0,isProcessing:!1};function a(u){o.has(u)&&(l.schedule(u),e()),u(s)}const l={schedule:(u,c=!1,h=!1)=>{const v=h&&r?t:n;return c&&o.add(u),v.has(u)||v.add(u),u},cancel:u=>{n.delete(u),o.delete(u)},process:u=>{if(s=u,r){i=!0;return}r=!0,[t,n]=[n,t],n.clear(),t.forEach(a),r=!1,i&&(i=!1,l.process(u))}};return l}const Ss=["read","resolveKeyframes","update","preRender","render","postRender"],X3=40;function C1(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,s=Ss.reduce((y,w)=>(y[w]=Z3(o),y),{}),{read:a,resolveKeyframes:l,update:u,preRender:c,render:h,postRender:f}=s,v=()=>{const y=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(y-i.timestamp,X3),1),i.timestamp=y,i.isProcessing=!0,a.process(i),l.process(i),u.process(i),c.process(i),h.process(i),f.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(v))},p=()=>{n=!0,r=!0,i.isProcessing||e(v)};return{schedule:Ss.reduce((y,w)=>{const _=s[w];return y[w]=(E,b=!1,k=!1)=>(n||p(),_.schedule(E,b,k)),y},{}),cancel:y=>{for(let w=0;w<Ss.length;w++)s[Ss[w]].cancel(y)},state:i,steps:s}}const{schedule:Cf,cancel:M9}=C1(queueMicrotask,!1);function Zr(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}const b1=M.createContext({});let yd=!1;function Q3(e,t,n,r,i){const{visualElement:o}=M.useContext($a),s=M.useContext(E1),a=M.useContext(Ef),l=M.useContext(S1).reducedMotion,u=M.useRef();r=r||s.renderer,!u.current&&r&&(u.current=r(e,{visualState:t,parent:o,props:n,presenceContext:a,blockInitialAnimation:a?a.initial===!1:!1,reducedMotionConfig:l}));const c=u.current,h=M.useContext(b1);c&&!c.projection&&i&&(c.type==="html"||c.type==="svg")&&ew(u.current,n,i,h),M.useInsertionEffect(()=>{c&&c.update(n,a)});const f=M.useRef(!!(n[A1]&&!window.HandoffComplete));return q3(()=>{c&&(c.updateFeatures(),Cf.render(c.render),f.current&&c.animationState&&c.animationState.animateChanges())}),M.useEffect(()=>{c&&(!f.current&&c.animationState&&c.animationState.animateChanges(),f.current&&(f.current=!1,yd||(yd=!0,queueMicrotask(J3))))}),c}function J3(){window.HandoffComplete=!0}function ew(e,t,n,r){const{layoutId:i,layout:o,drag:s,dragConstraints:a,layoutScroll:l,layoutRoot:u}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:T1(e.parent)),e.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!s||a&&Zr(a),visualElement:e,animationType:typeof o=="string"?o:"both",initialPromotionConfig:r,layoutScroll:l,layoutRoot:u})}function T1(e){if(e)return e.options.allowProjection!==!1?e.projection:T1(e.parent)}function tw(e,t,n){return M.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):Zr(n)&&(n.current=r))},[t])}function Do(e){return typeof e=="string"||Array.isArray(e)}function Mo(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const bf=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Tf=["initial",...bf];function Za(e){return Mo(e.animate)||Tf.some(t=>Do(e[t]))}function k1(e){return!!(Za(e)||e.variants)}function nw(e,t){if(Za(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Do(n)?n:void 0,animate:Do(r)?r:void 0}}return e.inherit!==!1?t:{}}function rw(e){const{initial:t,animate:n}=nw(e,M.useContext($a));return M.useMemo(()=>({initial:t,animate:n}),[wd(t),wd(n)])}function wd(e){return Array.isArray(e)?e.join(" "):e}const xd={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},yi={};for(const e in xd)yi[e]={isEnabled:t=>xd[e].some(n=>!!t[n])};function iw(e){for(const t in e)yi[t]={...yi[t],...e[t]}}const P1=M.createContext({}),ow=Symbol.for("motionComponentSymbol"),He=e=>e;let ec=He;function sw({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){e&&iw(e);function o(a,l){let u;const c={...M.useContext(S1),...a,layoutId:aw(a)},{isStatic:h}=c,f=rw(a),v=r(a,h);if(!h&&Af){lw();const p=uw(c);u=p.MeasureLayout,f.visualElement=Q3(i,v,c,t,p.ProjectionNode)}return fo.jsxs($a.Provider,{value:f,children:[u&&f.visualElement?fo.jsx(u,{visualElement:f.visualElement,...c}):null,n(i,a,tw(v,f.visualElement,l),v,h,f.visualElement)]})}const s=M.forwardRef(o);return s[ow]=i,s}function aw({layoutId:e}){const t=M.useContext(P1).id;return t&&e!==void 0?t+"-"+e:e}function lw(e,t){M.useContext(E1).strict}function uw(e){const{drag:t,layout:n}=yi;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t!=null&&t.isEnabled(e)||n!=null&&n.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}function cw(e){function t(r,i={}){return sw(e(r,i))}if(typeof Proxy>"u")return t;const n=new Map;return new Proxy(t,{get:(r,i)=>(n.has(i)||n.set(i,t(i)),n.get(i))})}const fw=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function kf(e){return typeof e!="string"||e.includes("-")?!1:!!(fw.indexOf(e)>-1||/[A-Z]/u.test(e))}const _a={};function hw(e){Object.assign(_a,e)}const Ko=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],tr=new Set(Ko);function D1(e,{layout:t,layoutId:n}){return tr.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!_a[e]||e==="opacity")}const Ge=e=>!!(e&&e.getVelocity),dw={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},pw=Ko.length;function mw(e,t,n){let r="";for(let i=0;i<pw;i++){const o=Ko[i];if(e[o]!==void 0){const s=dw[o]||o;r+=`${s}(${e[o]}) `}}return r=r.trim(),n?r=n(e,t?"":r):t&&(r="none"),r}const M1=e=>t=>typeof t=="string"&&t.startsWith(e),R1=M1("--"),gw=M1("var(--"),Pf=e=>gw(e)?vw.test(e.split("/*")[0].trim()):!1,vw=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,yw=(e,t)=>t&&typeof e=="number"?t.transform(e):e,Zn=(e,t,n)=>n>t?t:n<e?e:n,Pi={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},oo={...Pi,transform:e=>Zn(0,1,e)},Es={...Pi,default:1},so=e=>Math.round(e*1e5)/1e5,Df=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,ww=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,xw=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu;function qo(e){return typeof e=="string"}function _w(e){return e==null}const $o=e=>({test:t=>qo(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),Tn=$o("deg"),rn=$o("%"),W=$o("px"),Sw=$o("vh"),Ew=$o("vw"),_d={...rn,parse:e=>rn.parse(e)/100,transform:e=>rn.transform(e*100)},Sd={...Pi,transform:Math.round},O1={borderWidth:W,borderTopWidth:W,borderRightWidth:W,borderBottomWidth:W,borderLeftWidth:W,borderRadius:W,radius:W,borderTopLeftRadius:W,borderTopRightRadius:W,borderBottomRightRadius:W,borderBottomLeftRadius:W,width:W,maxWidth:W,height:W,maxHeight:W,size:W,top:W,right:W,bottom:W,left:W,padding:W,paddingTop:W,paddingRight:W,paddingBottom:W,paddingLeft:W,margin:W,marginTop:W,marginRight:W,marginBottom:W,marginLeft:W,rotate:Tn,rotateX:Tn,rotateY:Tn,rotateZ:Tn,scale:Es,scaleX:Es,scaleY:Es,scaleZ:Es,skew:Tn,skewX:Tn,skewY:Tn,distance:W,translateX:W,translateY:W,translateZ:W,x:W,y:W,z:W,perspective:W,transformPerspective:W,opacity:oo,originX:_d,originY:_d,originZ:W,zIndex:Sd,backgroundPositionX:W,backgroundPositionY:W,fillOpacity:oo,strokeOpacity:oo,numOctaves:Sd};function Mf(e,t,n){const{style:r,vars:i,transform:o,transformOrigin:s}=e;let a=!1,l=!1,u=!0;for(const c in t){const h=t[c];if(R1(c)){i[c]=h;continue}const f=O1[c],v=yw(h,f);if(tr.has(c)){if(a=!0,o[c]=v,!u)continue;h!==(f.default||0)&&(u=!1)}else c.startsWith("origin")?(l=!0,s[c]=v):r[c]=v}if(t.transform||(a||n?r.transform=mw(e.transform,u,n):r.transform&&(r.transform="none")),l){const{originX:c="50%",originY:h="50%",originZ:f=0}=s;r.transformOrigin=`${c} ${h} ${f}`}}const Rf=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function I1(e,t,n){for(const r in t)!Ge(t[r])&&!D1(r,n)&&(e[r]=t[r])}function Aw({transformTemplate:e},t){return M.useMemo(()=>{const n=Rf();return Mf(n,t,e),Object.assign({},n.vars,n.style)},[t])}function Cw(e,t){const n=e.style||{},r={};return I1(r,n,e),Object.assign(r,Aw(e,t)),r}function bw(e,t){const n={},r=Cw(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}const Tw=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Sa(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||Tw.has(e)}let L1=e=>!Sa(e);function kw(e){e&&(L1=t=>t.startsWith("on")?!Sa(t):e(t))}try{kw(require("@emotion/is-prop-valid").default)}catch{}function Pw(e,t,n){const r={};for(const i in e)i==="values"&&typeof e.values=="object"||(L1(i)||n===!0&&Sa(i)||!t&&!Sa(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function Ed(e,t,n){return typeof e=="string"?e:W.transform(t+n*e)}function Dw(e,t,n){const r=Ed(t,e.x,e.width),i=Ed(n,e.y,e.height);return`${r} ${i}`}const Mw={offset:"stroke-dashoffset",array:"stroke-dasharray"},Rw={offset:"strokeDashoffset",array:"strokeDasharray"};function Ow(e,t,n=1,r=0,i=!0){e.pathLength=1;const o=i?Mw:Rw;e[o.offset]=W.transform(-r);const s=W.transform(t),a=W.transform(n);e[o.array]=`${s} ${a}`}function Of(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:o,pathLength:s,pathSpacing:a=1,pathOffset:l=0,...u},c,h){if(Mf(e,u,h),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:f,style:v,dimensions:p}=e;f.transform&&(p&&(v.transform=f.transform),delete f.transform),p&&(i!==void 0||o!==void 0||v.transform)&&(v.transformOrigin=Dw(p,i!==void 0?i:.5,o!==void 0?o:.5)),t!==void 0&&(f.x=t),n!==void 0&&(f.y=n),r!==void 0&&(f.scale=r),s!==void 0&&Ow(f,s,a,l,!1)}const V1=()=>({...Rf(),attrs:{}}),If=e=>typeof e=="string"&&e.toLowerCase()==="svg";function Iw(e,t,n,r){const i=M.useMemo(()=>{const o=V1();return Of(o,t,If(r),e.transformTemplate),{...o.attrs,style:{...o.style}}},[t]);if(e.style){const o={};I1(o,e.style,e),i.style={...o,...i.style}}return i}function Lw(e=!1){return(n,r,i,{latestValues:o},s)=>{const l=(kf(n)?Iw:bw)(r,o,s,n),u=Pw(r,typeof n=="string",e),c=n!==M.Fragment?{...u,...l,ref:i}:{},{children:h}=r,f=M.useMemo(()=>Ge(h)?h.get():h,[h]);return M.createElement(n,{...c,children:f})}}function B1(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const o in n)e.style.setProperty(o,n[o])}const N1=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function F1(e,t,n,r){B1(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(N1.has(i)?i:Ya(i),t.attrs[i])}function Lf(e,t,n){var r;const{style:i}=e,o={};for(const s in i)(Ge(i[s])||t.style&&Ge(t.style[s])||D1(s,e)||((r=n==null?void 0:n.getValue(s))===null||r===void 0?void 0:r.liveStyle)!==void 0)&&(o[s]=i[s]);return n&&i&&typeof i.willChange=="string"&&(n.applyWillChange=!1),o}function z1(e,t,n){const r=Lf(e,t,n);for(const i in e)if(Ge(e[i])||Ge(t[i])){const o=Ko.indexOf(i)!==-1?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i;r[o]=e[i]}return r}function Ad(e){const t=[{},{}];return e==null||e.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function Vf(e,t,n,r){if(typeof t=="function"){const[i,o]=Ad(r);t=t(n!==void 0?n:e.custom,i,o)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[i,o]=Ad(r);t=t(n!==void 0?n:e.custom,i,o)}return t}function Vw(e){const t=M.useRef(null);return t.current===null&&(t.current=e()),t.current}const tc=e=>Array.isArray(e),Bw=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),Nw=e=>tc(e)?e[e.length-1]||0:e;function Ws(e){const t=Ge(e)?e.get():e;return Bw(t)?t.toValue():t}const U1=new Set(["opacity","clipPath","filter","transform"]);function j1(e){if(tr.has(e))return"transform";if(U1.has(e))return Ya(e)}function Xa(e,t){e.indexOf(t)===-1&&e.push(t)}function Qa(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}function Fw({applyWillChange:e=!1,scrapeMotionValuesFromProps:t,createRenderState:n,onMount:r},i,o,s,a){const l={latestValues:Uw(i,o,s,a?!1:e,t),renderState:n()};return r&&(l.mount=u=>r(i,u,l)),l}const G1=e=>(t,n)=>{const r=M.useContext($a),i=M.useContext(Ef),o=()=>Fw(e,t,r,i,n);return n?o():Vw(o)};function zw(e,t){const n=j1(t);n&&Xa(e,n)}function Cd(e,t,n){const r=Array.isArray(t)?t:[t];for(let i=0;i<r.length;i++){const o=Vf(e,r[i]);if(o){const{transitionEnd:s,transition:a,...l}=o;n(l,s)}}}function Uw(e,t,n,r,i){var o;const s={},a=[],l=r&&((o=e.style)===null||o===void 0?void 0:o.willChange)===void 0,u=i(e,{});for(const S in u)s[S]=Ws(u[S]);let{initial:c,animate:h}=e;const f=Za(e),v=k1(e);t&&v&&!f&&e.inherit!==!1&&(c===void 0&&(c=t.initial),h===void 0&&(h=t.animate));let p=n?n.initial===!1:!1;p=p||c===!1;const g=p?h:c;return g&&typeof g!="boolean"&&!Mo(g)&&Cd(e,g,(S,y)=>{for(const w in S){let _=S[w];if(Array.isArray(_)){const E=p?_.length-1:0;_=_[E]}_!==null&&(s[w]=_)}for(const w in y)s[w]=y[w]}),l&&(h&&c!==!1&&!Mo(h)&&Cd(e,h,S=>{for(const y in S)zw(a,y)}),a.length&&(s.willChange=a.join(","))),s}const{schedule:ae,cancel:_n,state:Ve,steps:Fl}=C1(typeof requestAnimationFrame<"u"?requestAnimationFrame:He,!0),jw={useVisualState:G1({scrapeMotionValuesFromProps:z1,createRenderState:V1,onMount:(e,t,{renderState:n,latestValues:r})=>{ae.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),ae.render(()=>{Of(n,r,If(t.tagName),e.transformTemplate),F1(t,n)})}})},Gw={useVisualState:G1({applyWillChange:!0,scrapeMotionValuesFromProps:Lf,createRenderState:Rf})};function Hw(e,{forwardMotionProps:t=!1},n,r){return{...kf(e)?jw:Gw,preloadedFeatures:n,useRender:Lw(t),createVisualElement:r,Component:e}}function hn(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const H1=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function Ja(e,t="page"){return{point:{x:e[`${t}X`],y:e[`${t}Y`]}}}const Ww=e=>t=>H1(t)&&e(t,Ja(t));function pn(e,t,n,r){return hn(e,t,Ww(n),r)}const Kw=(e,t)=>n=>t(e(n)),mn=(...e)=>e.reduce(Kw);function W1(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const bd=W1("dragHorizontal"),Td=W1("dragVertical");function K1(e){let t=!1;if(e==="y")t=Td();else if(e==="x")t=bd();else{const n=bd(),r=Td();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function q1(){const e=K1(!0);return e?(e(),!1):!0}class nr{constructor(t){this.isMounted=!1,this.node=t}update(){}}function kd(e,t){const n=t?"pointerenter":"pointerleave",r=t?"onHoverStart":"onHoverEnd",i=(o,s)=>{if(o.pointerType==="touch"||q1())return;const a=e.getProps();e.animationState&&a.whileHover&&e.animationState.setActive("whileHover",t);const l=a[r];l&&ae.postRender(()=>l(o,s))};return pn(e.current,n,i,{passive:!e.getProps()[r]})}class qw extends nr{mount(){this.unmount=mn(kd(this.node,!0),kd(this.node,!1))}unmount(){}}class $w extends nr{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=mn(hn(this.node.current,"focus",()=>this.onFocus()),hn(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const $1=(e,t)=>t?e===t?!0:$1(e,t.parentElement):!1;function zl(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,Ja(n))}class Yw extends nr{constructor(){super(...arguments),this.removeStartListeners=He,this.removeEndListeners=He,this.removeAccessibleListeners=He,this.startPointerPress=(t,n)=>{if(this.isPressing)return;this.removeEndListeners();const r=this.node.getProps(),o=pn(window,"pointerup",(a,l)=>{if(!this.checkPressEnd())return;const{onTap:u,onTapCancel:c,globalTapTarget:h}=this.node.getProps(),f=!h&&!$1(this.node.current,a.target)?c:u;f&&ae.update(()=>f(a,l))},{passive:!(r.onTap||r.onPointerUp)}),s=pn(window,"pointercancel",(a,l)=>this.cancelPress(a,l),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=mn(o,s),this.startPress(t,n)},this.startAccessiblePress=()=>{const t=o=>{if(o.key!=="Enter"||this.isPressing)return;const s=a=>{a.key!=="Enter"||!this.checkPressEnd()||zl("up",(l,u)=>{const{onTap:c}=this.node.getProps();c&&ae.postRender(()=>c(l,u))})};this.removeEndListeners(),this.removeEndListeners=hn(this.node.current,"keyup",s),zl("down",(a,l)=>{this.startPress(a,l)})},n=hn(this.node.current,"keydown",t),r=()=>{this.isPressing&&zl("cancel",(o,s)=>this.cancelPress(o,s))},i=hn(this.node.current,"blur",r);this.removeAccessibleListeners=mn(n,i)}}startPress(t,n){this.isPressing=!0;const{onTapStart:r,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&ae.postRender(()=>r(t,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!q1()}cancelPress(t,n){if(!this.checkPressEnd())return;const{onTapCancel:r}=this.node.getProps();r&&ae.postRender(()=>r(t,n))}mount(){const t=this.node.getProps(),n=pn(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=hn(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=mn(n,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const nc=new WeakMap,Ul=new WeakMap,Zw=e=>{const t=nc.get(e.target);t&&t(e)},Xw=e=>{e.forEach(Zw)};function Qw({root:e,...t}){const n=e||document;Ul.has(n)||Ul.set(n,{});const r=Ul.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(Xw,{root:e,...t})),r[i]}function Jw(e,t,n){const r=Qw(t);return nc.set(e,n),r.observe(e),()=>{nc.delete(e),r.unobserve(e)}}const ex={some:0,all:1};class tx extends nr{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:i="some",once:o}=t,s={root:n?n.current:void 0,rootMargin:r,threshold:typeof i=="number"?i:ex[i]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,o&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:h}=this.node.getProps(),f=u?c:h;f&&f(l)};return Jw(this.node.current,s,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(nx(t,n))&&this.startObserver()}unmount(){}}function nx({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const rx={inView:{Feature:tx},tap:{Feature:Yw},focus:{Feature:$w},hover:{Feature:qw}};function Y1(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function el(e,t,n){const r=e.getProps();return Vf(r,t,n!==void 0?n:r.custom,e)}const Wn=e=>e*1e3,gn=e=>e/1e3,ix={type:"spring",stiffness:500,damping:25,restSpeed:10},ox=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),sx={type:"keyframes",duration:.8},ax={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},lx=(e,{keyframes:t})=>t.length>2?sx:tr.has(e)?e.startsWith("scale")?ox(t[1]):ix:ax;function ux({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}function Bf(e,t){return e[t]||e.default||e}const cx=e=>e!==null;function tl(e,{repeat:t,repeatType:n="loop"},r){const i=e.filter(cx),o=t&&n!=="loop"&&t%2===1?0:i.length-1;return!o||r===void 0?i[o]:r}let Ks;function fx(){Ks=void 0}const Kn={now:()=>(Ks===void 0&&Kn.set(Ve.isProcessing||Y3.useManualTiming?Ve.timestamp:performance.now()),Ks),set:e=>{Ks=e,queueMicrotask(fx)}},Z1=e=>/^0[^.\s]+$/u.test(e);function hx(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||Z1(e):!0}const X1=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),dx=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function px(e){const t=dx.exec(e);if(!t)return[,];const[,n,r,i]=t;return[`--${n??r}`,i]}function Q1(e,t,n=1){const[r,i]=px(e);if(!r)return;const o=window.getComputedStyle(t).getPropertyValue(r);if(o){const s=o.trim();return X1(s)?parseFloat(s):s}return Pf(i)?Q1(i,t,n+1):i}const mx=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),Pd=e=>e===Pi||e===W,Dd=(e,t)=>parseFloat(e.split(", ")[t]),Md=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/u);if(i)return Dd(i[1],t);{const o=r.match(/^matrix\((.+)\)$/u);return o?Dd(o[1],e):0}},gx=new Set(["x","y","z"]),vx=Ko.filter(e=>!gx.has(e));function yx(e){const t=[];return vx.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}const wi={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:Md(4,13),y:Md(5,14)};wi.translateX=wi.x;wi.translateY=wi.y;const J1=e=>t=>t.test(e),wx={test:e=>e==="auto",parse:e=>e},e0=[Pi,W,rn,Tn,Ew,Sw,wx],Rd=e=>e0.find(J1(e)),Er=new Set;let rc=!1,ic=!1;function t0(){if(ic){const e=Array.from(Er).filter(r=>r.needsMeasurement),t=new Set(e.map(r=>r.element)),n=new Map;t.forEach(r=>{const i=yx(r);i.length&&(n.set(r,i),r.render())}),e.forEach(r=>r.measureInitialState()),t.forEach(r=>{r.render();const i=n.get(r);i&&i.forEach(([o,s])=>{var a;(a=r.getValue(o))===null||a===void 0||a.set(s)})}),e.forEach(r=>r.measureEndState()),e.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}ic=!1,rc=!1,Er.forEach(e=>e.complete()),Er.clear()}function n0(){Er.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(ic=!0)})}function xx(){n0(),t0()}class Nf{constructor(t,n,r,i,o,s=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=r,this.motionValue=i,this.element=o,this.isAsync=s}scheduleResolve(){this.isScheduled=!0,this.isAsync?(Er.add(this),rc||(rc=!0,ae.read(n0),ae.resolveKeyframes(t0))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:r,motionValue:i}=this;for(let o=0;o<t.length;o++)if(t[o]===null)if(o===0){const s=i==null?void 0:i.get(),a=t[t.length-1];if(s!==void 0)t[0]=s;else if(r&&n){const l=r.readValue(n,a);l!=null&&(t[0]=l)}t[0]===void 0&&(t[0]=a),i&&s===void 0&&i.set(t[0])}else t[o]=t[o-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),Er.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,Er.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const Ff=(e,t)=>n=>!!(qo(n)&&xw.test(n)&&n.startsWith(e)||t&&!_w(n)&&Object.prototype.hasOwnProperty.call(n,t)),r0=(e,t,n)=>r=>{if(!qo(r))return r;const[i,o,s,a]=r.match(Df);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(s),alpha:a!==void 0?parseFloat(a):1}},_x=e=>Zn(0,255,e),jl={...Pi,transform:e=>Math.round(_x(e))},xr={test:Ff("rgb","red"),parse:r0("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+jl.transform(e)+", "+jl.transform(t)+", "+jl.transform(n)+", "+so(oo.transform(r))+")"};function Sx(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const oc={test:Ff("#"),parse:Sx,transform:xr.transform},Xr={test:Ff("hsl","hue"),parse:r0("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+rn.transform(so(t))+", "+rn.transform(so(n))+", "+so(oo.transform(r))+")"},Ue={test:e=>xr.test(e)||oc.test(e)||Xr.test(e),parse:e=>xr.test(e)?xr.parse(e):Xr.test(e)?Xr.parse(e):oc.parse(e),transform:e=>qo(e)?e:e.hasOwnProperty("red")?xr.transform(e):Xr.transform(e)};function Ex(e){var t,n;return isNaN(e)&&qo(e)&&(((t=e.match(Df))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(ww))===null||n===void 0?void 0:n.length)||0)>0}const i0="number",o0="color",Ax="var",Cx="var(",Od="${}",bx=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Ro(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[];let o=0;const a=t.replace(bx,l=>(Ue.test(l)?(r.color.push(o),i.push(o0),n.push(Ue.parse(l))):l.startsWith(Cx)?(r.var.push(o),i.push(Ax),n.push(l)):(r.number.push(o),i.push(i0),n.push(parseFloat(l))),++o,Od)).split(Od);return{values:n,split:a,indexes:r,types:i}}function s0(e){return Ro(e).values}function a0(e){const{split:t,types:n}=Ro(e),r=t.length;return i=>{let o="";for(let s=0;s<r;s++)if(o+=t[s],i[s]!==void 0){const a=n[s];a===i0?o+=so(i[s]):a===o0?o+=Ue.transform(i[s]):o+=i[s]}return o}}const Tx=e=>typeof e=="number"?0:e;function kx(e){const t=s0(e);return a0(e)(t.map(Tx))}const Xn={test:Ex,parse:s0,createTransformer:a0,getAnimatableNone:kx},Px=new Set(["brightness","contrast","saturate","opacity"]);function Dx(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Df)||[];if(!r)return e;const i=n.replace(r,"");let o=Px.has(t)?1:0;return r!==n&&(o*=100),t+"("+o+i+")"}const Mx=/\b([a-z-]*)\(.*?\)/gu,sc={...Xn,getAnimatableNone:e=>{const t=e.match(Mx);return t?t.map(Dx).join(" "):e}},Rx={...O1,color:Ue,backgroundColor:Ue,outlineColor:Ue,fill:Ue,stroke:Ue,borderColor:Ue,borderTopColor:Ue,borderRightColor:Ue,borderBottomColor:Ue,borderLeftColor:Ue,filter:sc,WebkitFilter:sc},zf=e=>Rx[e];function l0(e,t){let n=zf(e);return n!==sc&&(n=Xn),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const Ox=new Set(["auto","none","0"]);function Ix(e,t,n){let r=0,i;for(;r<e.length&&!i;){const o=e[r];typeof o=="string"&&!Ox.has(o)&&Ro(o).values.length&&(i=e[r]),r++}if(i&&n)for(const o of t)e[o]=l0(n,i)}class u0 extends Nf{constructor(t,n,r,i){super(t,n,r,i,i==null?void 0:i.owner,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:r}=this;if(!n.current)return;super.readKeyframes();for(let l=0;l<t.length;l++){let u=t[l];if(typeof u=="string"&&(u=u.trim(),Pf(u))){const c=Q1(u,n.current);c!==void 0&&(t[l]=c),l===t.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!mx.has(r)||t.length!==2)return;const[i,o]=t,s=Rd(i),a=Rd(o);if(s!==a)if(Pd(s)&&Pd(a))for(let l=0;l<t.length;l++){const u=t[l];typeof u=="string"&&(t[l]=parseFloat(u))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,r=[];for(let i=0;i<t.length;i++)hx(t[i])&&r.push(i);r.length&&Ix(t,r,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:r}=this;if(!t.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=wi[r](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&t.getValue(r,i).jump(i,!1)}measureEndState(){var t;const{element:n,name:r,unresolvedKeyframes:i}=this;if(!n.current)return;const o=n.getValue(r);o&&o.jump(this.measuredOrigin,!1);const s=i.length-1,a=i[s];i[s]=wi[r](n.measureViewportBox(),window.getComputedStyle(n.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),!((t=this.removedTransforms)===null||t===void 0)&&t.length&&this.removedTransforms.forEach(([l,u])=>{n.getValue(l).set(u)}),this.resolveNoneKeyframes()}}function c0(e){let t;return()=>(t===void 0&&(t=e()),t)}const Id=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(Xn.test(e)||e==="0")&&!e.startsWith("url("));function Lx(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function Vx(e,t,n,r){const i=e[0];if(i===null)return!1;if(t==="display"||t==="visibility")return!0;const o=e[e.length-1],s=Id(i,t),a=Id(o,t);return!s||!a?!1:Lx(e)||n==="spring"&&r}class f0{constructor({autoplay:t=!0,delay:n=0,type:r="keyframes",repeat:i=0,repeatDelay:o=0,repeatType:s="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.options={autoplay:t,delay:n,type:r,repeat:i,repeatDelay:o,repeatType:s,...a},this.updateFinishedPromise()}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&xx(),this._resolved}onKeyframesResolved(t,n){this.hasAttemptedResolve=!0;const{name:r,type:i,velocity:o,delay:s,onComplete:a,onUpdate:l,isGenerator:u}=this.options;if(!u&&!Vx(t,r,i,o))if(s)this.options.duration=0;else{l==null||l(tl(t,this.options,n)),a==null||a(),this.resolveFinishedPromise();return}const c=this.initPlayback(t,n);c!==!1&&(this._resolved={keyframes:t,finalKeyframe:n,...c},this.onPostResolved())}onPostResolved(){}then(t,n){return this.currentFinishedPromise.then(t,n)}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}function h0(e,t){return t?e*(1e3/t):0}const Bx=5;function d0(e,t,n){const r=Math.max(t-Bx,0);return h0(n-e(r),t-r)}const Gl=.001,Nx=.01,Fx=10,zx=.05,Ux=1;function jx({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let i,o,s=1-t;s=Zn(zx,Ux,s),e=Zn(Nx,Fx,gn(e)),s<1?(i=u=>{const c=u*s,h=c*e,f=c-n,v=ac(u,s),p=Math.exp(-h);return Gl-f/v*p},o=u=>{const h=u*s*e,f=h*n+n,v=Math.pow(s,2)*Math.pow(u,2)*e,p=Math.exp(-h),g=ac(Math.pow(u,2),s);return(-i(u)+Gl>0?-1:1)*((f-v)*p)/g}):(i=u=>{const c=Math.exp(-u*e),h=(u-n)*e+1;return-Gl+c*h},o=u=>{const c=Math.exp(-u*e),h=(n-u)*(e*e);return c*h});const a=5/e,l=Hx(i,o,a);if(e=Wn(e),isNaN(l))return{stiffness:100,damping:10,duration:e};{const u=Math.pow(l,2)*r;return{stiffness:u,damping:s*2*Math.sqrt(r*u),duration:e}}}const Gx=12;function Hx(e,t,n){let r=n;for(let i=1;i<Gx;i++)r=r-e(r)/t(r);return r}function ac(e,t){return e*Math.sqrt(1-t*t)}const Wx=["duration","bounce"],Kx=["stiffness","damping","mass"];function Ld(e,t){return t.some(n=>e[n]!==void 0)}function qx(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!Ld(e,Kx)&&Ld(e,Wx)){const n=jx(e);t={...t,...n,mass:1},t.isResolvedFromDuration=!0}return t}function p0({keyframes:e,restDelta:t,restSpeed:n,...r}){const i=e[0],o=e[e.length-1],s={done:!1,value:i},{stiffness:a,damping:l,mass:u,duration:c,velocity:h,isResolvedFromDuration:f}=qx({...r,velocity:-gn(r.velocity||0)}),v=h||0,p=l/(2*Math.sqrt(a*u)),g=o-i,S=gn(Math.sqrt(a/u)),y=Math.abs(g)<5;n||(n=y?.01:2),t||(t=y?.005:.5);let w;if(p<1){const _=ac(S,p);w=E=>{const b=Math.exp(-p*S*E);return o-b*((v+p*S*g)/_*Math.sin(_*E)+g*Math.cos(_*E))}}else if(p===1)w=_=>o-Math.exp(-S*_)*(g+(v+S*g)*_);else{const _=S*Math.sqrt(p*p-1);w=E=>{const b=Math.exp(-p*S*E),k=Math.min(_*E,300);return o-b*((v+p*S*g)*Math.sinh(k)+_*g*Math.cosh(k))/_}}return{calculatedDuration:f&&c||null,next:_=>{const E=w(_);if(f)s.done=_>=c;else{let b=v;_!==0&&(p<1?b=d0(w,_,E):b=0);const k=Math.abs(b)<=n,P=Math.abs(o-E)<=t;s.done=k&&P}return s.value=s.done?o:E,s}}}function Vd({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:c}){const h=e[0],f={done:!1,value:h},v=C=>a!==void 0&&C<a||l!==void 0&&C>l,p=C=>a===void 0?l:l===void 0||Math.abs(a-C)<Math.abs(l-C)?a:l;let g=n*t;const S=h+g,y=s===void 0?S:s(S);y!==S&&(g=y-h);const w=C=>-g*Math.exp(-C/r),_=C=>y+w(C),E=C=>{const D=w(C),R=_(C);f.done=Math.abs(D)<=u,f.value=f.done?y:R};let b,k;const P=C=>{v(f.value)&&(b=C,k=p0({keyframes:[f.value,p(f.value)],velocity:d0(_,C,f.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return P(0),{calculatedDuration:null,next:C=>{let D=!1;return!k&&b===void 0&&(D=!0,E(C),P(C)),b!==void 0&&C>=b?k.next(C-b):(!D&&E(C),f)}}}const m0=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,$x=1e-7,Yx=12;function Zx(e,t,n,r,i){let o,s,a=0;do s=t+(n-t)/2,o=m0(s,r,i)-e,o>0?n=s:t=s;while(Math.abs(o)>$x&&++a<Yx);return s}function Yo(e,t,n,r){if(e===t&&n===r)return He;const i=o=>Zx(o,0,1,e,n);return o=>o===0||o===1?o:m0(i(o),t,r)}const Xx=Yo(.42,0,1,1),Qx=Yo(0,0,.58,1),g0=Yo(.42,0,.58,1),Jx=e=>Array.isArray(e)&&typeof e[0]!="number",v0=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,y0=e=>t=>1-e(1-t),Uf=e=>1-Math.sin(Math.acos(e)),w0=y0(Uf),e_=v0(Uf),x0=Yo(.33,1.53,.69,.99),jf=y0(x0),t_=v0(jf),n_=e=>(e*=2)<1?.5*jf(e):.5*(2-Math.pow(2,-10*(e-1))),Bd={linear:He,easeIn:Xx,easeInOut:g0,easeOut:Qx,circIn:Uf,circInOut:e_,circOut:w0,backIn:jf,backInOut:t_,backOut:x0,anticipate:n_},Nd=e=>{if(Array.isArray(e)){ec(e.length===4);const[t,n,r,i]=e;return Yo(t,n,r,i)}else if(typeof e=="string")return ec(Bd[e]!==void 0),Bd[e];return e},Oo=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},we=(e,t,n)=>e+(t-e)*n;function Hl(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function r_({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,o=0,s=0;if(!t)i=o=s=n;else{const a=n<.5?n*(1+t):n+t-n*t,l=2*n-a;i=Hl(l,a,e+1/3),o=Hl(l,a,e),s=Hl(l,a,e-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(s*255),alpha:r}}function Ea(e,t){return n=>n>0?t:e}const Wl=(e,t,n)=>{const r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},i_=[oc,xr,Xr],o_=e=>i_.find(t=>t.test(e));function Fd(e){const t=o_(e);if(!t)return!1;let n=t.parse(e);return t===Xr&&(n=r_(n)),n}const zd=(e,t)=>{const n=Fd(e),r=Fd(t);if(!n||!r)return Ea(e,t);const i={...n};return o=>(i.red=Wl(n.red,r.red,o),i.green=Wl(n.green,r.green,o),i.blue=Wl(n.blue,r.blue,o),i.alpha=we(n.alpha,r.alpha,o),xr.transform(i))},lc=new Set(["none","hidden"]);function s_(e,t){return lc.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function a_(e,t){return n=>we(e,t,n)}function Gf(e){return typeof e=="number"?a_:typeof e=="string"?Pf(e)?Ea:Ue.test(e)?zd:c_:Array.isArray(e)?_0:typeof e=="object"?Ue.test(e)?zd:l_:Ea}function _0(e,t){const n=[...e],r=n.length,i=e.map((o,s)=>Gf(o)(o,t[s]));return o=>{for(let s=0;s<r;s++)n[s]=i[s](o);return n}}function l_(e,t){const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=Gf(e[i])(e[i],t[i]));return i=>{for(const o in r)n[o]=r[o](i);return n}}function u_(e,t){var n;const r=[],i={color:0,var:0,number:0};for(let o=0;o<t.values.length;o++){const s=t.types[o],a=e.indexes[s][i[s]],l=(n=e.values[a])!==null&&n!==void 0?n:0;r[o]=l,i[s]++}return r}const c_=(e,t)=>{const n=Xn.createTransformer(t),r=Ro(e),i=Ro(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?lc.has(e)&&!i.values.length||lc.has(t)&&!r.values.length?s_(e,t):mn(_0(u_(r,i),i.values),n):Ea(e,t)};function S0(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?we(e,t,n):Gf(e)(e,t)}function f_(e,t,n){const r=[],i=n||S0,o=e.length-1;for(let s=0;s<o;s++){let a=i(e[s],e[s+1]);if(t){const l=Array.isArray(t)?t[s]||He:t;a=mn(l,a)}r.push(a)}return r}function h_(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const o=e.length;if(ec(o===t.length),o===1)return()=>t[0];if(o===2&&e[0]===e[1])return()=>t[1];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());const s=f_(t,r,i),a=s.length,l=u=>{let c=0;if(a>1)for(;c<e.length-2&&!(u<e[c+1]);c++);const h=Oo(e[c],e[c+1],u);return s[c](h)};return n?u=>l(Zn(e[0],e[o-1],u)):l}function d_(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=Oo(0,t,r);e.push(we(n,1,i))}}function p_(e){const t=[0];return d_(t,e.length-1),t}function m_(e,t){return e.map(n=>n*t)}function g_(e,t){return e.map(()=>t||g0).splice(0,e.length-1)}function Aa({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const i=Jx(r)?r.map(Nd):Nd(r),o={done:!1,value:t[0]},s=m_(n&&n.length===t.length?n:p_(t),e),a=h_(s,t,{ease:Array.isArray(i)?i:g_(t,i)});return{calculatedDuration:e,next:l=>(o.value=a(l),o.done=l>=e,o)}}const Ud=2e4;function v_(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<Ud;)t+=n,r=e.next(t);return t>=Ud?1/0:t}const y_=e=>{const t=({timestamp:n})=>e(n);return{start:()=>ae.update(t,!0),stop:()=>_n(t),now:()=>Ve.isProcessing?Ve.timestamp:Kn.now()}},w_={decay:Vd,inertia:Vd,tween:Aa,keyframes:Aa,spring:p0},x_=e=>e/100;class Hf extends f0{constructor({KeyframeResolver:t=Nf,...n}){super(n),this.holdTime=null,this.startTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:a}=this.options;a&&a()};const{name:r,motionValue:i,keyframes:o}=this.options,s=(a,l)=>this.onKeyframesResolved(a,l);r&&i&&i.owner?this.resolver=i.owner.resolveKeyframes(o,s,r,i):this.resolver=new t(o,s,r,i),this.resolver.scheduleResolve()}initPlayback(t){const{type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:o,velocity:s=0}=this.options,a=w_[n]||Aa;let l,u;a!==Aa&&typeof t[0]!="number"&&(l=mn(x_,S0(t[0],t[1])),t=[0,100]);const c=a({...this.options,keyframes:t});o==="mirror"&&(u=a({...this.options,keyframes:[...t].reverse(),velocity:-s})),c.calculatedDuration===null&&(c.calculatedDuration=v_(c));const{calculatedDuration:h}=c,f=h+i,v=f*(r+1)-i;return{generator:c,mirroredGenerator:u,mapPercentToKeyframes:l,calculatedDuration:h,resolvedDuration:f,totalDuration:v}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!t?this.pause():this.state=this.pendingPlayState}tick(t,n=!1){const{resolved:r}=this;if(!r){const{keyframes:C}=this.options;return{done:!0,value:C[C.length-1]}}const{finalKeyframe:i,generator:o,mirroredGenerator:s,mapPercentToKeyframes:a,keyframes:l,calculatedDuration:u,totalDuration:c,resolvedDuration:h}=r;if(this.startTime===null)return o.next(0);const{delay:f,repeat:v,repeatType:p,repeatDelay:g,onUpdate:S}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-c/this.speed,this.startTime)),n?this.currentTime=t:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const y=this.currentTime-f*(this.speed>=0?1:-1),w=this.speed>=0?y<0:y>c;this.currentTime=Math.max(y,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=c);let _=this.currentTime,E=o;if(v){const C=Math.min(this.currentTime,c)/h;let D=Math.floor(C),R=C%1;!R&&C>=1&&(R=1),R===1&&D--,D=Math.min(D,v+1),!!(D%2)&&(p==="reverse"?(R=1-R,g&&(R-=g/h)):p==="mirror"&&(E=s)),_=Zn(0,1,R)*h}const b=w?{done:!1,value:l[0]}:E.next(_);a&&(b.value=a(b.value));let{done:k}=b;!w&&u!==null&&(k=this.speed>=0?this.currentTime>=c:this.currentTime<=0);const P=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&k);return P&&i!==void 0&&(b.value=tl(l,this.options,i)),S&&S(b.value),P&&this.finish(),b}get duration(){const{resolved:t}=this;return t?gn(t.calculatedDuration):0}get time(){return gn(this.currentTime)}set time(t){t=Wn(t),this.currentTime=t,this.holdTime!==null||this.speed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=gn(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:t=y_,onPlay:n}=this.options;this.driver||(this.driver=t(i=>this.tick(i))),n&&n();const r=this.driver.now();this.holdTime!==null?this.startTime=r-this.holdTime:(!this.startTime||this.state==="finished")&&(this.startTime=r),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(t=this.currentTime)!==null&&t!==void 0?t:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}const E0=e=>Array.isArray(e)&&typeof e[0]=="number";function A0(e){return!!(!e||typeof e=="string"&&e in Wf||E0(e)||Array.isArray(e)&&e.every(A0))}const qi=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Wf={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:qi([0,.65,.55,1]),circOut:qi([.55,0,1,.45]),backIn:qi([.31,.01,.66,-.59]),backOut:qi([.33,1.53,.69,.99])};function __(e){return C0(e)||Wf.easeOut}function C0(e){if(e)return E0(e)?qi(e):Array.isArray(e)?e.map(__):Wf[e]}function S_(e,t,n,{delay:r=0,duration:i=300,repeat:o=0,repeatType:s="loop",ease:a,times:l}={}){const u={[t]:n};l&&(u.offset=l);const c=C0(a);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:s==="reverse"?"alternate":"normal"})}const E_=c0(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Ca=10,A_=2e4;function C_(e){return e.type==="spring"||!A0(e.ease)}function b_(e,t){const n=new Hf({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0});let r={done:!1,value:e[0]};const i=[];let o=0;for(;!r.done&&o<A_;)r=n.sample(o),i.push(r.value),o+=Ca;return{times:void 0,keyframes:i,duration:o-Ca,ease:"linear"}}class jd extends f0{constructor(t){super(t);const{name:n,motionValue:r,keyframes:i}=this.options;this.resolver=new u0(i,(o,s)=>this.onKeyframesResolved(o,s),n,r),this.resolver.scheduleResolve()}initPlayback(t,n){var r;let{duration:i=300,times:o,ease:s,type:a,motionValue:l,name:u}=this.options;if(!(!((r=l.owner)===null||r===void 0)&&r.current))return!1;if(C_(this.options)){const{onComplete:h,onUpdate:f,motionValue:v,...p}=this.options,g=b_(t,p);t=g.keyframes,t.length===1&&(t[1]=t[0]),i=g.duration,o=g.times,s=g.ease,a="keyframes"}const c=S_(l.owner.current,u,t,{...this.options,duration:i,times:o,ease:s});return c.startTime=Kn.now(),this.pendingTimeline?(c.timeline=this.pendingTimeline,this.pendingTimeline=void 0):c.onfinish=()=>{const{onComplete:h}=this.options;l.set(tl(t,this.options,n)),h&&h(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:i,times:o,type:a,ease:s,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:n}=t;return gn(n)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:n}=t;return gn(n.currentTime||0)}set time(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.currentTime=Wn(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:n}=t;return n.playbackRate}set speed(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:n}=t;return n.playState}attachTimeline(t){if(!this._resolved)this.pendingTimeline=t;else{const{resolved:n}=this;if(!n)return He;const{animation:r}=n;r.timeline=t,r.onfinish=null}return He}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;const{resolved:t}=this;if(!t)return;const{animation:n,keyframes:r,duration:i,type:o,ease:s,times:a}=t;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:u,onUpdate:c,onComplete:h,...f}=this.options,v=new Hf({...f,keyframes:r,duration:i,type:o,ease:s,times:a,isGenerator:!0}),p=Wn(this.time);u.setWithVelocity(v.sample(p-Ca).value,v.sample(p).value,Ca)}const{onStop:l}=this.options;l&&l(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:n,name:r,repeatDelay:i,repeatType:o,damping:s,type:a}=t;return E_()&&r&&U1.has(r)&&n&&n.owner&&n.owner.current instanceof HTMLElement&&!n.owner.getProps().onUpdate&&!i&&o!=="mirror"&&s!==0&&a!=="inertia"}}function T_(e,t){let n;const r=()=>{const{currentTime:i}=t,s=(i===null?0:i.value)/100;n!==s&&e(s),n=s};return ae.update(r,!0),()=>_n(r)}const k_=c0(()=>window.ScrollTimeline!==void 0);class P_{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}then(t,n){return Promise.all(this.animations).then(t).catch(n)}getAll(t){return this.animations[0][t]}setAll(t,n){for(let r=0;r<this.animations.length;r++)this.animations[r][t]=n}attachTimeline(t){const n=this.animations.map(r=>{if(k_()&&r.attachTimeline)r.attachTimeline(t);else return r.pause(),T_(i=>{r.time=r.duration*i},t)});return()=>{n.forEach((r,i)=>{r&&r(),this.animations[i].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get duration(){let t=0;for(let n=0;n<this.animations.length;n++)t=Math.max(t,this.animations[n].duration);return t}runAll(t){this.animations.forEach(n=>n[t]())}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}const Kf=(e,t,n,r={},i,o,s)=>a=>{const l=Bf(r,e)||{},u=l.delay||r.delay||0;let{elapsed:c=0}=r;c=c-Wn(u);let h={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...l,delay:-c,onUpdate:v=>{t.set(v),l.onUpdate&&l.onUpdate(v)},onComplete:()=>{a(),l.onComplete&&l.onComplete(),s&&s()},onStop:s,name:e,motionValue:t,element:o?void 0:i};ux(l)||(h={...h,...lx(e,h)}),h.duration&&(h.duration=Wn(h.duration)),h.repeatDelay&&(h.repeatDelay=Wn(h.repeatDelay)),h.from!==void 0&&(h.keyframes[0]=h.from);let f=!1;if((h.type===!1||h.duration===0&&!h.repeatDelay)&&(h.duration=0,h.delay===0&&(f=!0)),f&&!o&&t.get()!==void 0){const v=tl(h.keyframes,l);if(v!==void 0)return ae.update(()=>{h.onUpdate(v),h.onComplete()}),new P_([])}return!o&&jd.supports(h)?new jd(h):new Hf(h)};class qf{constructor(){this.subscriptions=[]}add(t){return Xa(this.subscriptions,t),()=>Qa(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let o=0;o<i;o++){const s=this.subscriptions[o];s&&s(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Gd=30,D_=e=>!isNaN(parseFloat(e));class b0{constructor(t,n={}){this.version="11.3.17",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,i=!0)=>{const o=Kn.now();this.updatedAt!==o&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=Kn.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=D_(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new qf);const r=this.events[t].add(n);return t==="change"?()=>{r(),ae.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=Kn.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>Gd)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Gd);return h0(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Io(e,t){return new b0(e,t)}function M_(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Io(n))}function R_(e,t){const n=el(e,t);let{transitionEnd:r={},transition:i={},...o}=n||{};o={...o,...r};for(const s in o){const a=Nw(o[s]);M_(e,s,a)}}function T0(e){return e.getProps()[A1]}class O_ extends b0{constructor(){super(...arguments),this.output=[],this.counts=new Map}add(t){const n=j1(t);if(!n)return;const r=this.counts.get(n)||0;this.counts.set(n,r+1),r===0&&(this.output.push(n),this.update());let i=!1;return()=>{if(i)return;i=!0;const o=this.counts.get(n)-1;this.counts.set(n,o),o===0&&(Qa(this.output,n),this.update())}}update(){this.set(this.output.length?this.output.join(", "):"auto")}}function I_(e){return!!(Ge(e)&&e.add)}function uc(e,t){var n;if(!e.applyWillChange)return;let r=e.getValue("willChange");if(!r&&!(!((n=e.props.style)===null||n===void 0)&&n.willChange)&&(r=new O_("auto"),e.addValue("willChange",r)),I_(r))return r.add(t)}function L_({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function k0(e,t,{delay:n=0,transitionOverride:r,type:i}={}){var o;let{transition:s=e.getDefaultTransition(),transitionEnd:a,...l}=t;r&&(s=r);const u=[],c=i&&e.animationState&&e.animationState.getState()[i];for(const h in l){const f=e.getValue(h,(o=e.latestValues[h])!==null&&o!==void 0?o:null),v=l[h];if(v===void 0||c&&L_(c,h))continue;const p={delay:n,elapsed:0,...Bf(s||{},h)};let g=!1;if(window.HandoffAppearAnimations){const y=T0(e);if(y){const w=window.HandoffAppearAnimations(y,h,f,ae);w!==null&&(p.elapsed=w,g=!0)}}f.start(Kf(h,f,v,e.shouldReduceMotion&&tr.has(h)?{type:!1}:p,e,g,uc(e,h)));const S=f.animation;S&&u.push(S)}return a&&Promise.all(u).then(()=>{ae.update(()=>{a&&R_(e,a)})}),u}function cc(e,t,n={}){var r;const i=el(e,t,n.type==="exit"?(r=e.presenceContext)===null||r===void 0?void 0:r.custom:void 0);let{transition:o=e.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(o=n.transitionOverride);const s=i?()=>Promise.all(k0(e,i,n)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(u=0)=>{const{delayChildren:c=0,staggerChildren:h,staggerDirection:f}=o;return V_(e,t,c+u,h,f,n)}:()=>Promise.resolve(),{when:l}=o;if(l){const[u,c]=l==="beforeChildren"?[s,a]:[a,s];return u().then(()=>c())}else return Promise.all([s(),a(n.delay)])}function V_(e,t,n=0,r=0,i=1,o){const s=[],a=(e.variantChildren.size-1)*r,l=i===1?(u=0)=>u*r:(u=0)=>a-u*r;return Array.from(e.variantChildren).sort(B_).forEach((u,c)=>{u.notify("AnimationStart",t),s.push(cc(u,t,{...o,delay:n+l(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(s)}function B_(e,t){return e.sortNodePosition(t)}function N_(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(o=>cc(e,o,n));r=Promise.all(i)}else if(typeof t=="string")r=cc(e,t,n);else{const i=typeof t=="function"?el(e,t,n.custom):t;r=Promise.all(k0(e,i,n))}return r.then(()=>{ae.postRender(()=>{e.notify("AnimationComplete",t)})})}const F_=[...bf].reverse(),z_=bf.length;function U_(e){return t=>Promise.all(t.map(({animation:n,options:r})=>N_(e,n,r)))}function j_(e){let t=U_(e),n=Hd(),r=!0;const i=l=>(u,c)=>{var h;const f=el(e,c,l==="exit"?(h=e.presenceContext)===null||h===void 0?void 0:h.custom:void 0);if(f){const{transition:v,transitionEnd:p,...g}=f;u={...u,...g,...p}}return u};function o(l){t=l(e)}function s(l){const u=e.getProps(),c=e.getVariantContext(!0)||{},h=[],f=new Set;let v={},p=1/0;for(let S=0;S<z_;S++){const y=F_[S],w=n[y],_=u[y]!==void 0?u[y]:c[y],E=Do(_),b=y===l?w.isActive:null;b===!1&&(p=S);let k=_===c[y]&&_!==u[y]&&E;if(k&&r&&e.manuallyAnimateOnMount&&(k=!1),w.protectedKeys={...v},!w.isActive&&b===null||!_&&!w.prevProp||Mo(_)||typeof _=="boolean")continue;let C=G_(w.prevProp,_)||y===l&&w.isActive&&!k&&E||S>p&&E,D=!1;const R=Array.isArray(_)?_:[_];let B=R.reduce(i(y),{});b===!1&&(B={});const{prevResolvedValues:Y={}}=w,J={...Y,...B},ue=j=>{C=!0,f.has(j)&&(D=!0,f.delete(j)),w.needsAnimating[j]=!0;const ee=e.getValue(j);ee&&(ee.liveStyle=!1)};for(const j in J){const ee=B[j],te=Y[j];if(v.hasOwnProperty(j))continue;let N=!1;tc(ee)&&tc(te)?N=!Y1(ee,te):N=ee!==te,N?ee!=null?ue(j):f.add(j):ee!==void 0&&f.has(j)?ue(j):w.protectedKeys[j]=!0}w.prevProp=_,w.prevResolvedValues=B,w.isActive&&(v={...v,...B}),r&&e.blockInitialAnimation&&(C=!1),C&&(!k||D)&&h.push(...R.map(j=>({animation:j,options:{type:y}})))}if(f.size){const S={};f.forEach(y=>{const w=e.getBaseTarget(y),_=e.getValue(y);_&&(_.liveStyle=!0),S[y]=w??null}),h.push({animation:S})}let g=!!h.length;return r&&(u.initial===!1||u.initial===u.animate)&&!e.manuallyAnimateOnMount&&(g=!1),r=!1,g?t(h):Promise.resolve()}function a(l,u){var c;if(n[l].isActive===u)return Promise.resolve();(c=e.variantChildren)===null||c===void 0||c.forEach(f=>{var v;return(v=f.animationState)===null||v===void 0?void 0:v.setActive(l,u)}),n[l].isActive=u;const h=s(l);for(const f in n)n[f].protectedKeys={};return h}return{animateChanges:s,setActive:a,setAnimateFunction:o,getState:()=>n,reset:()=>{n=Hd(),r=!0}}}function G_(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Y1(t,e):!1}function ar(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Hd(){return{animate:ar(!0),whileInView:ar(),whileHover:ar(),whileTap:ar(),whileDrag:ar(),whileFocus:ar(),exit:ar()}}class H_ extends nr{constructor(t){super(t),t.animationState||(t.animationState=j_(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();Mo(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)===null||t===void 0||t.call(this)}}let W_=0;class K_ extends nr{constructor(){super(...arguments),this.id=W_++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;const i=this.node.animationState.setActive("exit",!t);n&&!t&&i.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const q_={animation:{Feature:H_},exit:{Feature:K_}},Wd=(e,t)=>Math.abs(e-t);function $_(e,t){const n=Wd(e.x,t.x),r=Wd(e.y,t.y);return Math.sqrt(n**2+r**2)}class P0{constructor(t,n,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=ql(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,v=$_(h.offset,{x:0,y:0})>=3;if(!f&&!v)return;const{point:p}=h,{timestamp:g}=Ve;this.history.push({...p,timestamp:g});const{onStart:S,onMove:y}=this.handlers;f||(S&&S(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),y&&y(this.lastMoveEvent,h)},this.handlePointerMove=(h,f)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=Kl(f,this.transformPagePoint),ae.update(this.updatePoint,!0)},this.handlePointerUp=(h,f)=>{this.end();const{onEnd:v,onSessionEnd:p,resumeAnimation:g}=this.handlers;if(this.dragSnapToOrigin&&g&&g(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const S=ql(h.type==="pointercancel"?this.lastMoveEventInfo:Kl(f,this.transformPagePoint),this.history);this.startEvent&&v&&v(h,S),p&&p(h,S)},!H1(t))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=r,this.contextWindow=i||window;const s=Ja(t),a=Kl(s,this.transformPagePoint),{point:l}=a,{timestamp:u}=Ve;this.history=[{...l,timestamp:u}];const{onSessionStart:c}=n;c&&c(t,ql(a,this.history)),this.removeListeners=mn(pn(this.contextWindow,"pointermove",this.handlePointerMove),pn(this.contextWindow,"pointerup",this.handlePointerUp),pn(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),_n(this.updatePoint)}}function Kl(e,t){return t?{point:t(e.point)}:e}function Kd(e,t){return{x:e.x-t.x,y:e.y-t.y}}function ql({point:e},t){return{point:e,delta:Kd(e,D0(t)),offset:Kd(e,Y_(t)),velocity:Z_(t,.1)}}function Y_(e){return e[0]}function D0(e){return e[e.length-1]}function Z_(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=D0(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>Wn(t)));)n--;if(!r)return{x:0,y:0};const o=gn(i.timestamp-r.timestamp);if(o===0)return{x:0,y:0};const s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}const M0=1e-4,X_=1-M0,Q_=1+M0,R0=.01,J_=0-R0,e6=0+R0;function ht(e){return e.max-e.min}function t6(e,t,n){return Math.abs(e-t)<=n}function qd(e,t,n,r=.5){e.origin=r,e.originPoint=we(t.min,t.max,e.origin),e.scale=ht(n)/ht(t),e.translate=we(n.min,n.max,e.origin)-e.originPoint,(e.scale>=X_&&e.scale<=Q_||isNaN(e.scale))&&(e.scale=1),(e.translate>=J_&&e.translate<=e6||isNaN(e.translate))&&(e.translate=0)}function ao(e,t,n,r){qd(e.x,t.x,n.x,r?r.originX:void 0),qd(e.y,t.y,n.y,r?r.originY:void 0)}function $d(e,t,n){e.min=n.min+t.min,e.max=e.min+ht(t)}function n6(e,t,n){$d(e.x,t.x,n.x),$d(e.y,t.y,n.y)}function Yd(e,t,n){e.min=t.min-n.min,e.max=e.min+ht(t)}function lo(e,t,n){Yd(e.x,t.x,n.x),Yd(e.y,t.y,n.y)}function r6(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?we(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?we(n,e,r.max):Math.min(e,n)),e}function Zd(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function i6(e,{top:t,left:n,bottom:r,right:i}){return{x:Zd(e.x,n,i),y:Zd(e.y,t,r)}}function Xd(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function o6(e,t){return{x:Xd(e.x,t.x),y:Xd(e.y,t.y)}}function s6(e,t){let n=.5;const r=ht(e),i=ht(t);return i>r?n=Oo(t.min,t.max-r,e.min):r>i&&(n=Oo(e.min,e.max-i,t.min)),Zn(0,1,n)}function a6(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const fc=.35;function l6(e=fc){return e===!1?e=0:e===!0&&(e=fc),{x:Qd(e,"left","right"),y:Qd(e,"top","bottom")}}function Qd(e,t,n){return{min:Jd(e,t),max:Jd(e,n)}}function Jd(e,t){return typeof e=="number"?e:e[t]||0}const ep=()=>({translate:0,scale:1,origin:0,originPoint:0}),Qr=()=>({x:ep(),y:ep()}),tp=()=>({min:0,max:0}),Ee=()=>({x:tp(),y:tp()});function St(e){return[e("x"),e("y")]}function O0({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function u6({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function c6(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function $l(e){return e===void 0||e===1}function hc({scale:e,scaleX:t,scaleY:n}){return!$l(e)||!$l(t)||!$l(n)}function hr(e){return hc(e)||I0(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function I0(e){return np(e.x)||np(e.y)}function np(e){return e&&e!=="0%"}function ba(e,t,n){const r=e-n,i=t*r;return n+i}function rp(e,t,n,r,i){return i!==void 0&&(e=ba(e,i,r)),ba(e,n,r)+t}function dc(e,t=0,n=1,r,i){e.min=rp(e.min,t,n,r,i),e.max=rp(e.max,t,n,r,i)}function L0(e,{x:t,y:n}){dc(e.x,t.translate,t.scale,t.originPoint),dc(e.y,n.translate,n.scale,n.originPoint)}const ip=.999999999999,op=1.0000000000001;function f6(e,t,n,r=!1){const i=n.length;if(!i)return;t.x=t.y=1;let o,s;for(let a=0;a<i;a++){o=n[a],s=o.projectionDelta;const{visualElement:l}=o.options;l&&l.props.style&&l.props.style.display==="contents"||(r&&o.options.layoutScroll&&o.scroll&&o!==o.root&&ei(e,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,L0(e,s)),r&&hr(o.latestValues)&&ei(e,o.latestValues))}t.x<op&&t.x>ip&&(t.x=1),t.y<op&&t.y>ip&&(t.y=1)}function Jr(e,t){e.min=e.min+t,e.max=e.max+t}function sp(e,t,n,r,i=.5){const o=we(e.min,e.max,i);dc(e,t,n,o,r)}function ei(e,t){sp(e.x,t.x,t.scaleX,t.scale,t.originX),sp(e.y,t.y,t.scaleY,t.scale,t.originY)}function V0(e,t){return O0(c6(e.getBoundingClientRect(),t))}function h6(e,t,n){const r=V0(e,n),{scroll:i}=t;return i&&(Jr(r.x,i.offset.x),Jr(r.y,i.offset.y)),r}const B0=({current:e})=>e?e.ownerDocument.defaultView:null,d6=new WeakMap;class p6{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=Ee(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const i=c=>{const{dragSnapToOrigin:h}=this.getProps();h?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(Ja(c,"page").point)},o=(c,h)=>{var f;const{drag:v,dragPropagation:p,onDragStart:g}=this.getProps();if(v&&!p&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=K1(v),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),St(y=>{let w=this.getAxisMotionValue(y).get()||0;if(rn.test(w)){const{projection:_}=this.visualElement;if(_&&_.layout){const E=_.layout.layoutBox[y];E&&(w=ht(E)*(parseFloat(w)/100))}}this.originPoint[y]=w}),g&&ae.postRender(()=>g(c,h)),(f=this.removeWillChange)===null||f===void 0||f.call(this),this.removeWillChange=uc(this.visualElement,"transform");const{animationState:S}=this.visualElement;S&&S.setActive("whileDrag",!0)},s=(c,h)=>{const{dragPropagation:f,dragDirectionLock:v,onDirectionLock:p,onDrag:g}=this.getProps();if(!f&&!this.openGlobalLock)return;const{offset:S}=h;if(v&&this.currentDirection===null){this.currentDirection=m6(S),this.currentDirection!==null&&p&&p(this.currentDirection);return}this.updateAxis("x",h.point,S),this.updateAxis("y",h.point,S),this.visualElement.render(),g&&g(c,h)},a=(c,h)=>this.stop(c,h),l=()=>St(c=>{var h;return this.getAnimationState(c)==="paused"&&((h=this.getAxisMotionValue(c).animation)===null||h===void 0?void 0:h.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new P0(t,{onSessionStart:i,onStart:o,onMove:s,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:B0(this.visualElement)})}stop(t,n){var r;(r=this.removeWillChange)===null||r===void 0||r.call(this);const i=this.isDragging;if(this.cancel(),!i)return;const{velocity:o}=n;this.startAnimation(o);const{onDragEnd:s}=this.getProps();s&&ae.postRender(()=>s(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!As(t,i,this.currentDirection))return;const o=this.getAxisMotionValue(t);let s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=r6(s,this.constraints[t],this.elastic[t])),o.set(s)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,o=this.constraints;n&&Zr(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=i6(i.layoutBox,n):this.constraints=!1,this.elastic=l6(r),o!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&St(s=>{this.constraints!==!1&&this.getAxisMotionValue(s)&&(this.constraints[s]=a6(i.layoutBox[s],this.constraints[s]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!Zr(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=h6(r,i.root,this.visualElement.getTransformPagePoint());let s=o6(i.layout.layoutBox,o);if(n){const a=n(u6(s));this.hasMutatedConstraints=!!a,a&&(s=O0(a))}return s}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:o,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=St(c=>{if(!As(c,n,this.currentDirection))return;let h=l&&l[c]||{};s&&(h={min:0,max:0});const f=i?200:1e6,v=i?40:1e7,p={type:"inertia",velocity:r?t[c]:0,bounceStiffness:f,bounceDamping:v,timeConstant:750,restDelta:1,restSpeed:10,...o,...h};return this.startAxisValueAnimation(c,p)});return Promise.all(u).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start(Kf(t,r,0,n,this.visualElement,!1,uc(this.visualElement,t)))}stopAnimation(){St(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){St(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps(),i=r[n];return i||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){St(n=>{const{drag:r}=this.getProps();if(!As(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:s,max:a}=i.layout.layoutBox[n];o.set(t[n]-we(s,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!Zr(n)||!r||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};St(s=>{const a=this.getAxisMotionValue(s);if(a&&this.constraints!==!1){const l=a.get();i[s]=s6({min:l,max:l},this.constraints[s])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),St(s=>{if(!As(s,t,null))return;const a=this.getAxisMotionValue(s),{min:l,max:u}=this.constraints[s];a.set(we(l,u,i[s]))})}addListeners(){if(!this.visualElement.current)return;d6.set(this.visualElement,this);const t=this.visualElement.current,n=pn(t,"pointerdown",l=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(l)}),r=()=>{const{dragConstraints:l}=this.getProps();Zr(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),ae.read(r);const s=hn(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(St(c=>{const h=this.getAxisMotionValue(c);h&&(this.originPoint[c]+=l[c].translate,h.set(h.get()+l[c].translate))}),this.visualElement.render())});return()=>{s(),n(),o(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:s=fc,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:o,dragElastic:s,dragMomentum:a}}}function As(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function m6(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class g6 extends nr{constructor(t){super(t),this.removeGroupControls=He,this.removeListeners=He,this.controls=new p6(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||He}unmount(){this.removeGroupControls(),this.removeListeners()}}const ap=e=>(t,n)=>{e&&ae.postRender(()=>e(t,n))};class v6 extends nr{constructor(){super(...arguments),this.removePointerDownListener=He}onPointerDown(t){this.session=new P0(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:B0(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:ap(t),onStart:ap(n),onMove:r,onEnd:(o,s)=>{delete this.session,i&&ae.postRender(()=>i(o,s))}}}mount(){this.removePointerDownListener=pn(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function y6(){const e=M.useContext(Ef);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,i=M.useId();M.useEffect(()=>r(i),[]);const o=M.useCallback(()=>n&&n(i),[i,n]);return!t&&n?[!1,o]:[!0]}const qs={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function lp(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const ji={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(W.test(e))e=parseFloat(e);else return e;const n=lp(e,t.target.x),r=lp(e,t.target.y);return`${n}% ${r}%`}},w6={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=Xn.parse(e);if(i.length>5)return r;const o=Xn.createTransformer(e),s=typeof i[0]!="number"?1:0,a=n.x.scale*t.x,l=n.y.scale*t.y;i[0+s]/=a,i[1+s]/=l;const u=we(a,l,.5);return typeof i[2+s]=="number"&&(i[2+s]/=u),typeof i[3+s]=="number"&&(i[3+s]/=u),o(i)}};class x6 extends M.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:o}=t;hw(_6),o&&(n.group&&n.group.add(o),r&&r.register&&i&&r.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),qs.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:o}=this.props,s=r.projection;return s&&(s.isPresent=o,i||t.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),t.isPresent!==o&&(o?s.promote():s.relegate()||ae.postRender(()=>{const a=s.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Cf.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function N0(e){const[t,n]=y6(),r=M.useContext(P1);return fo.jsx(x6,{...e,layoutGroup:r,switchLayoutGroup:M.useContext(b1),isPresent:t,safeToRemove:n})}const _6={borderRadius:{...ji,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ji,borderTopRightRadius:ji,borderBottomLeftRadius:ji,borderBottomRightRadius:ji,boxShadow:w6},F0=["TopLeft","TopRight","BottomLeft","BottomRight"],S6=F0.length,up=e=>typeof e=="string"?parseFloat(e):e,cp=e=>typeof e=="number"||W.test(e);function E6(e,t,n,r,i,o){i?(e.opacity=we(0,n.opacity!==void 0?n.opacity:1,A6(r)),e.opacityExit=we(t.opacity!==void 0?t.opacity:1,0,C6(r))):o&&(e.opacity=we(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let s=0;s<S6;s++){const a=`border${F0[s]}Radius`;let l=fp(t,a),u=fp(n,a);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||cp(l)===cp(u)?(e[a]=Math.max(we(up(l),up(u),r),0),(rn.test(u)||rn.test(l))&&(e[a]+="%")):e[a]=u}(t.rotate||n.rotate)&&(e.rotate=we(t.rotate||0,n.rotate||0,r))}function fp(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const A6=z0(0,.5,w0),C6=z0(.5,.95,He);function z0(e,t,n){return r=>r<e?0:r>t?1:n(Oo(e,t,r))}function hp(e,t){e.min=t.min,e.max=t.max}function _t(e,t){hp(e.x,t.x),hp(e.y,t.y)}function dp(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function pp(e,t,n,r,i){return e-=t,e=ba(e,1/n,r),i!==void 0&&(e=ba(e,1/i,r)),e}function b6(e,t=0,n=1,r=.5,i,o=e,s=e){if(rn.test(t)&&(t=parseFloat(t),t=we(s.min,s.max,t/100)-s.min),typeof t!="number")return;let a=we(o.min,o.max,r);e===o&&(a-=t),e.min=pp(e.min,t,n,a,i),e.max=pp(e.max,t,n,a,i)}function mp(e,t,[n,r,i],o,s){b6(e,t[n],t[r],t[i],t.scale,o,s)}const T6=["x","scaleX","originX"],k6=["y","scaleY","originY"];function gp(e,t,n,r){mp(e.x,t,T6,n?n.x:void 0,r?r.x:void 0),mp(e.y,t,k6,n?n.y:void 0,r?r.y:void 0)}function vp(e){return e.translate===0&&e.scale===1}function U0(e){return vp(e.x)&&vp(e.y)}function yp(e,t){return e.min===t.min&&e.max===t.max}function P6(e,t){return yp(e.x,t.x)&&yp(e.y,t.y)}function wp(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function j0(e,t){return wp(e.x,t.x)&&wp(e.y,t.y)}function xp(e){return ht(e.x)/ht(e.y)}function _p(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class D6{constructor(){this.members=[]}add(t){Xa(this.members,t),t.scheduleRender()}remove(t){if(Qa(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){r=o;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;i===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function M6(e,t,n){let r="";const i=e.x.translate/t.x,o=e.y.translate/t.y,s=(n==null?void 0:n.z)||0;if((i||o||s)&&(r=`translate3d(${i}px, ${o}px, ${s}px) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{transformPerspective:u,rotate:c,rotateX:h,rotateY:f,skewX:v,skewY:p}=n;u&&(r=`perspective(${u}px) ${r}`),c&&(r+=`rotate(${c}deg) `),h&&(r+=`rotateX(${h}deg) `),f&&(r+=`rotateY(${f}deg) `),v&&(r+=`skewX(${v}deg) `),p&&(r+=`skewY(${p}deg) `)}const a=e.x.scale*t.x,l=e.y.scale*t.y;return(a!==1||l!==1)&&(r+=`scale(${a}, ${l})`),r||"none"}const R6=(e,t)=>e.depth-t.depth;class O6{constructor(){this.children=[],this.isDirty=!1}add(t){Xa(this.children,t),this.isDirty=!0}remove(t){Qa(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(R6),this.isDirty=!1,this.children.forEach(t)}}function I6(e,t){const n=Kn.now(),r=({timestamp:i})=>{const o=i-n;o>=t&&(_n(r),e(o-t))};return ae.read(r,!0),()=>_n(r)}function L6(e){return e instanceof SVGElement&&e.tagName!=="svg"}function V6(e,t,n){const r=Ge(e)?e:Io(e);return r.start(Kf("",r,t,n)),r.animation}const dr={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},$i=typeof window<"u"&&window.MotionDebug!==void 0,Yl=["","X","Y","Z"],B6={visibility:"hidden"},Sp=1e3;let N6=0;function Zl(e,t,n,r){const{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function G0(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return!1;const{visualElement:t}=e.options;return t?T0(t)?!0:e.parent&&!e.parent.hasCheckedOptimisedAppear?G0(e.parent):!1:!1}function H0({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(s={},a=t==null?void 0:t()){this.id=N6++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,$i&&(dr.totalNodes=dr.resolvedTargetDeltas=dr.recalculatedProjection=0),this.nodes.forEach(U6),this.nodes.forEach(K6),this.nodes.forEach(q6),this.nodes.forEach(j6),$i&&window.MotionDebug.record(dr)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=s,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new O6)}addEventListener(s,a){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new qf),this.eventHandlers.get(s).add(a)}notifyListeners(s,...a){const l=this.eventHandlers.get(s);l&&l.notify(...a)}hasListeners(s){return this.eventHandlers.has(s)}mount(s,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=L6(s),this.instance=s;const{layoutId:l,layout:u,visualElement:c}=this.options;if(c&&!c.current&&c.mount(s),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(u||l)&&(this.isLayoutDirty=!0),e){let h;const f=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,h&&h(),h=I6(f,250),qs.hasAnimatedSinceResize&&(qs.hasAnimatedSinceResize=!1,this.nodes.forEach(Ap))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&c&&(l||u)&&this.addEventListener("didUpdate",({delta:h,hasLayoutChanged:f,hasRelativeTargetChanged:v,layout:p})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const g=this.options.transition||c.getDefaultTransition()||Q6,{onLayoutAnimationStart:S,onLayoutAnimationComplete:y}=c.getProps(),w=!this.targetLayout||!j0(this.targetLayout,p)||v,_=!f&&v;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||_||f&&(w||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(h,_);const E={...Bf(g,"layout"),onPlay:S,onComplete:y};(c.shouldReduceMotion||this.options.layoutRoot)&&(E.delay=0,E.type=!1),this.startAnimation(E)}else f||Ap(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=p})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const s=this.getStack();s&&s.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,_n(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach($6),this.animationId++)}getTransformTemplate(){const{visualElement:s}=this.options;return s&&s.getProps().transformTemplate}willUpdate(s=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.HandoffCancelAllAnimations&&G0(this)&&window.HandoffCancelAllAnimations(),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const h=this.path[c];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Ep);return}this.isUpdating||this.nodes.forEach(H6),this.isUpdating=!1,this.nodes.forEach(W6),this.nodes.forEach(F6),this.nodes.forEach(z6),this.clearAllSnapshots();const a=Kn.now();Ve.delta=Zn(0,1e3/60,a-Ve.timestamp),Ve.timestamp=a,Ve.isProcessing=!0,Fl.update.process(Ve),Fl.preRender.process(Ve),Fl.render.process(Ve),Ve.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Cf.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(G6),this.sharedNodes.forEach(Y6)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,ae.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){ae.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const s=this.layout;this.layout=this.measure(!1),this.layoutCorrected=Ee(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,s?s.layoutBox:void 0)}updateScroll(s="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(a=!1),a){const l=r(this.instance);this.scroll={animationId:this.root.animationId,phase:s,isRoot:l,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!i)return;const s=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!U0(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;s&&(a||hr(this.latestValues)||c)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return s&&(l=this.removeTransform(l)),J6(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var s;const{visualElement:a}=this.options;if(!a)return Ee();const l=a.measureViewportBox();if(!(((s=this.scroll)===null||s===void 0?void 0:s.wasRoot)||this.path.some(eS))){const{scroll:c}=this.root;c&&(Jr(l.x,c.offset.x),Jr(l.y,c.offset.y))}return l}removeElementScroll(s){var a;const l=Ee();if(_t(l,s),!((a=this.scroll)===null||a===void 0)&&a.wasRoot)return l;for(let u=0;u<this.path.length;u++){const c=this.path[u],{scroll:h,options:f}=c;c!==this.root&&h&&f.layoutScroll&&(h.wasRoot&&_t(l,s),Jr(l.x,h.offset.x),Jr(l.y,h.offset.y))}return l}applyTransform(s,a=!1){const l=Ee();_t(l,s);for(let u=0;u<this.path.length;u++){const c=this.path[u];!a&&c.options.layoutScroll&&c.scroll&&c!==c.root&&ei(l,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),hr(c.latestValues)&&ei(l,c.latestValues)}return hr(this.latestValues)&&ei(l,this.latestValues),l}removeTransform(s){const a=Ee();_t(a,s);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!hr(u.latestValues))continue;hc(u.latestValues)&&u.updateSnapshot();const c=Ee(),h=u.measurePageBox();_t(c,h),gp(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return hr(this.latestValues)&&gp(a,this.latestValues),a}setTargetDelta(s){this.targetDelta=s,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Ve.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(s=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==l;if(!(s||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:h,layoutId:f}=this.options;if(!(!this.layout||!(h||f))){if(this.resolvedRelativeTargetAt=Ve.timestamp,!this.targetDelta&&!this.relativeTarget){const v=this.getClosestProjectingParent();v&&v.layout&&this.animationProgress!==1?(this.relativeParent=v,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Ee(),this.relativeTargetOrigin=Ee(),lo(this.relativeTargetOrigin,this.layout.layoutBox,v.layout.layoutBox),_t(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=Ee(),this.targetWithTransforms=Ee()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),n6(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):_t(this.target,this.layout.layoutBox),L0(this.target,this.targetDelta)):_t(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const v=this.getClosestProjectingParent();v&&!!v.resumingFrom==!!this.resumingFrom&&!v.options.layoutScroll&&v.target&&this.animationProgress!==1?(this.relativeParent=v,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Ee(),this.relativeTargetOrigin=Ee(),lo(this.relativeTargetOrigin,this.target,v.target),_t(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}$i&&dr.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||hc(this.parent.latestValues)||I0(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var s;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let u=!0;if((this.isProjectionDirty||!((s=this.parent)===null||s===void 0)&&s.isProjectionDirty)&&(u=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===Ve.timestamp&&(u=!1),u)return;const{layout:c,layoutId:h}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||h))return;_t(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,v=this.treeScale.y;f6(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=Ee());const{target:p}=a;if(!p){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(dp(this.prevProjectionDelta.x,this.projectionDelta.x),dp(this.prevProjectionDelta.y,this.projectionDelta.y)),ao(this.projectionDelta,this.layoutCorrected,p,this.latestValues),(this.treeScale.x!==f||this.treeScale.y!==v||!_p(this.projectionDelta.x,this.prevProjectionDelta.x)||!_p(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",p)),$i&&dr.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){var a;if((a=this.options.visualElement)===null||a===void 0||a.scheduleRender(),s){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=Qr(),this.projectionDelta=Qr(),this.projectionDeltaWithTransform=Qr()}setAnimationOrigin(s,a=!1){const l=this.snapshot,u=l?l.latestValues:{},c={...this.latestValues},h=Qr();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=Ee(),v=l?l.source:void 0,p=this.layout?this.layout.source:void 0,g=v!==p,S=this.getStack(),y=!S||S.members.length<=1,w=!!(g&&!y&&this.options.crossfade===!0&&!this.path.some(X6));this.animationProgress=0;let _;this.mixTargetDelta=E=>{const b=E/1e3;Cp(h.x,s.x,b),Cp(h.y,s.y,b),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(lo(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Z6(this.relativeTarget,this.relativeTargetOrigin,f,b),_&&P6(this.relativeTarget,_)&&(this.isProjectionDirty=!1),_||(_=Ee()),_t(_,this.relativeTarget)),g&&(this.animationValues=c,E6(c,u,this.latestValues,b,w,y)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=b},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(s){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(_n(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=ae.update(()=>{qs.hasAnimatedSinceResize=!0,this.currentAnimation=V6(0,Sp,{...s,onUpdate:a=>{this.mixTargetDelta(a),s.onUpdate&&s.onUpdate(a)},onComplete:()=>{s.onComplete&&s.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const s=this.getStack();s&&s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Sp),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:a,target:l,layout:u,latestValues:c}=s;if(!(!a||!l||!u)){if(this!==s&&this.layout&&u&&W0(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||Ee();const h=ht(this.layout.layoutBox.x);l.x.min=s.target.x.min,l.x.max=l.x.min+h;const f=ht(this.layout.layoutBox.y);l.y.min=s.target.y.min,l.y.max=l.y.min+f}_t(a,l),ei(a,c),ao(this.projectionDeltaWithTransform,this.layoutCorrected,a,c)}}registerSharedNode(s,a){this.sharedNodes.has(s)||this.sharedNodes.set(s,new D6),this.sharedNodes.get(s).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:a}=this.options;return a?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:a}=this.options;return a?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:a,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),s&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetSkewAndRotation(){const{visualElement:s}=this.options;if(!s)return;let a=!1;const{latestValues:l}=s;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const u={};l.z&&Zl("z",s,u,this.animationValues);for(let c=0;c<Yl.length;c++)Zl(`rotate${Yl[c]}`,s,u,this.animationValues),Zl(`skew${Yl[c]}`,s,u,this.animationValues);s.render();for(const c in u)s.setStaticValue(c,u[c]),this.animationValues&&(this.animationValues[c]=u[c]);s.scheduleRender()}getProjectionStyles(s){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return B6;const u={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=Ws(s==null?void 0:s.pointerEvents)||"",u.transform=c?c(this.latestValues,""):"none",u;const h=this.getLead();if(!this.projectionDelta||!this.layout||!h.target){const g={};return this.options.layoutId&&(g.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,g.pointerEvents=Ws(s==null?void 0:s.pointerEvents)||""),this.hasProjected&&!hr(this.latestValues)&&(g.transform=c?c({},""):"none",this.hasProjected=!1),g}const f=h.animationValues||h.latestValues;this.applyTransformsToTarget(),u.transform=M6(this.projectionDeltaWithTransform,this.treeScale,f),c&&(u.transform=c(f,u.transform));const{x:v,y:p}=this.projectionDelta;u.transformOrigin=`${v.origin*100}% ${p.origin*100}% 0`,h.animationValues?u.opacity=h===this?(l=(a=f.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:u.opacity=h===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const g in _a){if(f[g]===void 0)continue;const{correct:S,applyTo:y}=_a[g],w=u.transform==="none"?f[g]:S(f[g],h);if(y){const _=y.length;for(let E=0;E<_;E++)u[y[E]]=w}else u[g]=w}return this.options.layoutId&&(u.pointerEvents=h===this?Ws(s==null?void 0:s.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var a;return(a=s.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(Ep),this.root.sharedNodes.clear()}}}function F6(e){e.updateLayout()}function z6(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:i}=e.layout,{animationType:o}=e.options,s=n.source!==e.layout.source;o==="size"?St(h=>{const f=s?n.measuredBox[h]:n.layoutBox[h],v=ht(f);f.min=r[h].min,f.max=f.min+v}):W0(o,n.layoutBox,r)&&St(h=>{const f=s?n.measuredBox[h]:n.layoutBox[h],v=ht(r[h]);f.max=f.min+v,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[h].max=e.relativeTarget[h].min+v)});const a=Qr();ao(a,r,n.layoutBox);const l=Qr();s?ao(l,e.applyTransform(i,!0),n.measuredBox):ao(l,r,n.layoutBox);const u=!U0(a);let c=!1;if(!e.resumeFrom){const h=e.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:f,layout:v}=h;if(f&&v){const p=Ee();lo(p,n.layoutBox,f.layoutBox);const g=Ee();lo(g,r,v.layoutBox),j0(p,g)||(c=!0),h.options.layoutRoot&&(e.relativeTarget=g,e.relativeTargetOrigin=p,e.relativeParent=h)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:u,hasRelativeTargetChanged:c})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function U6(e){$i&&dr.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function j6(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function G6(e){e.clearSnapshot()}function Ep(e){e.clearMeasurements()}function H6(e){e.isLayoutDirty=!1}function W6(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Ap(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function K6(e){e.resolveTargetDelta()}function q6(e){e.calcProjection()}function $6(e){e.resetSkewAndRotation()}function Y6(e){e.removeLeadSnapshot()}function Cp(e,t,n){e.translate=we(t.translate,0,n),e.scale=we(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function bp(e,t,n,r){e.min=we(t.min,n.min,r),e.max=we(t.max,n.max,r)}function Z6(e,t,n,r){bp(e.x,t.x,n.x,r),bp(e.y,t.y,n.y,r)}function X6(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const Q6={duration:.45,ease:[.4,0,.1,1]},Tp=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),kp=Tp("applewebkit/")&&!Tp("chrome/")?Math.round:He;function Pp(e){e.min=kp(e.min),e.max=kp(e.max)}function J6(e){Pp(e.x),Pp(e.y)}function W0(e,t,n){return e==="position"||e==="preserve-aspect"&&!t6(xp(t),xp(n),.2)}function eS(e){var t;return e!==e.root&&((t=e.scroll)===null||t===void 0?void 0:t.wasRoot)}const tS=H0({attachResizeListener:(e,t)=>hn(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Xl={current:void 0},K0=H0({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Xl.current){const e=new tS({});e.mount(window),e.setOptions({layoutScroll:!0}),Xl.current=e}return Xl.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),nS={pan:{Feature:v6},drag:{Feature:g6,ProjectionNode:K0,MeasureLayout:N0}},pc={current:null},q0={current:!1};function rS(){if(q0.current=!0,!!Af)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>pc.current=e.matches;e.addListener(t),t()}else pc.current=!1}function iS(e,t,n){for(const r in t){const i=t[r],o=n[r];if(Ge(i))e.addValue(r,i);else if(Ge(o))e.addValue(r,Io(i,{owner:e}));else if(o!==i)if(e.hasValue(r)){const s=e.getValue(r);s.liveStyle===!0?s.jump(i):s.hasAnimated||s.set(i)}else{const s=e.getStaticValue(r);e.addValue(r,Io(s!==void 0?s:i,{owner:e}))}}for(const r in n)t[r]===void 0&&e.removeValue(r);return t}const Dp=new WeakMap,oS=[...e0,Ue,Xn],sS=e=>oS.find(J1(e)),Mp=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],aS=Tf.length;class lS{scrapeMotionValuesFromProps(t,n,r){return{}}constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:i,blockInitialAnimation:o,visualState:s},a={}){this.applyWillChange=!1,this.resolveKeyframes=(f,v,p,g)=>new this.KeyframeResolver(f,v,p,g,this),this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Nf,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.isRenderScheduled=!1,this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.isRenderScheduled=!1,this.scheduleRender=()=>{this.isRenderScheduled||(this.isRenderScheduled=!0,ae.render(this.render,!1,!0))};const{latestValues:l,renderState:u}=s;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=u,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=a,this.blockInitialAnimation=!!o,this.isControllingVariants=Za(n),this.isVariantNode=k1(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:c,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const f in h){const v=h[f];l[f]!==void 0&&Ge(v)&&v.set(l[f],!1)}}mount(t){this.current=t,Dp.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),q0.current||rS(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:pc.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Dp.delete(this.current),this.projection&&this.projection.unmount(),_n(this.notifyUpdate),_n(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(t,n){const r=tr.has(t),i=n.on("change",s=>{this.latestValues[t]=s,this.props.onUpdate&&ae.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),o(),n.owner&&n.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in yi){const n=yi[t];if(!n)continue;const{isEnabled:r,Feature:i}=n;if(!this.features[t]&&i&&r(this.props)&&(this.features[t]=new i(this)),this.features[t]){const o=this.features[t];o.isMounted?o.update():(o.mount(),o.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):Ee()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<Mp.length;r++){const i=Mp[r];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const o="on"+i,s=t[o];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=iS(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}const n={};for(let r=0;r<aS;r++){const i=Tf[r],o=this.props[i];(Do(o)||o===!1)&&(n[i]=o)}return n}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){const r=this.values.get(t);n!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,n),this.values.set(t,n),this.latestValues[t]=n.get())}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=Io(n===null?void 0:n,{owner:this}),this.addValue(t,r)),r}readValue(t,n){var r;let i=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(r=this.getBaseTargetFromProps(this.props,t))!==null&&r!==void 0?r:this.readValueFromInstance(this.current,t,this.options);return i!=null&&(typeof i=="string"&&(X1(i)||Z1(i))?i=parseFloat(i):!sS(i)&&Xn.test(n)&&(i=l0(t,n)),this.setBaseTarget(t,Ge(i)?i.get():i)),Ge(i)?i.get():i}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props;let i;if(typeof r=="string"||typeof r=="object"){const s=Vf(this.props,r,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);s&&(i=s[t])}if(r&&i!==void 0)return i;const o=this.getBaseTargetFromProps(this.props,t);return o!==void 0&&!Ge(o)?o:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new qf),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class $0 extends lS{constructor(){super(...arguments),this.KeyframeResolver=u0}sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}}function uS(e){return window.getComputedStyle(e)}class cS extends $0{constructor(){super(...arguments),this.type="html",this.applyWillChange=!0,this.renderInstance=B1}readValueFromInstance(t,n){if(tr.has(n)){const r=zf(n);return r&&r.default||0}else{const r=uS(t),i=(R1(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return V0(t,n)}build(t,n,r){Mf(t,n,r.transformTemplate)}scrapeMotionValuesFromProps(t,n,r){return Lf(t,n,r)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Ge(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}class fS extends $0{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Ee}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(tr.has(n)){const r=zf(n);return r&&r.default||0}return n=N1.has(n)?n:Ya(n),t.getAttribute(n)}scrapeMotionValuesFromProps(t,n,r){return z1(t,n,r)}build(t,n,r){Of(t,n,this.isSVGTag,r.transformTemplate)}renderInstance(t,n,r,i){F1(t,n,r,i)}mount(t){this.isSVGTag=If(t.tagName),super.mount(t)}}const hS=(e,t)=>kf(e)?new fS(t):new cS(t,{allowProjection:e!==M.Fragment}),dS={layout:{ProjectionNode:K0,MeasureLayout:N0}},pS={...q_,...rx,...nS,...dS},Y0=cw((e,t)=>Hw(e,t,pS,hS)),mS="/assets/k-circle-Cd-jQUUg.png",gS="data:image/png;base64,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",vS="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAA5BSURBVHgB7VpLbF3Hef7/mfO495KU+Ahti7ZVRlHkWAxcJ0ztLtyYKQw0aOtFUVAo0CaboA0KFF4FXTSASSHtsgsvkgDJpkHajYgWRbpoGxg11cA1HFRF64a0rbqK/BCpiBZfl/dxHjN//nmdey4pSnayTAY6ukfnzPzzv//vnyOAX45fjl/sgfAhBxFV6xCRBs/t31gnTHe4D+t48GMk/NAcDI+fcTmhYfjiRcC184Bz60Bra4Bbc6sIqwsA/Ke9ccXRvgIwPz8PV2eAzm0A7jwDGlZWABYXYW79Ii0tLTmNDEQn+BDjAwlgtBUmLq6A2Fp3jM7+wf5H00byWdbo40zqNM+ZBQEnCcQJID0BxkJk9jA8iR0AtQ8k3maCuxroHa31ZUnR9Tffjf97es1MWoG5ufWaUPcWBu/FuPm1mmYNX5u4Ij795NzTKMQXUeCzPGM8TK1okXcXNGYCK8HAidC+Rqfm6j0CXicNPyi1+t6PbzT/cfo80ApbFZbMy7sLgcczbxhfFqvwtPjo5GcmotHoORTyz/jFeN2jh6hXvJL1CcMf+1rgYEjIanc6QuttXeq/fA1a3znzIuiVS6Cr6XcQ5o4CmO0vLhP+08YV+einP7HQSKJv8+PTWHfQIxs7pWLgkegIUfI3aIWDQ9IfGW/nmfrjVjyyujPBgixy7NxBADG0h3EZvi4sXhKG+ceemHs+kfL7WsNp0oSKHVcrAs03/Mxd7ANsfjDvyT4jUPY9WSGMHDowH/7NNMhc/ILjwM0197xA22f2+hUZi3/NqP/8+jpIuLAilpeXRT3THbGAEeACT+RQla1n+s8LhL84sgCddtH5c0WADuVMqr0M1kCsJVTtM4+xhhEMB+5mPXAwk4XTf/X+Gze/1pt6Ty3AZb20tKxDVokq5pmECVaYY+bv7z/P6vyqJrepZdjci7rnWFcwJkPwmxkjGAtWc8jHKj8QTvKBxBiY1mjesSW8f7h5ChwtP/mrk+cegO2r8DWYehpcGQG/v9e8zzRR+lTnN0WC/zxQq/N89BoUfhvhVRD40t49zJ7GO4xgQSAzR6AjJTwdHMSCr4E837qRs6xxKfJ2sMmAlxVKfZ52T7wEaytq5dKijQnpmDDZBoQsN+PGA+m/MMWTTqOeca84s7k0TDA3kf0ly5gYGAUqZiiYwAth6LD0PB8NDSkdLcm8CZdZrYQudqyHOc6tVgAtL4i/8f/XfvTNneYEffm/vk2rq6skjPZNNW3efkuOnh/7AsfpaTNZWw6crZzPEwRNJsx4LAnSSFAjRr4HiAVY64jgT3yZoGdVhkjgSDTMk5tvrggpYSliphMzTfNOoKsWRnKTLFzQm+xhk8bp2XNnvzg2MyXX1pasL0TIrvMnp7i63szj8bPwRxT8GCwxW0WN5syP2SBiKcYaCL/1qwmcm5GWuTc3FVxeK2BrT1mH0N561h/QJ19jbta2EdSs//zjg/VXNxT+21oO7+9rm9UUB4DJTmYoFapIQBz4h3Br5G/XYd1Ip3FxkeTBA29FyfTUucZY9FrwBldJnf9GwmjJXqxxwud+uwXjraEMDDcZHHzncp/2uwqzAiAvORCZgnEPEy+Rsxg0EoDnfueY9S/1YKdLVBSEOTNeqhBXw7Wvu0uz3Ru7P1mYnc3F1tYq3spGZNSQn6WQj60aXBCS80VrVqPBT83GRzY344FxAb9+NkIRAtYGkc0s5OGDjZnHZ6Pj1z8SgxTeXT3ntu5YvsDXB3a9Fv3u9keacoNrlWifW8BGaTyaHrNFyE9WttCw/7pUYomxNuGJszEcNx55MPIx4IMbwbkRubRtMsYTH0+OXz/D62vAkWreY1zKCWEKCDzV3GzLl3fmMbovfUv0WpEEffKTWghwezqVs0AOGohBVbp/XBzLgNGiFZWZMApQ2lmiKhTk5txtvWM4VGN2BF/hHQkNHhB+sjkdiebudcEYrSH4seD5p0FVFQhs9AknhIMNbFRN94Tf5ObboDdM2IwfzE9w70G+BmifPhVVKdVPMFROdItYyg7H13vwEGSCPY/gocr/jQvZbMBcW2xTaYSDTR27980d5eYpV5TIplLtihyvNUF5r/VlqaE0+zs6DoZYK1AQzGSo06ohMJ+QKPKTm6jyDuqKefIbams65RlnwpxdiF5/pziWgf94PYO8MNlDW2aVzyTmtzCMcWZav8v6l3l9oRwY5FRq9w7710CeVUza6YozM5EQxQFLwhbwyNIy7xDnADmajQtz8f3ltZw2d45qcfdAww+v5kYAKs3m9tJ2vbk3z4xw//6/GWxuH12/w+tffYMVkJt9jPVDNnSChHTqLAyUMM97B5rko4//eVr286Q5lnwlwF+byHwFDljfQW1A1i7+340SWqmAyVFhGfufH+fwNy924KBPxExi4ZlWXilmBNrm3dUbBYxwMZsYlW79Nbe+0ycrJCsLjRA2/mqZiAbtHe739v5aFqMFPvaVmyPNAkcnxuV/cpQ+hId6HPRAThr8w3kw4uLJ9cgWNjuEC+7SWoq1zBuXztRovFA4wEiC1xkokXjoIcUAXhvmrIWc5i2t4DoO2KHHcxVX71Ln4NfUxGgnUt3bKPWUSVf7GKoWDKQOAgSkaUxYotGkneGwqkGg2vm7STzV5iadGhk5O9vYQre+EPaZgSgWZYQMxRZza6lqljwfph55Rhw8v5FlmkbZhaIHW1N0cHDAoo+9zjMedcilJoRGC9vtJuxESjoEjQWzg7YbqHqAwIj2MJx87ndr2RrCpWTlEa3BnKFYBW0Hf9dUNf+V74A/S+KxHviL0n1Nt3lZUsIrQujfd26DtZ6WBp0o2rbSIir0jUDozQL0tkwoqEzu9rbVFV0RdX2AQ9nmBZO3hSM0P14ghygGFRQonNGYOesyJrrNvEftmUyPb2kGUPn3MY5dQ2SbiRp88lQVOjrGrz1tsratt5FBVK99qAoxeXcg6/9VS+UOnUwgoX9kLeb0g+5oqVYADR3Oiq9A1FBJpChqTs5Sb/e6gizbJRm9ynOfhOEx0ILDCah9UPjGKvQxoburNbRYHVMES5j7slbxB+vCe4IgCNsBPR70R5lWgT/UXXonGy31KVXq6NaPQEdpyS4Ul/zvf+Cs8mSlN99VVZIMde7gtePzrtti0MA7bWHYORyS+SOjo+pxPlRtSwEHI1WnBxYTafX3pVAqZWZvZWdZlwsvRfPnHklaKY5kPRyNR9UPWPoHq8OpIfsdTmdBsMPc1B8ONHgcFKqEIrjDSUdNYs4+UEZPsad39nZvdSc/tp3ZhmZr5HpcNtvNKJocyYVYYFG/60XHYQXZICOXfmqZClymgiFfrXOBlXD1DOftOHjm3a4ucH0OW+ALXKhfGkPdHYvy7ujNs6W4tAJ6erard96XxfZuPy874mW29tcNFrKprcJDDthZbj1eCr2zCUCtaBiv1HAL/1LAM9YzPN0K4yiAwXpd4TJdhxQavs7l+WWRRXmEzeJWtqf5kFtbAA9ra2p6pFVOTk3lcSPPdK5fYJ6+UZ2uHdrQFyqqnikPGg+DLotkPTJ276kuqDosNA26LlcIdTin/EZTwwtKjPRFnObXu53yzM417ezlA/VznwO5/fBraQOTRmP0RKtT5iNCyuf45Z/Cceedx/p03W1qvl3zc+cY6ArW4bgK8Wu7EvxmQ8oXJPa6RZp28o7sA2wUV741Xxr27fmUScfT0yuk+lHRGp3KOk3siSjpJCBfYF6+xFrbUPZoY2BaHbSrB/fKgTiq969aD1mg6rQGWreuSr6PqCzMYm3wwi8Z5lExP2Wrl2ZpfmbnVPnszLwKWqinSDRHLHNcncffeTO93RxNTopmk3SvJQtKOXh+j7f7Mm84M6zfQdAdySIw2KRaM2TFsMzXc3dzwBjp75KEviuU3NIy648w81tR1M/kXvEpDtxLlwwyQRoSwG+J/AlGzPH56DRsJd2tPI0xTVF1GyVSSiTjvFTPsJYWeMN5VtQpDxMGp5AAcNjTqDp6r0pLEJq8J+0zuLvMN99rSvkqRaqMCDNFItsroHffw2k+DdN5/UhxWAXD/ot4wRwurMv5iYlorJUlPaIUdBLLTKe5lBE7S9yIhezm+hOq0A+y4j7DbnyKk80YkzjF1EeZ0Ikh/SO2+dkB37VZexsszSaz8WYj1ldApO+aPMZHZ0WiFLccIpPjUd6S92c7O5vFmZmdEs6fVysXwLf3dxEAvFr4Awd/IwCZjLwSTccfj7bb3ShNVELiRNTttWOhS1nGTW6mhVSIQhel4H7B0tNo7pFxfUTmEMY0kea3lKz20nZsJGKtJcV89TlxxkyCHVW1y352IidqFCPiJyo+nRbtjVn17LcuqiUaHKnDESe8kxD+VG7xEohrL4JIRt6L0mxftssGC8Unmr0sko1I5iqS7GfYzvgDlC4kMStRgxF/xn1r6s6AihIoM5sx802ZEP/oQpMWEVHSb6tSJIrSpFRFVvajvpq+r1VOr3X11tYtvbq6oAOa/sCfmOpChPsL7FbmI9996UlhTvKmegL3xtuyKT8ibmf7mLRiqbc72EzHUKVOV82ka397eYs57mLcanJMMtN8Wtvl9jMdG9U9PrY4UDf1XnpCNd7o65MP72mT4y+tLGoguOeXyuhuLw8tVvyJh9bOL+ne+qrK+TPrwcfuF5O3Adu9XTzVUJjHe3jQYQE4OY+PbnM+maSCf5vFCG1BF6a7XUi4gWor0pudkh5V47Q1BTQNk/o8f2adexJoadkkpXm39z1PoeCDTBkewbUCal7m1LsM5tv2qj1Wa2+MWZr9naajzXkZ1gAaEz0am5m3CjGfUcPH8bk5oOWlGvT7AN+Gfy4Bjhvhvw4MpcuhAuA3dPiaqm7l5xw/BUlpmR/4WibIAAAAAElFTkSuQmCC",yS="/assets/kimi-failed-DzTamUVb.png",wS="/assets/kimi-reading-CqmPaZDN.png",xS="/assets/kimi-searching-x9Yw6sOV.png",_S="/assets/kimi-thinking-Dsv7Np7c.png",SS="/assets/CHN-Cklw3avt.png",ES="/assets/ENG-BRkzBUtx.png",AS=e=>M.createElement("svg",{width:16,height:16,viewBox:"0 0 16 16",fill:"none",stroke:"currentColor",strokeWidth:1.5,strokeLinecap:"round",strokeLinejoin:"round",...e},M.createElement("path",{d:"M13.25 4.75L6 12L2.75 8.75"})),CS=e=>M.createElement("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},M.createElement("circle",{cx:12,cy:12,r:10.5,fill:"black",fillOpacity:.6}),M.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.29289 8.29289C8.68342 7.90237 9.31658 7.90237 9.70711 8.29289L12 10.5858L14.2929 8.29289C14.6834 7.90237 15.3166 7.90237 15.7071 8.29289C16.0976 8.68342 16.0976 9.31658 15.7071 9.70711L13.4142 12L15.7071 14.2929C16.0976 14.6834 16.0976 15.3166 15.7071 15.7071C15.3166 16.0976 14.6834 16.0976 14.2929 15.7071L12 13.4142L9.70711 15.7071C9.31658 16.0976 8.68342 16.0976 8.29289 15.7071C7.90237 15.3166 7.90237 14.6834 8.29289 14.2929L10.5858 12L8.29289 9.70711C7.90237 9.31658 7.90237 8.68342 8.29289 8.29289Z",fill:"white"})),bS=e=>M.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,viewBox:"0 0 16 16",fill:"none",...e},M.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.2808 2.43249C5.52099 2.1923 5.87091 2 6.2665 2H10.0301C10.1748 2 10.3135 2.05747 10.4158 2.15976L12.8704 4.61431C12.9727 4.7166 13.0301 4.85534 13.0301 5V10.4C13.0301 10.7956 12.8378 11.1455 12.5977 11.3857C12.3575 11.6259 12.0075 11.8182 11.612 11.8182H6.2665C5.87091 11.8182 5.52099 11.6259 5.2808 11.3857C5.04061 11.1455 4.84831 10.7956 4.84831 10.4V3.41818C4.84831 3.0226 5.04061 2.67268 5.2808 2.43249ZM9.21193 3.09091H6.2665C6.22571 3.09091 6.13927 3.11679 6.05219 3.20388C5.96511 3.29096 5.93922 3.3774 5.93922 3.41818V10.4C5.93922 10.4408 5.96511 10.5272 6.05219 10.6143C6.13927 10.7014 6.22571 10.7273 6.2665 10.7273H11.612C11.6527 10.7273 11.7392 10.7014 11.8263 10.6143C11.9133 10.5272 11.9392 10.4408 11.9392 10.4V5.81819H9.75738C9.45613 5.81819 9.21193 5.57398 9.21193 5.27273V3.09091ZM11.4406 4.72728H10.3028V3.58955L11.4406 4.72728Z",fill:"currentColor"}),M.createElement("path",{d:"M3.21196 5.05458C3.51321 5.05458 3.75741 5.29879 3.75741 5.60004V12.5819C3.75741 12.6226 3.7833 12.7091 3.87038 12.7962C3.95747 12.8833 4.0439 12.9091 4.08469 12.9091H9.43015C9.73139 12.9091 9.9756 13.1533 9.9756 13.4546C9.9756 13.7558 9.73139 14 9.43015 14H4.08469C3.68911 14 3.33918 13.8077 3.09899 13.5676C2.8588 13.3274 2.6665 12.9774 2.6665 12.5819V5.60004C2.6665 5.29879 2.91071 5.05458 3.21196 5.05458Z",fill:"currentColor"})),TS=e=>M.createElement("svg",{width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},M.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.498 3.26324C12.3652 3.1492 12.1951 3.08791 12.02 3.091L12.0104 3.09109H11.0995V8.0001H12.0104L12.02 8.00018C12.1951 8.00328 12.3652 7.94199 12.498 7.82795C12.6229 7.72077 12.7067 7.57403 12.7358 7.41273V3.67846C12.7067 3.51716 12.6229 3.37041 12.498 3.26324ZM10.0086 8.42979L8.04071 12.8575C7.87632 12.8051 7.72515 12.7139 7.60087 12.5896C7.39629 12.385 7.28136 12.1075 7.28136 11.8182V9.63644C7.28136 9.33519 7.03715 9.09099 6.73591 9.09099H3.64869L3.64251 9.09102C3.56345 9.09192 3.48513 9.07561 3.413 9.04324C3.34086 9.01086 3.27662 8.96319 3.22474 8.90352C3.17286 8.84386 3.13457 8.77362 3.11252 8.69769C3.0905 8.62186 3.08522 8.54215 3.09702 8.46407C3.097 8.46418 3.09704 8.46397 3.09702 8.46407L3.84979 3.55468C3.86952 3.42462 3.93558 3.30607 4.03582 3.22087C4.13605 3.13567 4.2637 3.08957 4.39524 3.09105L10.0086 3.09109V8.42979ZM12.006 2.0002C12.4465 1.99369 12.8742 2.14846 13.2086 2.43547C13.5443 2.72363 13.7625 3.12501 13.8218 3.56342C13.8251 3.58765 13.8267 3.61208 13.8267 3.63653V7.45465C13.8267 7.47911 13.8251 7.50353 13.8218 7.52777C13.7625 7.96618 13.5443 8.36756 13.2086 8.65571C12.8742 8.94272 12.4465 9.0975 12.006 9.09099H10.9085L8.87068 13.6761C8.78314 13.8731 8.5878 14 8.37225 14C7.7936 14 7.23866 13.7701 6.82949 13.361C6.42033 12.9518 6.19047 12.3969 6.19047 11.8182V10.1819H3.65148C3.41544 10.1841 3.1817 10.1352 2.96632 10.0385C2.74991 9.94136 2.5572 9.79834 2.40155 9.61934C2.2459 9.44034 2.13102 9.22964 2.06489 9.00184C1.99875 8.77404 1.98293 8.53458 2.01853 8.30006L2.77123 3.39112C2.77121 3.39128 2.77126 3.39095 2.77123 3.39112C2.8305 3.00112 3.02873 2.64516 3.3293 2.38968C3.62914 2.13481 4.01075 1.99656 4.40418 2.0002H12.006Z",fill:"currentColor"})),kS=e=>M.createElement("svg",{width:32,height:32,viewBox:"0 0 32 32",fill:"none",...e},M.createElement("path",{d:"M24.7496 9C24.418 9 24.0999 9.13402 23.8654 9.37258C23.631 9.61113 23.4992 9.93469 23.4992 10.2721V15.3603C23.4992 15.6977 23.3675 16.0212 23.133 16.2598C22.8985 16.4983 22.5805 16.6323 22.2488 16.6323H10.2576L11.8831 14.9914C12.1186 14.7519 12.2508 14.427 12.2508 14.0882C12.2508 13.7495 12.1186 13.4246 11.8831 13.1851C11.6476 12.9455 11.3283 12.811 10.9953 12.811C10.6623 12.811 10.343 12.9455 10.1075 13.1851L6.35638 17.0012C6.24254 17.1222 6.15331 17.2649 6.0938 17.421C5.96873 17.7307 5.96873 18.0781 6.0938 18.3878C6.15331 18.5439 6.24254 18.6866 6.35638 18.8076L10.1075 22.6237C10.2238 22.743 10.3621 22.8376 10.5145 22.9022C10.6668 22.9668 10.8303 23 10.9953 23C11.1604 23 11.3238 22.9668 11.4762 22.9022C11.6286 22.8376 11.7669 22.743 11.8831 22.6237C12.0003 22.5055 12.0933 22.3648 12.1568 22.2098C12.2203 22.0548 12.253 21.8885 12.253 21.7206C12.253 21.5526 12.2203 21.3864 12.1568 21.2314C12.0933 21.0764 12.0003 20.9357 11.8831 20.8174L10.2576 19.1765H22.2488C23.2437 19.1765 24.1978 18.7744 24.9013 18.0587C25.6048 17.3431 26 16.3724 26 15.3603V10.2721C26 9.93469 25.8683 9.61113 25.6338 9.37258C25.3993 9.13402 25.0812 9 24.7496 9Z",fill:"currentColor"})),PS=e=>M.createElement("svg",{width:20,height:21,viewBox:"0 0 20 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},M.createElement("mask",{id:"mask0_1789_634",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:0,y:0,width:20,height:21},M.createElement("rect",{y:.5,width:20,height:20,fill:"#D9D9D9"})),M.createElement("g",{mask:"url(#mask0_1789_634)"},M.createElement("path",{d:"M4.99995 16.3337H7.49995V12.167C7.49995 11.9309 7.57981 11.733 7.73953 11.5732C7.89925 11.4135 8.09717 11.3337 8.33328 11.3337H11.6666C11.9027 11.3337 12.1006 11.4135 12.2604 11.5732C12.4201 11.733 12.4999 11.9309 12.4999 12.167V16.3337H14.9999V8.83366L9.99995 5.08366L4.99995 8.83366V16.3337ZM3.33328 16.3337V8.83366C3.33328 8.56977 3.39231 8.31977 3.51037 8.08366C3.62842 7.84755 3.79162 7.6531 3.99995 7.50033L8.99995 3.75033C9.29162 3.5281 9.62495 3.41699 9.99995 3.41699C10.3749 3.41699 10.7083 3.5281 10.9999 3.75033L15.9999 7.50033C16.2083 7.6531 16.3715 7.84755 16.4895 8.08366C16.6076 8.31977 16.6666 8.56977 16.6666 8.83366V16.3337C16.6666 16.792 16.5034 17.1844 16.177 17.5107C15.8506 17.8371 15.4583 18.0003 14.9999 18.0003H11.6666C11.4305 18.0003 11.2326 17.9205 11.0729 17.7607C10.9131 17.601 10.8333 17.4031 10.8333 17.167V13.0003H9.16662V17.167C9.16662 17.4031 9.08676 17.601 8.92703 17.7607C8.76731 17.9205 8.56939 18.0003 8.33328 18.0003H4.99995C4.54162 18.0003 4.14925 17.8371 3.82287 17.5107C3.49648 17.1844 3.33328 16.792 3.33328 16.3337Z",fill:"black",fillOpacity:.85}))),DS=e=>M.createElement("svg",{width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},M.createElement("mask",{id:"mask0_1576_502",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:0,y:0,width:16,height:16},M.createElement("rect",{width:16,height:16,fill:"#D9D9D9"})),M.createElement("g",{mask:"url(#mask0_1576_502)"},M.createElement("path",{d:"M4.00033 12.6666H6.00033V9.33325C6.00033 9.14436 6.06421 8.98603 6.19199 8.85825C6.31977 8.73047 6.4781 8.66659 6.66699 8.66659H9.33366C9.52255 8.66659 9.68088 8.73047 9.80866 8.85825C9.93644 8.98603 10.0003 9.14436 10.0003 9.33325V12.6666H12.0003V6.66659L8.00033 3.66659L4.00033 6.66659V12.6666ZM2.66699 12.6666V6.66659C2.66699 6.45547 2.71421 6.25547 2.80866 6.06659C2.9031 5.8777 3.03366 5.72214 3.20033 5.59992L7.20033 2.59992C7.43366 2.42214 7.70033 2.33325 8.00033 2.33325C8.30033 2.33325 8.56699 2.42214 8.80033 2.59992L12.8003 5.59992C12.967 5.72214 13.0975 5.8777 13.192 6.06659C13.2864 6.25547 13.3337 6.45547 13.3337 6.66659V12.6666C13.3337 13.0333 13.2031 13.3471 12.942 13.6083C12.6809 13.8694 12.367 13.9999 12.0003 13.9999H9.33366C9.14477 13.9999 8.98644 13.936 8.85866 13.8083C8.73088 13.6805 8.66699 13.5221 8.66699 13.3333V9.99992H7.33366V13.3333C7.33366 13.5221 7.26977 13.6805 7.14199 13.8083C7.01421 13.936 6.85588 13.9999 6.66699 13.9999H4.00033C3.63366 13.9999 3.31977 13.8694 3.05866 13.6083C2.79755 13.3471 2.66699 13.0333 2.66699 12.6666Z",fill:"black",fillOpacity:.6}))),MS=e=>M.createElement("svg",{width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},M.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.9562 2.32392C7.04374 2.12694 7.23908 2 7.45464 2C8.03329 2 8.58824 2.22987 8.99741 2.63904C9.40657 3.0482 9.63644 3.60315 9.63644 4.18181V5.81816H12.1755C12.4115 5.81598 12.6452 5.86489 12.8606 5.96155C13.077 6.05868 13.2697 6.2017 13.4254 6.3807C13.5811 6.5597 13.6959 6.77041 13.7621 6.99821C13.8282 7.22602 13.844 7.46548 13.8084 7.7L13.0557 12.609C12.9965 12.999 12.7982 13.3549 12.4976 13.6104C12.1978 13.8653 11.8161 14.0036 11.4227 13.9999H3.63648C3.20249 13.9999 2.78627 13.8275 2.4794 13.5207C2.17252 13.2138 2.00012 12.7976 2.00012 12.3636V8.54542C2.00012 8.11143 2.17252 7.69522 2.4794 7.38834C2.78627 7.08146 3.20249 6.90906 3.63648 6.90906H4.91836L6.9562 2.32392ZM5.81828 7.57027L7.78618 3.1425C7.95057 3.19495 8.10174 3.28614 8.22602 3.41042C8.4306 3.615 8.54554 3.89248 8.54554 4.18181V6.36361C8.54554 6.66486 8.78974 6.90906 9.09099 6.90906H12.1782L12.1844 6.90903C12.2635 6.90813 12.3418 6.92444 12.4139 6.95681C12.4861 6.98919 12.5503 7.03686 12.6022 7.09653C12.6541 7.1562 12.6924 7.22643 12.7144 7.30237C12.7364 7.37823 12.7417 7.45797 12.7299 7.53608L11.9771 12.4454C11.9574 12.5755 11.8913 12.694 11.7911 12.7792C11.6909 12.8644 11.5632 12.9105 11.4317 12.9091L5.81828 12.909V7.57027ZM4.72738 12.909V7.99997H3.63648C3.49181 7.99997 3.35308 8.05743 3.25078 8.15972C3.14849 8.26202 3.09102 8.40075 3.09102 8.54542V12.3636C3.09102 12.5082 3.14849 12.647 3.25078 12.7493C3.35308 12.8516 3.49181 12.909 3.63648 12.909H4.72738Z",fill:"currentColor"})),RS=e=>M.createElement("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},M.createElement("path",{d:"M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z",stroke:"white",strokeOpacity:.4,strokeWidth:2,strokeLinecap:"round"}),M.createElement("path",{d:"M12 3C16.9706 3 21 7.02944 21 12",stroke:"white",strokeWidth:2,strokeLinecap:"round"})),OS=e=>M.createElement("svg",{width:20,height:21,viewBox:"0 0 20 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},M.createElement("mask",{id:"mask0_1789_620",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:0,y:0,width:20,height:21},M.createElement("rect",{y:.5,width:20,height:20,fill:"currentColor"})),M.createElement("g",{mask:"url(#mask0_1789_620)"},M.createElement("path",{d:"M16.7917 11.3333H7.5C7.26389 11.3333 7.06597 11.2535 6.90625 11.0938C6.74653 10.934 6.66667 10.7361 6.66667 10.5C6.66667 10.2639 6.74653 10.066 6.90625 9.90625C7.06597 9.74653 7.26389 9.66667 7.5 9.66667H16.7917L16.0833 8.95833C15.9167 8.79167 15.8368 8.59722 15.8438 8.375C15.8507 8.15278 15.9306 7.95833 16.0833 7.79167C16.25 7.625 16.4479 7.53819 16.6771 7.53125C16.9063 7.52431 17.1042 7.60417 17.2708 7.77083L19.4167 9.91667C19.5833 10.0833 19.6667 10.2778 19.6667 10.5C19.6667 10.7222 19.5833 10.9167 19.4167 11.0833L17.2708 13.2292C17.1042 13.3958 16.9063 13.4757 16.6771 13.4688C16.4479 13.4618 16.25 13.375 16.0833 13.2083C15.9306 13.0417 15.8507 12.8472 15.8438 12.625C15.8368 12.4028 15.9167 12.2083 16.0833 12.0417L16.7917 11.3333ZM12.5 7.16667V4.66667H4.16667V16.3333H12.5V13.8333C12.5 13.5972 12.5799 13.3993 12.7396 13.2396C12.8993 13.0799 13.0972 13 13.3333 13C13.5694 13 13.7674 13.0799 13.9271 13.2396C14.0868 13.3993 14.1667 13.5972 14.1667 13.8333V16.3333C14.1667 16.7917 14.0035 17.184 13.6771 17.5104C13.3507 17.8368 12.9583 18 12.5 18H4.16667C3.70833 18 3.31597 17.8368 2.98958 17.5104C2.66319 17.184 2.5 16.7917 2.5 16.3333V4.66667C2.5 4.20833 2.66319 3.81597 2.98958 3.48958C3.31597 3.16319 3.70833 3 4.16667 3H12.5C12.9583 3 13.3507 3.16319 13.6771 3.48958C14.0035 3.81597 14.1667 4.20833 14.1667 4.66667V7.16667C14.1667 7.40278 14.0868 7.60069 13.9271 7.76042C13.7674 7.92014 13.5694 8 13.3333 8C13.0972 8 12.8993 7.92014 12.7396 7.76042C12.5799 7.60069 12.5 7.40278 12.5 7.16667Z",fill:"currentColor"}))),IS=e=>M.createElement("svg",{width:32,height:32,viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},M.createElement("path",{d:"M9 23H10.425L20.2 13.225L18.775 11.8L9 21.575V23ZM8 25C7.71667 25 7.47917 24.9042 7.2875 24.7125C7.09583 24.5208 7 24.2833 7 24V21.575C7 21.3083 7.05 21.0542 7.15 20.8125C7.25 20.5708 7.39167 20.3583 7.575 20.175L20.2 7.575C20.4 7.39167 20.6208 7.25 20.8625 7.15C21.1042 7.05 21.3583 7 21.625 7C21.8917 7 22.15 7.05 22.4 7.15C22.65 7.25 22.8667 7.4 23.05 7.6L24.425 9C24.625 9.18333 24.7708 9.4 24.8625 9.65C24.9542 9.9 25 10.15 25 10.4C25 10.6667 24.9542 10.9208 24.8625 11.1625C24.7708 11.4042 24.625 11.625 24.425 11.825L11.825 24.425C11.6417 24.6083 11.4292 24.75 11.1875 24.85C10.9458 24.95 10.6917 25 10.425 25H8ZM19.475 12.525L18.775 11.8L20.2 13.225L19.475 12.525Z",fill:"black",fillOpacity:.6})),LS=e=>M.createElement("svg",{width:20,height:20,viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},M.createElement("path",{d:"M10.6932 13.0385C12.2861 12.242 13.3186 10.7081 13.3186 9.41014C13.1416 9.52814 12.8171 9.64613 12.4926 9.64613C11.4012 9.64613 10.6637 8.82017 10.6637 7.75822C10.6637 6.81427 11.3422 5.84082 12.6696 5.84082C13.9381 5.84082 15 7.02076 15 8.64318C15 11.2685 13.2006 13.2744 11.3127 14.1594L10.6932 13.0385ZM5.0295 13.0385C6.62242 12.242 7.65487 10.7081 7.65487 9.41014C7.47788 9.52814 7.18289 9.64613 6.85841 9.64613C5.73746 9.64613 5 8.82017 5 7.75822C5 6.81427 5.67847 5.84082 7.0059 5.84082C8.27434 5.84082 9.33628 7.02076 9.33628 8.64318C9.33628 11.2685 7.53687 13.2744 5.64897 14.1594L5.0295 13.0385Z",fill:"black",fillOpacity:.3})),VS=e=>M.createElement("svg",{width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},M.createElement("path",{d:"M13.2347 6.40214C13.2976 6.33929 13.3329 6.25405 13.3329 6.16518V3.98384C13.3329 3.85135 13.2802 3.72429 13.1866 3.63061C13.0929 3.53692 12.9658 3.48429 12.8333 3.48429C12.7008 3.48429 12.5737 3.53692 12.48 3.63061C12.3864 3.72429 12.3337 3.85135 12.3337 3.98384V4.89851C11.8417 4.2079 11.1915 3.64498 10.4376 3.25673C9.52044 2.78539 8.48626 2.59005 7.46031 2.69435C6.43437 2.79864 5.46066 3.19811 4.65708 3.84439C3.85351 4.49066 3.25452 5.35602 2.93266 6.3357C2.79028 6.76906 2.70483 7.21657 2.67662 7.66721C2.66513 7.85094 2.81545 8.00033 2.99954 8.00033H3.3347C3.5188 8.00033 3.6617 7.85103 3.66621 7.66699C3.68454 6.91876 3.89954 6.41479 4.2843 5.77278C4.66905 5.13078 5.21357 4.5994 5.86479 4.23043C6.51601 3.86146 7.25172 3.66748 8.00021 3.6674C8.69356 3.66688 9.37686 3.83313 9.99246 4.15213C10.6081 4.47113 11.1379 4.93352 11.5372 5.50029H10.667C10.538 5.50573 10.4161 5.56078 10.3268 5.65396C10.2375 5.74713 10.1876 5.87121 10.1876 6.00029C10.1876 6.12936 10.2375 6.25345 10.3268 6.34662C10.4161 6.43979 10.538 6.49485 10.667 6.50029H12.9978C13.0866 6.50029 13.1719 6.46498 13.2347 6.40214Z",fill:"currentColor"}),M.createElement("path",{d:"M2.76437 9.59852C2.70152 9.66136 2.66621 9.7466 2.66621 9.83547V12.0168C2.66621 12.1493 2.71884 12.2764 2.81253 12.37C2.90622 12.4637 3.03329 12.5164 3.16579 12.5164C3.29828 12.5164 3.42535 12.4637 3.51904 12.37C3.61273 12.2764 3.66536 12.1493 3.66536 12.0168V11.1021C4.15743 11.7928 4.80754 12.3557 5.56144 12.7439C6.47865 13.2153 7.51283 13.4106 8.53877 13.3063C9.56472 13.202 10.5384 12.8025 11.342 12.1563C12.1456 11.51 12.7446 10.6446 13.0664 9.66495C13.2088 9.23159 13.2943 8.78408 13.3225 8.33344C13.334 8.14971 13.1836 8.00033 12.9995 8.00033H12.6644C12.4803 8.00033 12.3374 8.14962 12.3329 8.33366C12.3145 9.08189 12.0995 9.58587 11.7148 10.2279C11.33 10.8699 10.7855 11.4012 10.1343 11.7702C9.48307 12.1392 8.74737 12.3332 7.99888 12.3333C7.30553 12.3338 6.62223 12.1675 6.00663 11.8485C5.39103 11.5295 4.8612 11.0671 4.46184 10.5004H5.3321C5.46107 10.4949 5.58294 10.4399 5.67227 10.3467C5.7616 10.2535 5.81148 10.1294 5.81148 10.0004C5.81148 9.87129 5.7616 9.7472 5.67227 9.65403C5.58294 9.56086 5.46107 9.5058 5.3321 9.50036H3.00134C2.91245 9.50036 2.82722 9.53567 2.76437 9.59852Z",fill:"currentColor"})),BS=e=>M.createElement("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},M.createElement("path",{d:"M4.025 19.975C4.375 20.325 4.80258 20.5 5.30775 20.5H8.25C8.46283 20.5 8.641 20.4282 8.7845 20.2845C8.92817 20.141 9 19.9628 9 19.75C9 19.5372 8.92817 19.359 8.7845 19.2155C8.641 19.0718 8.46283 19 8.25 19H5.30775C5.23075 19 5.16025 18.9679 5.09625 18.9038C5.03208 18.8398 5 18.7693 5 18.6923V15.75C5 15.5372 4.92817 15.359 4.7845 15.2155C4.641 15.0718 4.46283 15 4.25 15C4.03717 15 3.859 15.0718 3.7155 15.2155C3.57183 15.359 3.5 15.5372 3.5 15.75V18.6923C3.5 19.1974 3.675 19.625 4.025 19.975Z",fill:"currentColor"}),M.createElement("path",{d:"M3.5 5.30775V8.25C3.5 8.46283 3.57183 8.641 3.7155 8.7845C3.859 8.92817 4.03717 9 4.25 9C4.46283 9 4.641 8.92817 4.7845 8.7845C4.92817 8.641 5 8.46283 5 8.25V5.30775C5 5.23075 5.03208 5.16025 5.09625 5.09625C5.16025 5.03208 5.23075 5 5.30775 5H8.25C8.46283 5 8.641 4.92817 8.7845 4.7845C8.92817 4.641 9 4.46283 9 4.25C9 4.03717 8.92817 3.859 8.7845 3.7155C8.641 3.57183 8.46283 3.5 8.25 3.5H5.30775C4.80258 3.5 4.375 3.675 4.025 4.025C3.675 4.375 3.5 4.80258 3.5 5.30775Z",fill:"currentColor"}),M.createElement("path",{d:"M19.1795 8.16951L21.7963 10.7863C21.9288 10.919 21.9967 11.0858 21.9999 11.2867C22.0029 11.4875 21.9351 11.6573 21.7963 11.7963C21.6573 11.9351 21.489 12.0045 21.2913 12.0045C21.0935 12.0045 20.9252 11.9351 20.7863 11.7963L18.1695 9.17951C17.8587 9.40183 17.5132 9.57535 17.1329 9.70008C16.7528 9.82466 16.3563 9.88694 15.9435 9.88694C14.8439 9.88694 13.9117 9.5046 13.147 8.73991C12.3823 7.97522 12 7.04307 12 5.94347C12 4.84387 12.3823 3.91173 13.147 3.14704C13.9117 2.38235 14.8439 2 15.9435 2C17.0431 2 17.9752 2.38235 18.7399 3.14704C19.5046 3.91173 19.8869 4.84387 19.8869 5.94347C19.8869 6.37469 19.8215 6.7758 19.6907 7.1468C19.5599 7.51781 19.3895 7.85871 19.1795 8.16951ZM15.9435 8.4498C16.6449 8.4498 17.238 8.20744 17.7227 7.72272C18.2074 7.238 18.4498 6.64492 18.4498 5.94347C18.4498 5.24203 18.2074 4.64894 17.7227 4.16423C17.238 3.67951 16.6449 3.43715 15.9435 3.43715C15.242 3.43715 14.6489 3.67951 14.1642 4.16423C13.6795 4.64894 13.4371 5.24203 13.4371 5.94347C13.4371 6.64492 13.6795 7.238 14.1642 7.72272C14.6489 8.20744 15.242 8.4498 15.9435 8.4498Z",fill:"currentColor"}),M.createElement("path",{d:"M19 18.6922V15.75C19 15.5372 19.0718 15.359 19.2155 15.2155C19.359 15.0718 19.5372 15 19.75 15C19.9628 15 20.141 15.0718 20.2845 15.2155C20.4282 15.359 20.5 15.5372 20.5 15.75V18.6922C20.5 19.1974 20.325 19.625 19.975 19.975C19.625 20.325 19.1974 20.5 18.6923 20.5H15.75C15.5372 20.5 15.359 20.4282 15.2155 20.2845C15.0718 20.141 15 19.9628 15 19.75C15 19.5372 15.0718 19.359 15.2155 19.2155C15.359 19.0718 15.5372 19 15.75 19H18.6923C18.7693 19 18.8398 18.9679 18.9038 18.9038C18.9679 18.8398 19 18.7692 19 18.6922Z",fill:"currentColor"})),NS=e=>M.createElement("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},M.createElement("path",{d:"M5.30775 20.5C4.80258 20.5 4.375 20.325 4.025 19.975C3.675 19.625 3.5 19.1974 3.5 18.6923V5.30775C3.5 4.80258 3.675 4.375 4.025 4.025C4.375 3.675 4.80258 3.5 5.30775 3.5H18.6923C19.1974 3.5 19.625 3.675 19.975 4.025C20.325 4.375 20.5 4.80258 20.5 5.30775V18.6923C20.5 19.1974 20.325 19.625 19.975 19.975C19.625 20.325 19.1974 20.5 18.6923 20.5H5.30775ZM5.30775 19H18.6923C18.7692 19 18.8398 18.9679 18.9038 18.9038C18.9679 18.8398 19 18.7692 19 18.6923V5.30775C19 5.23075 18.9679 5.16025 18.9038 5.09625C18.8398 5.03208 18.7692 5 18.6923 5H5.30775C5.23075 5 5.16025 5.03208 5.09625 5.09625C5.03208 5.16025 5 5.23075 5 5.30775V18.6923C5 18.7692 5.03208 18.8398 5.09625 18.9038C5.16025 18.9679 5.23075 19 5.30775 19ZM8 16.75H13C13.2128 16.75 13.391 16.6782 13.5345 16.5345C13.6782 16.391 13.75 16.2128 13.75 16C13.75 15.7872 13.6782 15.609 13.5345 15.4655C13.391 15.3218 13.2128 15.25 13 15.25H8C7.78717 15.25 7.609 15.3218 7.4655 15.4655C7.32183 15.609 7.25 15.7872 7.25 16C7.25 16.2128 7.32183 16.391 7.4655 16.5345C7.609 16.6782 7.78717 16.75 8 16.75ZM8 12.75H16C16.2128 12.75 16.391 12.6782 16.5345 12.5345C16.6782 12.391 16.75 12.2128 16.75 12C16.75 11.7872 16.6782 11.609 16.5345 11.4655C16.391 11.3218 16.2128 11.25 16 11.25H8C7.78717 11.25 7.609 11.3218 7.4655 11.4655C7.32183 11.609 7.25 11.7872 7.25 12C7.25 12.2128 7.32183 12.391 7.4655 12.5345C7.609 12.6782 7.78717 12.75 8 12.75ZM8 8.75H16C16.2128 8.75 16.391 8.67817 16.5345 8.5345C16.6782 8.391 16.75 8.21283 16.75 8C16.75 7.78717 16.6782 7.609 16.5345 7.4655C16.391 7.32183 16.2128 7.25 16 7.25H8C7.78717 7.25 7.609 7.32183 7.4655 7.4655C7.32183 7.609 7.25 7.78717 7.25 8C7.25 8.21283 7.32183 8.391 7.4655 8.5345C7.609 8.67817 7.78717 8.75 8 8.75Z",fill:"currentColor"})),FS=e=>M.createElement("svg",{width:32,height:32,viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},M.createElement("circle",{cx:16,cy:16,r:16,fill:"#45464F"}),M.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16 7.11108C13.5454 7.11108 11.5556 9.10093 11.5556 11.5555C11.5556 14.0101 13.5454 16 16 16C18.4546 16 20.4444 14.0101 20.4444 11.5555C20.4444 9.10093 18.4546 7.11108 16 7.11108Z",fill:"#8F909A"}),M.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.3333 16.8889C11.8553 16.8889 10.434 17.5632 9.60103 18.5782C9.17828 19.0934 8.88108 19.7308 8.85521 20.4373C8.82878 21.1588 9.08921 21.863 9.6263 22.4774C10.9352 23.9748 13.0251 24.8889 16 24.8889C18.9749 24.8889 21.0648 23.9748 22.3737 22.4774C22.9108 21.863 23.1712 21.1588 23.1448 20.4373C23.1189 19.7308 22.8217 19.0934 22.399 18.5782C21.566 17.5632 20.1447 16.8889 18.6667 16.8889H13.3333Z",fill:"#8F909A"})),zS=e=>M.createElement("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},M.createElement("mask",{id:"mask0_3537_862",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:0,y:0,width:24,height:24},M.createElement("rect",{width:24,height:24,fill:"#D9D9D9"})),M.createElement("g",{mask:"url(#mask0_3537_862)"},M.createElement("path",{d:"M2.72479 21C2.54146 21 2.37479 20.9542 2.22479 20.8625C2.07479 20.7708 1.95812 20.65 1.87479 20.5C1.79146 20.35 1.74562 20.1875 1.73729 20.0125C1.72896 19.8375 1.77479 19.6667 1.87479 19.5L11.1248 3.5C11.2248 3.33333 11.354 3.20833 11.5123 3.125C11.6706 3.04167 11.8331 3 11.9998 3C12.1665 3 12.329 3.04167 12.4873 3.125C12.6456 3.20833 12.7748 3.33333 12.8748 3.5L22.1248 19.5C22.2248 19.6667 22.2706 19.8375 22.2623 20.0125C22.254 20.1875 22.2081 20.35 22.1248 20.5C22.0415 20.65 21.9248 20.7708 21.7748 20.8625C21.6248 20.9542 21.4581 21 21.2748 21H2.72479ZM4.44979 19H19.5498L11.9998 6L4.44979 19ZM11.9998 18C12.2831 18 12.5206 17.9042 12.7123 17.7125C12.904 17.5208 12.9998 17.2833 12.9998 17C12.9998 16.7167 12.904 16.4792 12.7123 16.2875C12.5206 16.0958 12.2831 16 11.9998 16C11.7165 16 11.479 16.0958 11.2873 16.2875C11.0956 16.4792 10.9998 16.7167 10.9998 17C10.9998 17.2833 11.0956 17.5208 11.2873 17.7125C11.479 17.9042 11.7165 18 11.9998 18ZM11.9998 15C12.2831 15 12.5206 14.9042 12.7123 14.7125C12.904 14.5208 12.9998 14.2833 12.9998 14V11C12.9998 10.7167 12.904 10.4792 12.7123 10.2875C12.5206 10.0958 12.2831 10 11.9998 10C11.7165 10 11.479 10.0958 11.2873 10.2875C11.0956 10.4792 10.9998 10.7167 10.9998 11V14C10.9998 14.2833 11.0956 14.5208 11.2873 14.7125C11.479 14.9042 11.7165 15 11.9998 15Z",fill:"white"})));function Ie(e){return t=>{const{size:n,style:r,...i}=t||{};return fo.jsx(Y0.span,{...i,children:e({style:{width:n,height:n}}),style:{display:"flex",...r}})}}function sn(e){return t=>{const{size:n,style:r,...i}=t||{};return fo.jsx(Y0.img,{...i,src:e,style:{width:n,height:n,...r}})}}const O9={CHN:sn(SS),ENG:sn(ES),kCircle:sn(mS),kSquare:sn(gS),kimi:sn(vS),kimiFailed:sn(yS),kimiReading:sn(wS),kimiSearching:sn(xS),kimiThinking:sn(_S)},I9={check:Ie(AS),close:Ie(CS),copy:Ie(bS),dislike:Ie(TS),enter:Ie(kS),home:Ie(PS),homemini:Ie(DS),like:Ie(MS),loading:Ie(RS),logout:Ie(OS),pen:Ie(IS),quote:Ie(LS),retry:Ie(VS),screenshot:Ie(BS),summary:Ie(NS),user:Ie(FS),warning:Ie(zS)};var mc={exports:{}};(function(e,t){(function(n,r){var i="1.0.38",o="",s="?",a="function",l="undefined",u="object",c="string",h="major",f="model",v="name",p="type",g="vendor",S="version",y="architecture",w="console",_="mobile",E="tablet",b="smarttv",k="wearable",P="embedded",C=500,D="Amazon",R="Apple",B="ASUS",Y="BlackBerry",J="Browser",ue="Chrome",j="Edge",ee="Firefox",te="Google",N="Huawei",H="LG",G="Microsoft",se="Motorola",re="Opera",Rt="Samsung",vt="Sharp",Yt="Sony",rt="Xiaomi",Zt="Zebra",Zo="Facebook",Xt="Chromium OS",Xo="Mac OS",al=function(Z,oe){var q={};for(var ce in Z)oe[ce]&&oe[ce].length%2===0?q[ce]=oe[ce].concat(Z[ce]):q[ce]=Z[ce];return q},Ir=function(Z){for(var oe={},q=0;q<Z.length;q++)oe[Z[q].toUpperCase()]=Z[q];return oe},Qo=function(Z,oe){return typeof Z===c?rr(oe).indexOf(rr(Z))!==-1:!1},rr=function(Z){return Z.toLowerCase()},ll=function(Z){return typeof Z===c?Z.replace(/[^\d\.]/g,o).split(".")[0]:r},Mi=function(Z,oe){if(typeof Z===c)return Z=Z.replace(/^\s\s*/,o),typeof oe===l?Z:Z.substring(0,C)},ir=function(Z,oe){for(var q=0,ce,Ot,yt,ie,K,wt;q<oe.length&&!K;){var or=oe[q],ts=oe[q+1];for(ce=Ot=0;ce<or.length&&!K&&or[ce];)if(K=or[ce++].exec(Z),K)for(yt=0;yt<ts.length;yt++)wt=K[++Ot],ie=ts[yt],typeof ie===u&&ie.length>0?ie.length===2?typeof ie[1]==a?this[ie[0]]=ie[1].call(this,wt):this[ie[0]]=ie[1]:ie.length===3?typeof ie[1]===a&&!(ie[1].exec&&ie[1].test)?this[ie[0]]=wt?ie[1].call(this,wt,ie[2]):r:this[ie[0]]=wt?wt.replace(ie[1],ie[2]):r:ie.length===4&&(this[ie[0]]=wt?ie[3].call(this,wt.replace(ie[1],ie[2])):r):this[ie]=wt||r;q+=2}},Ri=function(Z,oe){for(var q in oe)if(typeof oe[q]===u&&oe[q].length>0){for(var ce=0;ce<oe[q].length;ce++)if(Qo(oe[q][ce],Z))return q===s?r:q}else if(Qo(oe[q],Z))return q===s?r:q;return Z},ul={"1.0":"/8","1.2":"/1","1.3":"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"},Jo={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2","8.1":"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},es={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[S,[v,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[S,[v,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[v,S],[/opios[\/ ]+([\w\.]+)/i],[S,[v,re+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[S,[v,re+" GX"]],[/\bopr\/([\w\.]+)/i],[S,[v,re]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[S,[v,"Baidu"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim)\s?(?:browser)?[\/ ]?([\w\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[v,S],[/\bddg\/([\w\.]+)/i],[S,[v,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[S,[v,"UC"+J]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[S,[v,"WeChat"]],[/konqueror\/([\w\.]+)/i],[S,[v,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[S,[v,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[S,[v,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[S,[v,"Smart Lenovo "+J]],[/(avast|avg)\/([\w\.]+)/i],[[v,/(.+)/,"$1 Secure "+J],S],[/\bfocus\/([\w\.]+)/i],[S,[v,ee+" Focus"]],[/\bopt\/([\w\.]+)/i],[S,[v,re+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[S,[v,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[S,[v,"Dolphin"]],[/coast\/([\w\.]+)/i],[S,[v,re+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[S,[v,"MIUI "+J]],[/fxios\/([-\w\.]+)/i],[S,[v,ee]],[/\bqihu|(qi?ho?o?|360)browser/i],[[v,"360 "+J]],[/(oculus|sailfish|huawei|vivo)browser\/([\w\.]+)/i],[[v,/(.+)/,"$1 "+J],S],[/samsungbrowser\/([\w\.]+)/i],[S,[v,Rt+" Internet"]],[/(comodo_dragon)\/([\w\.]+)/i],[[v,/_/g," "],S],[/metasr[\/ ]?([\d\.]+)/i],[S,[v,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[v,"Sogou Mobile"],S],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345Explorer)[\/ ]?([\w\.]+)/i],[v,S],[/(lbbrowser)/i,/\[(linkedin)app\]/i],[v],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[v,Zo],S],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(chromium|instagram|snapchat)[\/ ]([-\w\.]+)/i],[v,S],[/\bgsa\/([\w\.]+) .*safari\//i],[S,[v,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[S,[v,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[S,[v,ue+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[v,ue+" WebView"],S],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[S,[v,"Android "+J]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[v,S],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[S,[v,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[S,v],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[v,[S,Ri,ul]],[/(webkit|khtml)\/([\w\.]+)/i],[v,S],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[v,"Netscape"],S],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[S,[v,ee+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[v,S],[/(cobalt)\/([\w\.]+)/i],[v,[S,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[y,"amd64"]],[/(ia32(?=;))/i],[[y,rr]],[/((?:i[346]|x)86)[;\)]/i],[[y,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[y,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[y,"armhf"]],[/windows (ce|mobile); ppc;/i],[[y,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[y,/ower/,o,rr]],[/(sun4\w)[;\)]/i],[[y,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[y,rr]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[f,[g,Rt],[p,E]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[f,[g,Rt],[p,_]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[f,[g,R],[p,_]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[f,[g,R],[p,E]],[/(macintosh);/i],[f,[g,R]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[f,[g,vt],[p,_]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[f,[g,N],[p,E]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[f,[g,N],[p,_]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[f,/_/g," "],[g,rt],[p,_]],[/oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i,/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[f,/_/g," "],[g,rt],[p,E]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[f,[g,"OPPO"],[p,_]],[/\b(opd2\d{3}a?) bui/i],[f,[g,"OPPO"],[p,E]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[f,[g,"Vivo"],[p,_]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[f,[g,"Realme"],[p,_]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[f,[g,se],[p,_]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[f,[g,se],[p,E]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[f,[g,H],[p,E]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[f,[g,H],[p,_]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[f,[g,"Lenovo"],[p,E]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[f,/_/g," "],[g,"Nokia"],[p,_]],[/(pixel c)\b/i],[f,[g,te],[p,E]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[f,[g,te],[p,_]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[f,[g,Yt],[p,_]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[f,"Xperia Tablet"],[g,Yt],[p,E]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[f,[g,"OnePlus"],[p,_]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[f,[g,D],[p,E]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[f,/(.+)/g,"Fire Phone $1"],[g,D],[p,_]],[/(playbook);[-\w\),; ]+(rim)/i],[f,g,[p,E]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[f,[g,Y],[p,_]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[f,[g,B],[p,E]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[f,[g,B],[p,_]],[/(nexus 9)/i],[f,[g,"HTC"],[p,E]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[g,[f,/_/g," "],[p,_]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[f,[g,"Acer"],[p,E]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[f,[g,"Meizu"],[p,_]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[f,[g,"Ulefone"],[p,_]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[g,f,[p,_]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[g,f,[p,E]],[/(surface duo)/i],[f,[g,G],[p,E]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[f,[g,"Fairphone"],[p,_]],[/(u304aa)/i],[f,[g,"AT&T"],[p,_]],[/\bsie-(\w*)/i],[f,[g,"Siemens"],[p,_]],[/\b(rct\w+) b/i],[f,[g,"RCA"],[p,E]],[/\b(venue[\d ]{2,7}) b/i],[f,[g,"Dell"],[p,E]],[/\b(q(?:mv|ta)\w+) b/i],[f,[g,"Verizon"],[p,E]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[f,[g,"Barnes & Noble"],[p,E]],[/\b(tm\d{3}\w+) b/i],[f,[g,"NuVision"],[p,E]],[/\b(k88) b/i],[f,[g,"ZTE"],[p,E]],[/\b(nx\d{3}j) b/i],[f,[g,"ZTE"],[p,_]],[/\b(gen\d{3}) b.+49h/i],[f,[g,"Swiss"],[p,_]],[/\b(zur\d{3}) b/i],[f,[g,"Swiss"],[p,E]],[/\b((zeki)?tb.*\b) b/i],[f,[g,"Zeki"],[p,E]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[g,"Dragon Touch"],f,[p,E]],[/\b(ns-?\w{0,9}) b/i],[f,[g,"Insignia"],[p,E]],[/\b((nxa|next)-?\w{0,9}) b/i],[f,[g,"NextBook"],[p,E]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[g,"Voice"],f,[p,_]],[/\b(lvtel\-)?(v1[12]) b/i],[[g,"LvTel"],f,[p,_]],[/\b(ph-1) /i],[f,[g,"Essential"],[p,_]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[f,[g,"Envizen"],[p,E]],[/\b(trio[-\w\. ]+) b/i],[f,[g,"MachSpeed"],[p,E]],[/\btu_(1491) b/i],[f,[g,"Rotor"],[p,E]],[/(shield[\w ]+) b/i],[f,[g,"Nvidia"],[p,E]],[/(sprint) (\w+)/i],[g,f,[p,_]],[/(kin\.[onetw]{3})/i],[[f,/\./g," "],[g,G],[p,_]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[f,[g,Zt],[p,E]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[f,[g,Zt],[p,_]],[/smart-tv.+(samsung)/i],[g,[p,b]],[/hbbtv.+maple;(\d+)/i],[[f,/^/,"SmartTV"],[g,Rt],[p,b]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[g,H],[p,b]],[/(apple) ?tv/i],[g,[f,R+" TV"],[p,b]],[/crkey/i],[[f,ue+"cast"],[g,te],[p,b]],[/droid.+aft(\w+)( bui|\))/i],[f,[g,D],[p,b]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[f,[g,vt],[p,b]],[/(bravia[\w ]+)( bui|\))/i],[f,[g,Yt],[p,b]],[/(mitv-\w{5}) bui/i],[f,[g,rt],[p,b]],[/Hbbtv.*(technisat) (.*);/i],[g,f,[p,b]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[g,Mi],[f,Mi],[p,b]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[p,b]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[g,f,[p,w]],[/droid.+; (shield) bui/i],[f,[g,"Nvidia"],[p,w]],[/(playstation [345portablevi]+)/i],[f,[g,Yt],[p,w]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[f,[g,G],[p,w]],[/((pebble))app/i],[g,f,[p,k]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[f,[g,R],[p,k]],[/droid.+; (glass) \d/i],[f,[g,te],[p,k]],[/droid.+; (wt63?0{2,3})\)/i],[f,[g,Zt],[p,k]],[/(quest( \d| pro)?)/i],[f,[g,Zo],[p,k]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[g,[p,P]],[/(aeobc)\b/i],[f,[g,D],[p,P]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+? mobile safari/i],[f,[p,_]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[f,[p,E]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[p,E]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[p,_]],[/(android[-\w\. ]{0,9});.+buil/i],[f,[g,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[S,[v,j+"HTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[S,[v,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[v,S],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[S,v]],os:[[/microsoft (windows) (vista|xp)/i],[v,S],[/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i],[v,[S,Ri,Jo]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[S,Ri,Jo],[v,"Windows"]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[S,/_/g,"."],[v,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[v,Xo],[S,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[S,v],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[v,S],[/\(bb(10);/i],[S,[v,Y]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[S,[v,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[S,[v,ee+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[S,[v,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[S,[v,"watchOS"]],[/crkey\/([\d\.]+)/i],[S,[v,ue+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[v,Xt],S],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[v,S],[/(sunos) ?([\w\.\d]*)/i],[[v,"Solaris"],S],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[v,S]]},it=function(Z,oe){if(typeof Z===u&&(oe=Z,Z=r),!(this instanceof it))return new it(Z,oe).getResult();var q=typeof n!==l&&n.navigator?n.navigator:r,ce=Z||(q&&q.userAgent?q.userAgent:o),Ot=q&&q.userAgentData?q.userAgentData:r,yt=oe?al(es,oe):es,ie=q&&q.userAgent==ce;return this.getBrowser=function(){var K={};return K[v]=r,K[S]=r,ir.call(K,ce,yt.browser),K[h]=ll(K[S]),ie&&q&&q.brave&&typeof q.brave.isBrave==a&&(K[v]="Brave"),K},this.getCPU=function(){var K={};return K[y]=r,ir.call(K,ce,yt.cpu),K},this.getDevice=function(){var K={};return K[g]=r,K[f]=r,K[p]=r,ir.call(K,ce,yt.device),ie&&!K[p]&&Ot&&Ot.mobile&&(K[p]=_),ie&&K[f]=="Macintosh"&&q&&typeof q.standalone!==l&&q.maxTouchPoints&&q.maxTouchPoints>2&&(K[f]="iPad",K[p]=E),K},this.getEngine=function(){var K={};return K[v]=r,K[S]=r,ir.call(K,ce,yt.engine),K},this.getOS=function(){var K={};return K[v]=r,K[S]=r,ir.call(K,ce,yt.os),ie&&!K[v]&&Ot&&Ot.platform&&Ot.platform!="Unknown"&&(K[v]=Ot.platform.replace(/chrome os/i,Xt).replace(/macos/i,Xo)),K},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return ce},this.setUA=function(K){return ce=typeof K===c&&K.length>C?Mi(K,C):K,this},this.setUA(ce),this};it.VERSION=i,it.BROWSER=Ir([v,S,h]),it.CPU=Ir([y]),it.DEVICE=Ir([f,g,p,w,_,b,E,k,P]),it.ENGINE=it.OS=Ir([v,S]),e.exports&&(t=e.exports=it),t.UAParser=it;var An=typeof n!==l&&(n.jQuery||n.Zepto);if(An&&!An.ua){var Lr=new it;An.ua=Lr.getResult(),An.ua.get=function(){return Lr.getUA()},An.ua.set=function(Z){Lr.setUA(Z);var oe=Lr.getResult();for(var q in oe)An.ua[q]=oe[q]}}})(typeof window=="object"?window:sm)})(mc,mc.exports);var US=mc.exports;const jS="1.1.3",Rp="prod",GS={dev:"https://oversea.dev.kimi.team",pre:"https://kimi-pre.msh.team",prod:"https://kimi.moonshot.cn"},pe={name:"kimi-web-extension",env:Rp,version:jS,platform:"web-extension",timeZone:Intl.DateTimeFormat().resolvedOptions().timeZone,url:GS[Rp],storage:{version:"",access_token:"",refresh_token:"",browser_name:"",explore_toolbar:!0,explore_underline:!0,chat_float_button:!0,chat_window_mode:"modal"},dom:{id:"data-kimi-id",disabled:"data-kimi-disabled",recommend:"data-kimi-recommend"},feature:{sidepanel:["Chrome","Edge"]}},Ql={shortcuts:"chrome://extensions/shortcuts",home:pe.url,login:pe.url+"/extension/login",welcome:pe.url+"/extension/welcome",uninstall:pe.url+"/extension/uninstall",modeluse:pe.url+"/user/agreement/modeluse",userprivacy:pe.url+"/user/agreement/userprivacy"},ln={tracking_event:"tracking_event",tracking_profile:"tracking_profile",open_sidepanel:"open_sidepanel",close_sidepanel:"close_sidepanel",new_tab:"new_tab",get_commands:"get_commands",screenshot:"screenshot",command:"command",url_changed:"url_changed",get_web_info:"get_web_info",sidepanel_opened:"sidepanel_opened"};var HS={VITE_CJS_IGNORE_WARNING:"true",BASE_URL:"/",MODE:"prod",DEV:!1,PROD:!0,SSR:!1,MANIFEST_VERSION:3,BROWSER:"chrome",CHROME:!0,FIREFOX:!1,SAFARI:!1,EDGE:!1,OPERA:!1,COMMAND:"build",ENTRYPOINT:"html"};const Op=e=>{let t;const n=new Set,r=(c,h)=>{const f=typeof c=="function"?c(t):c;if(!Object.is(f,t)){const v=t;t=h??(typeof f!="object"||f===null)?f:Object.assign({},t,f),n.forEach(p=>p(t,v))}},i=()=>t,l={setState:r,getState:i,getInitialState:()=>u,subscribe:c=>(n.add(c),()=>n.delete(c)),destroy:()=>{(HS?"prod":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},u=t=e(r,i,l);return l},WS=e=>e?Op(e):Op;var Z0={exports:{}},X0={},Q0={exports:{}},J0={};/**
 * @license React
 * use-sync-external-store-shim.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xi=M;function KS(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var qS=typeof Object.is=="function"?Object.is:KS,$S=xi.useState,YS=xi.useEffect,ZS=xi.useLayoutEffect,XS=xi.useDebugValue;function QS(e,t){var n=t(),r=$S({inst:{value:n,getSnapshot:t}}),i=r[0].inst,o=r[1];return ZS(function(){i.value=n,i.getSnapshot=t,Jl(i)&&o({inst:i})},[e,n,t]),YS(function(){return Jl(i)&&o({inst:i}),e(function(){Jl(i)&&o({inst:i})})},[e]),XS(n),n}function Jl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!qS(e,n)}catch{return!0}}function JS(e,t){return t()}var e5=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?JS:QS;J0.useSyncExternalStore=xi.useSyncExternalStore!==void 0?xi.useSyncExternalStore:e5;Q0.exports=J0;var t5=Q0.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var nl=M,n5=t5;function r5(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var i5=typeof Object.is=="function"?Object.is:r5,o5=n5.useSyncExternalStore,s5=nl.useRef,a5=nl.useEffect,l5=nl.useMemo,u5=nl.useDebugValue;X0.useSyncExternalStoreWithSelector=function(e,t,n,r,i){var o=s5(null);if(o.current===null){var s={hasValue:!1,value:null};o.current=s}else s=o.current;o=l5(function(){function l(v){if(!u){if(u=!0,c=v,v=r(v),i!==void 0&&s.hasValue){var p=s.value;if(i(p,v))return h=p}return h=v}if(p=h,i5(c,v))return p;var g=r(v);return i!==void 0&&i(p,g)?p:(c=v,h=g)}var u=!1,c,h,f=n===void 0?null:n;return[function(){return l(t())},f===null?void 0:function(){return l(f())}]},[t,n,r,i]);var a=o5(e,o[0],o[1]);return a5(function(){s.hasValue=!0,s.value=a},[a]),u5(a),a};Z0.exports=X0;var c5=Z0.exports;const f5=Ma(c5);var ev={VITE_CJS_IGNORE_WARNING:"true",BASE_URL:"/",MODE:"prod",DEV:!1,PROD:!0,SSR:!1,MANIFEST_VERSION:3,BROWSER:"chrome",CHROME:!0,FIREFOX:!1,SAFARI:!1,EDGE:!1,OPERA:!1,COMMAND:"build",ENTRYPOINT:"html"};const{useDebugValue:h5}=Ly,{useSyncExternalStoreWithSelector:d5}=f5;let Ip=!1;const p5=e=>e;function m5(e,t=p5,n){(ev?"prod":void 0)!=="production"&&n&&!Ip&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),Ip=!0);const r=d5(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return h5(r),r}const Lp=e=>{(ev?"prod":void 0)!=="production"&&typeof e!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t=typeof e=="function"?WS(e):e,n=(r,i)=>m5(t,r,i);return Object.assign(n,t),n},g5=e=>e?Lp(e):Lp,v5=e=>(t,n,r)=>{const i=r.subscribe;return r.subscribe=(s,a,l)=>{let u=s;if(a){const c=(l==null?void 0:l.equalityFn)||Object.is;let h=s(r.getState());u=f=>{const v=s(f);if(!c(h,v)){const p=h;a(h=v,p)}},l!=null&&l.fireImmediately&&a(h,h)}return i(u)},e(t,n,r)},y5=v5;var tv=Symbol.for("immer-nothing"),Vp=Symbol.for("immer-draftable"),dt=Symbol.for("immer-state");function Gt(e,...t){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var _i=Object.getPrototypeOf;function Si(e){return!!e&&!!e[dt]}function Pr(e){var t;return e?nv(e)||Array.isArray(e)||!!e[Vp]||!!((t=e.constructor)!=null&&t[Vp])||il(e)||ol(e):!1}var w5=Object.prototype.constructor.toString();function nv(e){if(!e||typeof e!="object")return!1;const t=_i(e);if(t===null)return!0;const n=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return n===Object?!0:typeof n=="function"&&Function.toString.call(n)===w5}function Ta(e,t){rl(e)===0?Reflect.ownKeys(e).forEach(n=>{t(n,e[n],e)}):e.forEach((n,r)=>t(r,n,e))}function rl(e){const t=e[dt];return t?t.type_:Array.isArray(e)?1:il(e)?2:ol(e)?3:0}function gc(e,t){return rl(e)===2?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function rv(e,t,n){const r=rl(e);r===2?e.set(t,n):r===3?e.add(n):e[t]=n}function x5(e,t){return e===t?e!==0||1/e===1/t:e!==e&&t!==t}function il(e){return e instanceof Map}function ol(e){return e instanceof Set}function pr(e){return e.copy_||e.base_}function vc(e,t){if(il(e))return new Map(e);if(ol(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const n=nv(e);if(t===!0||t==="class_only"&&!n){const r=Object.getOwnPropertyDescriptors(e);delete r[dt];let i=Reflect.ownKeys(r);for(let o=0;o<i.length;o++){const s=i[o],a=r[s];a.writable===!1&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(r[s]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[s]})}return Object.create(_i(e),r)}else{const r=_i(e);if(r!==null&&n)return{...e};const i=Object.create(r);return Object.assign(i,e)}}function $f(e,t=!1){return sl(e)||Si(e)||!Pr(e)||(rl(e)>1&&(e.set=e.add=e.clear=e.delete=_5),Object.freeze(e),t&&Object.entries(e).forEach(([n,r])=>$f(r,!0))),e}function _5(){Gt(2)}function sl(e){return Object.isFrozen(e)}var S5={};function Dr(e){const t=S5[e];return t||Gt(0,e),t}var Lo;function iv(){return Lo}function E5(e,t){return{drafts_:[],parent_:e,immer_:t,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function Bp(e,t){t&&(Dr("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function yc(e){wc(e),e.drafts_.forEach(A5),e.drafts_=null}function wc(e){e===Lo&&(Lo=e.parent_)}function Np(e){return Lo=E5(Lo,e)}function A5(e){const t=e[dt];t.type_===0||t.type_===1?t.revoke_():t.revoked_=!0}function Fp(e,t){t.unfinalizedDrafts_=t.drafts_.length;const n=t.drafts_[0];return e!==void 0&&e!==n?(n[dt].modified_&&(yc(t),Gt(4)),Pr(e)&&(e=ka(t,e),t.parent_||Pa(t,e)),t.patches_&&Dr("Patches").generateReplacementPatches_(n[dt].base_,e,t.patches_,t.inversePatches_)):e=ka(t,n,[]),yc(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==tv?e:void 0}function ka(e,t,n){if(sl(t))return t;const r=t[dt];if(!r)return Ta(t,(i,o)=>zp(e,r,t,i,o,n)),t;if(r.scope_!==e)return t;if(!r.modified_)return Pa(e,r.base_,!0),r.base_;if(!r.finalized_){r.finalized_=!0,r.scope_.unfinalizedDrafts_--;const i=r.copy_;let o=i,s=!1;r.type_===3&&(o=new Set(i),i.clear(),s=!0),Ta(o,(a,l)=>zp(e,r,i,a,l,n,s)),Pa(e,i,!1),n&&e.patches_&&Dr("Patches").generatePatches_(r,n,e.patches_,e.inversePatches_)}return r.copy_}function zp(e,t,n,r,i,o,s){if(Si(i)){const a=o&&t&&t.type_!==3&&!gc(t.assigned_,r)?o.concat(r):void 0,l=ka(e,i,a);if(rv(n,r,l),Si(l))e.canAutoFreeze_=!1;else return}else s&&n.add(i);if(Pr(i)&&!sl(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;ka(e,i),(!t||!t.scope_.parent_)&&typeof r!="symbol"&&Object.prototype.propertyIsEnumerable.call(n,r)&&Pa(e,i)}}function Pa(e,t,n=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&$f(t,n)}function C5(e,t){const n=Array.isArray(e),r={type_:n?1:0,scope_:t?t.scope_:iv(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let i=r,o=Yf;n&&(i=[r],o=Vo);const{revoke:s,proxy:a}=Proxy.revocable(i,o);return r.draft_=a,r.revoke_=s,a}var Yf={get(e,t){if(t===dt)return e;const n=pr(e);if(!gc(n,t))return b5(e,n,t);const r=n[t];return e.finalized_||!Pr(r)?r:r===eu(e.base_,t)?(tu(e),e.copy_[t]=_c(r,e)):r},has(e,t){return t in pr(e)},ownKeys(e){return Reflect.ownKeys(pr(e))},set(e,t,n){const r=ov(pr(e),t);if(r!=null&&r.set)return r.set.call(e.draft_,n),!0;if(!e.modified_){const i=eu(pr(e),t),o=i==null?void 0:i[dt];if(o&&o.base_===n)return e.copy_[t]=n,e.assigned_[t]=!1,!0;if(x5(n,i)&&(n!==void 0||gc(e.base_,t)))return!0;tu(e),xc(e)}return e.copy_[t]===n&&(n!==void 0||t in e.copy_)||Number.isNaN(n)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=n,e.assigned_[t]=!0),!0},deleteProperty(e,t){return eu(e.base_,t)!==void 0||t in e.base_?(e.assigned_[t]=!1,tu(e),xc(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0},getOwnPropertyDescriptor(e,t){const n=pr(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r&&{writable:!0,configurable:e.type_!==1||t!=="length",enumerable:r.enumerable,value:n[t]}},defineProperty(){Gt(11)},getPrototypeOf(e){return _i(e.base_)},setPrototypeOf(){Gt(12)}},Vo={};Ta(Yf,(e,t)=>{Vo[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}});Vo.deleteProperty=function(e,t){return Vo.set.call(this,e,t,void 0)};Vo.set=function(e,t,n){return Yf.set.call(this,e[0],t,n,e[0])};function eu(e,t){const n=e[dt];return(n?pr(n):e)[t]}function b5(e,t,n){var i;const r=ov(t,n);return r?"value"in r?r.value:(i=r.get)==null?void 0:i.call(e.draft_):void 0}function ov(e,t){if(!(t in e))return;let n=_i(e);for(;n;){const r=Object.getOwnPropertyDescriptor(n,t);if(r)return r;n=_i(n)}}function xc(e){e.modified_||(e.modified_=!0,e.parent_&&xc(e.parent_))}function tu(e){e.copy_||(e.copy_=vc(e.base_,e.scope_.immer_.useStrictShallowCopy_))}var T5=class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(t,n,r)=>{if(typeof t=="function"&&typeof n!="function"){const o=n;n=t;const s=this;return function(l=o,...u){return s.produce(l,c=>n.call(this,c,...u))}}typeof n!="function"&&Gt(6),r!==void 0&&typeof r!="function"&&Gt(7);let i;if(Pr(t)){const o=Np(this),s=_c(t,void 0);let a=!0;try{i=n(s),a=!1}finally{a?yc(o):wc(o)}return Bp(o,r),Fp(i,o)}else if(!t||typeof t!="object"){if(i=n(t),i===void 0&&(i=t),i===tv&&(i=void 0),this.autoFreeze_&&$f(i,!0),r){const o=[],s=[];Dr("Patches").generateReplacementPatches_(t,i,o,s),r(o,s)}return i}else Gt(1,t)},this.produceWithPatches=(t,n)=>{if(typeof t=="function")return(s,...a)=>this.produceWithPatches(s,l=>t(l,...a));let r,i;return[this.produce(t,n,(s,a)=>{r=s,i=a}),r,i]},typeof(e==null?void 0:e.autoFreeze)=="boolean"&&this.setAutoFreeze(e.autoFreeze),typeof(e==null?void 0:e.useStrictShallowCopy)=="boolean"&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){Pr(e)||Gt(8),Si(e)&&(e=k5(e));const t=Np(this),n=_c(e,void 0);return n[dt].isManual_=!0,wc(t),n}finishDraft(e,t){const n=e&&e[dt];(!n||!n.isManual_)&&Gt(9);const{scope_:r}=n;return Bp(r,t),Fp(void 0,r)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let n;for(n=t.length-1;n>=0;n--){const i=t[n];if(i.path.length===0&&i.op==="replace"){e=i.value;break}}n>-1&&(t=t.slice(n+1));const r=Dr("Patches").applyPatches_;return Si(e)?r(e,t):this.produce(e,i=>r(i,t))}};function _c(e,t){const n=il(e)?Dr("MapSet").proxyMap_(e,t):ol(e)?Dr("MapSet").proxySet_(e,t):C5(e,t);return(t?t.scope_:iv()).drafts_.push(n),n}function k5(e){return Si(e)||Gt(10,e),sv(e)}function sv(e){if(!Pr(e)||sl(e))return e;const t=e[dt];let n;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,n=vc(e,t.scope_.immer_.useStrictShallowCopy_)}else n=vc(e,!0);return Ta(n,(r,i)=>{rv(n,r,sv(i))}),t&&(t.finalized_=!1),n}var pt=new T5,P5=pt.produce;pt.produceWithPatches.bind(pt);pt.setAutoFreeze.bind(pt);pt.setUseStrictShallowCopy.bind(pt);pt.applyPatches.bind(pt);pt.createDraft.bind(pt);pt.finishDraft.bind(pt);const D5=e=>(t,n,r)=>(r.setState=(i,o,...s)=>{const a=typeof i=="function"?P5(i):i;return t(a,o,...s)},e(r.setState,n,r)),M5=D5;function av(e){const{state:t,actions:n}=e,r=g5(y5(M5(()=>t)));return{useState:r,getState:r.getState,resetState:()=>r.setState(t),subscribe:r.subscribe,...n==null?void 0:n({getState:r.getState,setState:r.setState})}}function R5(e){return Array.isArray(e)?e:[e]}var Up=Object.prototype.hasOwnProperty;function Sc(e,t){var n,r;if(e===t)return!0;if(e&&t&&(n=e.constructor)===t.constructor){if(n===Date)return e.getTime()===t.getTime();if(n===RegExp)return e.toString()===t.toString();if(n===Array){if((r=e.length)===t.length)for(;r--&&Sc(e[r],t[r]););return r===-1}if(!n||typeof e=="object"){r=0;for(n in e)if(Up.call(e,n)&&++r&&!Up.call(t,n)||!(n in t)||!Sc(e[n],t[n]))return!1;return Object.keys(t).length===r}}return e!==e&&t!==t}var nu=_1;function Cs(e,...t){if(typeof t[0]=="string"){const n=t.shift();e(`[wxt] ${n}`,...t)}else e("[wxt]",...t)}var ru={debug:(...e)=>Cs(console.debug,...e),log:(...e)=>Cs(console.log,...e),warn:(...e)=>Cs(console.warn,...e),error:(...e)=>Cs(console.error,...e)},bs=O5();function O5(){const e={local:Ts("local"),session:Ts("session"),sync:Ts("sync"),managed:Ts("managed")},t=p=>{const g=e[p];if(g==null){const S=Object.keys(e).join(", ");throw Error(`Invalid area "${p}". Options: ${S}`)}return g},n=p=>{const g=p.indexOf(":"),S=p.substring(0,g),y=p.substring(g+1);if(y==null)throw Error(`Storage key should be in the form of "area:key", but received "${p}"`);return{driverArea:S,driverKey:y,driver:t(S)}},r=p=>p+"$",i=(p,g)=>p??g??null,o=p=>typeof p=="object"&&!Array.isArray(p)?p:{},s=async(p,g,S)=>{const y=await p.getItem(g);return i(y,S==null?void 0:S.defaultValue)},a=async(p,g)=>{const S=r(g),y=await p.getItem(S);return o(y)},l=async(p,g,S)=>{await p.setItem(g,S??null)},u=async(p,g,S)=>{const y=r(g),_={...o(await p.getItem(y))};Object.entries(S).forEach(([E,b])=>{b==null?delete _[E]:_[E]=b}),await p.setItem(y,_)},c=async(p,g,S)=>{if(await p.removeItem(g),S!=null&&S.removeMeta){const y=r(g);await p.removeItem(y)}},h=async(p,g,S)=>{const y=r(g);if(S==null)await p.removeItem(y);else{const w=o(await p.getItem(y));R5(S).forEach(_=>delete w[_]),await p.setItem(y,w)}},f=(p,g,S)=>p.watch(g,S);return{getItem:async(p,g)=>{const{driver:S,driverKey:y}=n(p);return await s(S,y,g)},getItems:async p=>{const g=new Map,S=new Map;return p.forEach(w=>{let _,E;typeof w=="string"?_=w:(_=w.key,E=w.options);const{driverArea:b,driverKey:k}=n(_),P=g.get(b)??[];g.set(b,P.concat(k)),S.set(_,E)}),(await Promise.all(Array.from(g.entries()).map(async([w,_])=>(await e[w].getItems(_)).map(b=>{var C;const k=`${w}:${b.key}`,P=i(b.value,(C=S.get(k))==null?void 0:C.defaultValue);return{key:k,value:P}})))).flat()},getMeta:async p=>{const{driver:g,driverKey:S}=n(p);return await a(g,S)},setItem:async(p,g)=>{const{driver:S,driverKey:y}=n(p);await l(S,y,g)},setItems:async p=>{const g=new Map;p.forEach(({key:S,value:y})=>{const{driverArea:w,driverKey:_}=n(S),E=g.get(w)??[];g.set(w,E.concat({key:_,value:y}))}),await Promise.all(Array.from(g.entries()).map(async([S,y])=>{await t(S).setItems(y)}))},setMeta:async(p,g)=>{const{driver:S,driverKey:y}=n(p);await u(S,y,g)},removeItem:async(p,g)=>{const{driver:S,driverKey:y}=n(p);await c(S,y,g)},removeItems:async p=>{const g=new Map;p.forEach(S=>{let y,w;typeof S=="string"?y=S:(y=S.key,w=S.options);const{driverArea:_,driverKey:E}=n(y),b=g.get(_)??[];b.push(E),w!=null&&w.removeMeta&&b.push(r(E)),g.set(_,b)}),await Promise.all(Array.from(g.entries()).map(async([S,y])=>{await t(S).removeItems(y)}))},removeMeta:async(p,g)=>{const{driver:S,driverKey:y}=n(p);await h(S,y,g)},snapshot:async(p,g)=>{var w;const y=await t(p).snapshot();return(w=g==null?void 0:g.excludeKeys)==null||w.forEach(_=>{delete y[_],delete y[r(_)]}),y},restoreSnapshot:async(p,g)=>{await t(p).restoreSnapshot(g)},watch:(p,g)=>{const{driver:S,driverKey:y}=n(p);return f(S,y,g)},unwatch(){Object.values(e).forEach(p=>{p.unwatch()})},defineItem:(p,g)=>{const{driver:S,driverKey:y}=n(p),{version:w=1,migrations:_={}}=g??{};if(w<1)throw Error("Storage item version cannot be less than 1. Initial versions should be set to 1, not 0.");const E=async()=>{var J;const P=r(y),[{value:C},{value:D}]=await S.getItems([y,P]);if(C==null)return;const R=(D==null?void 0:D.v)??1;if(R>w)throw Error(`Version downgrade detected (v${R} -> v${w}) for "${p}"`);ru.debug(`Running storage migration for ${p}: v${R} -> v${w}`);const B=Array.from({length:w-R},(ue,j)=>R+j+1);let Y=C;for(const ue of B)Y=await((J=_==null?void 0:_[ue])==null?void 0:J.call(_,Y))??Y;await S.setItems([{key:y,value:Y},{key:P,value:{...D,v:w}}]),ru.debug(`Storage migration completed for ${p} v${w}`,{migratedValue:Y})},b=(g==null?void 0:g.migrations)==null?Promise.resolve():E().catch(P=>{ru.error(`Migration failed for ${p}`,P)}),k=()=>(g==null?void 0:g.defaultValue)??null;return{get defaultValue(){return k()},getValue:async()=>(await b,await s(S,y,g)),getMeta:async()=>(await b,await a(S,y)),setValue:async P=>(await b,await l(S,y,P)),setMeta:async P=>(await b,await u(S,y,P)),removeValue:async P=>(await b,await c(S,y,P)),removeMeta:async P=>(await b,await h(S,y,P)),watch:P=>f(S,y,(C,D)=>P(C??k(),D??k())),migrate:E}}}}function Ts(e){const t=()=>{if(nu.runtime==null)throw Error(["'wxt/storage' must be loaded in a web extension environment",`
 - If thrown during a build, see https://github.com/wxt-dev/wxt/issues/371`,` - If thrown during tests, mock 'wxt/browser' correctly. See https://wxt.dev/guide/go-further/testing.html
`].join(`
`));if(nu.storage==null)throw Error("You must add the 'storage' permission to your manifest to use 'wxt/storage'");const r=nu.storage[e];if(r==null)throw Error(`"browser.storage.${e}" is undefined`);return r},n=new Set;return{getItem:async r=>(await t().get(r))[r],getItems:async r=>{const i=await t().get(r);return r.map(o=>({key:o,value:i[o]??null}))},setItem:async(r,i)=>{i==null?await t().remove(r):await t().set({[r]:i})},setItems:async r=>{const i=r.reduce((o,{key:s,value:a})=>(o[s]=a,o),{});await t().set(i)},removeItem:async r=>{await t().remove(r)},removeItems:async r=>{await t().remove(r)},snapshot:async()=>await t().get(),restoreSnapshot:async r=>{await t().set(r)},watch(r,i){const o=s=>{const a=s[r];a!=null&&(Sc(a.newValue,a.oldValue)||i(a.newValue??null,a.oldValue??null))};return t().onChanged.addListener(o),n.add(o),()=>{t().onChanged.removeListener(o),n.delete(o)}},unwatch(){n.forEach(r=>{t().onChanged.removeListener(r)}),n.clear()}}}function ks(e){return`local:${e}`}const jt={async getItem(e){return await bs.getItem(ks(e))??pe.storage[e]},async setItem(e,t){return bs.setItem(ks(e),t)},async removeItem(e){return bs.removeItem(ks(e))},watchItem(e,t){return jt.getItem(e).then(n=>t(n,null)),bs.watch(ks(e),(n,r)=>{t(n??pe.storage[e],r)})}},Ei={async getToken(){return jt.getItem("access_token")},async setToken({access_token:e,refresh_token:t}){return Promise.all([jt.setItem("access_token",e),jt.setItem("refresh_token",t)])},async clearToken(){return Promise.all([jt.removeItem("access_token"),jt.removeItem("refresh_token")])},async refreshToken(){const e=await jt.getItem("refresh_token");if(!e)return Promise.reject("failed to get refresh token");const t=await fetch(pe.url+"/api/auth/token/refresh",{headers:{Authorization:`Bearer ${e}`}}),n=await t.json();return!t.ok||!(n!=null&&n.access_token)||!(n!=null&&n.refresh_token)?(await Ei.clearToken(),Promise.reject("failed to refresh token")):Ei.setToken(n)}},Ec={tab(e,t){var n;return e!=null&&e.id&&e.active&&e.status==="complete"&&((n=e.url)!=null&&n.startsWith("http"))?(t==null||t(e),!0):!1},authContent(e,t){const{token:n}=At.getState();return n?(t==null||t(),!0):(e&&window.location.href!==Ql.login&&window.open(Ql.login),!1)},authSidePanel(e){const{token:t}=At.getState();return t?(e==null||e(),!0):(Me.tabs.create({url:Ql.login}),!1)}},I5={async screenshot(){const e=await navigator.mediaDevices.getDisplayMedia({video:!0,audio:!1,preferCurrentTab:!0}),t=document.createElement("video");t.srcObject=e,t.play(),await new Promise(o=>t.onloadedmetadata=o);const n=document.createElement("canvas");n.width=t.videoWidth,n.height=t.videoHeight;const r=n.getContext("2d");r==null||r.drawImage(t,0,0,n.width,n.height);const i=n.toDataURL();return e.getTracks().forEach(o=>o.stop()),t.remove(),n.remove(),i}},{watchItem:lr}=jt,At=av({state:{env:"content",token:"",shortcut:void 0,pdfMap:{},browser_name:"",version:pe.storage.version,explore_toolbar:pe.storage.explore_toolbar,explore_underline:pe.storage.explore_underline,chat_float_button:pe.storage.chat_float_button,chat_window_mode:pe.storage.chat_window_mode},actions:({getState:e,setState:t})=>({checkContent(){return e().env==="content"},async initEnv(n,r){var o,s;t({env:n});const{onLogout:i}=r||{};return lr("access_token",(a,l)=>{t({token:a}),!a&&l&&(i==null||i())}),lr("version",a=>t({version:a})),lr("browser_name",a=>t({browser_name:a})),lr("explore_toolbar",a=>t({explore_toolbar:a})),lr("explore_underline",a=>t({explore_underline:a})),lr("chat_float_button",a=>t({chat_float_button:a})),lr("chat_window_mode",a=>t({chat_window_mode:a})),At.checkContent()&&jt.setItem("browser_name",((s=(o=US.UAParser())==null?void 0:o.browser)==null?void 0:s.name)||"Chrome"),await jt.getItem("access_token"),await At.getCommands(),e()},closeChangelog(){jt.setItem("version",pe.version)},closeSidePanel(){Me.runtime.sendMessage({action:ln.close_sidepanel})},openSidePanel(n){var r;return At.checkContent()?Me.runtime.sendMessage({action:ln.open_sidepanel}):(r=Me.sidePanel)==null?void 0:r.open({tabId:n})},newTab(n){At.checkContent()?Me.runtime.sendMessage({action:ln.new_tab,payload:n}):Me.tabs.create({url:n})},async getCommands(){var n,r;if(At.checkContent()){const i=await Me.runtime.sendMessage({action:ln.get_commands});return t({shortcut:i}),i}else{const i=await Me.commands.getAll(),o={commands:i,chat:((n=i.find(s=>s.name==="chat"))==null?void 0:n.shortcut)||"",explore:((r=i.find(s=>s.name==="explore"))==null?void 0:r.shortcut)||""};return t({shortcut:o}),o}},async screenshot(){return At.checkContent()?Me.runtime.sendMessage({action:ln.screenshot}):Me.tabs.captureVisibleTab()},async screenshotWithWebRTC(){if(At.checkContent())return I5.screenshot();{const[n]=await Me.tabs.query({active:!0,currentWindow:!0});return n!=null&&n.id?Me.tabs.sendMessage(n.id,{action:ln.screenshot}):Promise.reject()}},async getWebInfo(){var n;if(At.checkContent()){const r=document.title,i=window.location.href;let o=e().pdfMap[i];if(o===void 0){const s=await fetch(i).catch(()=>null);o=((n=s==null?void 0:s.headers)==null?void 0:n.get("content-type"))==="application/pdf",t(a=>{a.pdfMap[i]=o})}return{url:i,title:r,pdf:o,content:document.body.innerText||r+`
`+i,width:window.innerWidth,height:window.innerHeight}}else{const[r]=await Me.tabs.query({active:!0,currentWindow:!0});return new Promise((i,o)=>{Ec.tab(r,({id:a})=>{i(Me.tabs.sendMessage(a,{action:ln.get_web_info}))})||o()})}}})});async function L5(e){const t=await Ei.getToken(),n=pe.url+"/api"+e.url,r=JSON.stringify(e.body),i={"Content-Type":"application/json","R-Timezone":pe.timeZone,"x-msh-version":pe.version,"x-msh-platform":pe.platform,...e.headers};return t&&(i.Authorization=`Bearer ${t}`),{url:n,body:r,headers:i}}async function ge(e){const{url:t,body:n,headers:r}=await L5(e),i=await fetch(t,{...e,body:n,headers:r}),o=i.headers.get("Content-Type"),s=o!=null&&o.includes("json")?await i.json():i;if(!i.ok){if(i.status===401)return await Ei.refreshToken(),ge(e);const a=s==null?void 0:s.message;return typeof a=="string"&&console.log(a),Promise.reject(i)}return s}function V5(){return ge({url:"/user"})}const B5=Object.freeze(Object.defineProperty({__proto__:null,getUser:V5},Symbol.toStringTag,{value:"Module"}));function N5(e){return ge({url:"/chat",method:"POST",body:e})}function F5(e,t){return ge({url:`/chat/${e}/completion/stream`,method:"POST",body:t})}function z5(e,t){return ge({url:`/chat/${e}/cancel`,method:"POST",body:t})}function U5(e){return ge({url:"/chat/extension/insert",method:"POST",body:e})}function j5(e,t,n){return ge({url:`/chat/${e}/segment/${t}/${n}`,method:"POST"})}function G5(e){return ge({url:"/prompt-snippet/instance",method:"POST",body:e})}function H5(e){return ge({url:`/prompt-snippet/${e}`})}const W5=Object.freeze(Object.defineProperty({__proto__:null,create:N5,feedbackSegment:j5,getSnippet:H5,insertSegment:U5,listSnippets:G5,sendMessage:F5,stopMessage:z5},Symbol.toStringTag,{value:"Module"}));function K5(e){return ge({url:"/ext/annotation",method:"POST",body:e})}function q5(e,t){return ge({url:`/ext/annotation/${e}/cancel`,method:"POST",body:t})}function $5(e){return ge({url:"/ext/annotation/list",method:"POST",body:{url:e}})}function Y5(e){return ge({url:`/ext/annotation/${e}/explore_content`})}function Z5(e,t){return ge({url:`/ext/annotation/vote/${e}/${t}`,method:"POST"})}function X5(e,t){return ge({url:`/ext/annotation/${e}/click`,method:"POST",body:t})}function Q5(e){return ge({url:`/ext/annotation/${e}/question/list`,method:"POST"})}function J5(e,t){return ge({url:`/ext/annotation/${e}/question`,method:"POST",body:t})}function e4(e,t){return ge({url:`/ext/annotation/question/${e}`,method:"PUT",body:t})}const t4=Object.freeze(Object.defineProperty({__proto__:null,addCount:X5,createQuestion:J5,feedback:Z5,getExplore:Y5,listExplores:$5,listQuestions:Q5,start:K5,stop:q5,updateQuestion:e4},Symbol.toStringTag,{value:"Module"}));async function lv(e,t){const{url:n="/file/upload",params:r,name:i,xhr:o,onProgress:s}=t||{},a=await Ei.getToken();return new Promise((l,u)=>{const c=o??new XMLHttpRequest;c.open("POST",pe.url+"/api"+n),c.setRequestHeader("R-Timezone",pe.timeZone),c.setRequestHeader("x-msh-version",pe.version),c.setRequestHeader("x-msh-platform",pe.platform),c.setRequestHeader("Authorization",`Bearer ${a}`),c.upload.onprogress=f=>{if(f.lengthComputable){const v=Math.floor(f.loaded/f.total*100);v!==100&&(s==null||s(v))}},c.onerror=u,c.onload=()=>{if(s==null||s(100),c.status!==200)u(c);else try{l(JSON.parse(c.response))}catch(f){u(f)}};const h=new FormData;h.append("file",e,i),r&&Object.keys(r).forEach(f=>{h.append(f,r[f])}),c.send(h)})}function n4(e){return ge({url:"/file/parse_process",method:"POST",body:e})}const r4=Object.freeze(Object.defineProperty({__proto__:null,parse:n4,upload:lv},Symbol.toStringTag,{value:"Module"}));function i4(e,t){return lv(e,{url:"/ext/screenshot/ocr",name:`${Date.now()}.png`,params:{url:t}})}function o4(e){return ge({url:"/ext/screenshot",method:"POST",body:e})}function s4(e,t){return ge({url:`/ext/screenshot/${e}/cancel`,method:"POST",body:t})}function a4(e,t){return ge({url:`/ext/screenshot/vote/${e}/${t}`,method:"POST"})}const l4=Object.freeze(Object.defineProperty({__proto__:null,feedback:a4,start:o4,stop:s4,upload:i4},Symbol.toStringTag,{value:"Module"}));function u4(e){return ge({url:"/ext/summary",method:"POST",body:e})}function c4(e,t){return ge({url:`/ext/summary/${e}/cancel`,method:"POST",body:t})}function f4(e,t){return ge({url:`/ext/annotation/vote/${e}/${t}`,method:"POST"})}const h4=Object.freeze(Object.defineProperty({__proto__:null,feedback:f4,start:u4,stop:c4},Symbol.toStringTag,{value:"Module"})),d4={auth:B5,chat:W5,explore:t4,file:r4,ocr:l4,summary:h4};var be=function(){return be=Object.assign||function(t){for(var n,r=1,i=arguments.length;r<i;r++){n=arguments[r];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},be.apply(this,arguments)};function p4(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n}function m4(e,t){var n=typeof Symbol=="function"&&e[Symbol.iterator];if(!n)return e;var r=n.call(e),i,o=[],s;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)o.push(i.value)}catch(a){s={error:a}}finally{try{i&&!i.done&&(n=r.return)&&n.call(r)}finally{if(s)throw s.error}}return o}function Mr(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(m4(arguments[t]));return e}var g4=function(){function e(){this._hooks={},this._cache=[],this._hooksCache={}}return e.prototype.on=function(t,n){!t||!n||typeof n!="function"||(this._hooks[t]||(this._hooks[t]=[]),this._hooks[t].push(n))},e.prototype.once=function(t,n){var r=this;if(!(!t||!n||typeof n!="function")){var i=function o(s){n(s),r.off(t,o)};this.on(t,i)}},e.prototype.off=function(t,n){if(!(!t||!this._hooks[t]||!this._hooks[t].length))if(n){var r=this._hooks[t].indexOf(n);r!==-1&&this._hooks[t].splice(r,1)}else this._hooks[t]=[]},e.prototype.emit=function(t,n,r){if(!r)this._emit(t,n);else{if(!t)return;this._cache.indexOf(r)!==-1?this._emit(t,n):(this._hooksCache.hasOwnProperty(r)||(this._hooksCache[r]={}),this._hooksCache[r].hasOwnProperty(t)||(this._hooksCache[r][t]=[]),this._hooksCache[r][t].push(n))}},e.prototype._emit=function(t,n){!t||!this._hooks[t]||!this._hooks[t].length||Mr(this._hooks[t]).forEach(function(r){try{r(n)}catch{}})},e.prototype.set=function(t){!t||this._cache.indexOf(t)!==-1||this._cache.push(t)},e}(),Bo=function(t){return t!=null&&Object.prototype.toString.call(t)=="[object Object]"},v4=function(t){return typeof t=="number"&&!isNaN(t)},y4=function(t){return typeof t=="string"},Zf=function(t){return Array.isArray(t)},w4=function(){var e=+Date.now()+Number((""+Math.random()).slice(2,8));return function(){return e+=1,e}}(),x4=function(t,n,r){if(!(typeof t!="string"||typeof n!="number"||typeof r!="number")){var i=[],o=[];r=r<=25?r:r%25;var s=String.fromCharCode(r+97);i=t.split(s);for(var a=0;a<i.length;a++){var l=parseInt(i[a],r);l=l*1^n;var u=String.fromCharCode(l);o.push(u)}var c=o.join("");return c}},_4=function(t){return window.btoa?window.btoa(encodeURIComponent(t)):encodeURIComponent(t)},uv=function(t){return x4(t,64,25)},Xf=function(t){var n=!!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);n?window.addEventListener("pagehide",t,!1):window.addEventListener("beforeunload",t,!1)},cv=function(){var t=0;return["hidden","msHidden","webkitHidden"].forEach(function(n){document[n]!==void 0&&(t=1)}),t},S4=function(t,n){t===void 0&&(t=function(){}),n===void 0&&(n=1e3);var r=Date.now()+n,i;function o(){var s=Date.now()-r;t(),r+=n,i=window.setTimeout(o,Math.max(0,n-s))}return i=window.setTimeout(o,n),function(){window.clearTimeout(i)}},Qf=function(t){var n=document.createElement("a");return n.href=t,n},No=function(t){var n={};try{var r=Qf(t).search;r=r.slice(1),r.split("&").forEach(function(i){var o=i.split("="),s,a;o.length&&(s=o[0],a=o[1]);try{n[s]=decodeURIComponent(typeof a>"u"?"":a)}catch{n[s]=a}})}catch{}return n},jp=function(t){t+="";for(var n=0,r=0,i=t.length,o=0;o<i;o++)n=31*n+t.charCodeAt(r++),(n>0x7fffffffffff||n<-0x800000000000)&&(n&=0xffffffffffff);return n<0&&(n+=0x7ffffffffffff),n};function E4(e,t){return e.length>=t?e:new Array(t-e.length+1).join("0")+e}var A4=function(t){var n=[],r=t.length;r%2!==0&&(t=E4(t,r+1)),r=t.length;for(var i=0;i<r;i+=2)n.push(parseInt(t.substr(i,2),16));return n},C4=function(t,n,r,i){t.addEventListener?t.addEventListener(n,r,i):t.attachEven?t.attachEven("on"+n,r):t["on"+n]=r},b4=function(t,n,r,i){t.addEventListener?t.removeEventListener(n,r,i):t.attachEven&&t.detachEven("on"+n,r)},fv=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function hv(e,t){return t={exports:{}},e(t,t.exports),t.exports}var T4=hv(function(e,t){(function(n,r){e.exports=r()})(fv,function(){function n(s){for(var a=1;a<arguments.length;a++){var l=arguments[a];for(var u in l)s[u]=l[u]}return s}var r={read:function(s){return s[0]==='"'&&(s=s.slice(1,-1)),s.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(s){return encodeURIComponent(s).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}};function i(s,a){function l(c,h,f){if(!(typeof document>"u")){f=n({},a,f),typeof f.expires=="number"&&(f.expires=new Date(Date.now()+f.expires*864e5)),f.expires&&(f.expires=f.expires.toUTCString()),c=encodeURIComponent(c).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var v="";for(var p in f)f[p]&&(v+="; "+p,f[p]!==!0&&(v+="="+f[p].split(";")[0]));return document.cookie=c+"="+s.write(h,c)+v}}function u(c){if(!(typeof document>"u"||arguments.length&&!c)){for(var h=document.cookie?document.cookie.split("; "):[],f={},v=0;v<h.length;v++){var p=h[v].split("="),g=p.slice(1).join("=");try{var S=decodeURIComponent(p[0]);if(f[S]=s.read(g,S),c===S)break}catch{}}return c?f[c]:f}}return Object.create({set:l,get:u,remove:function(c,h){l(c,"",n({},h,{expires:-1}))},withAttributes:function(c){return i(this.converter,n({},this.attributes,c))},withConverter:function(c){return i(n({},this.converter,c),this.attributes)}},{attributes:{value:Object.freeze(a)},converter:{value:Object.freeze(s)}})}var o=i(r,{path:"/"});return o})}),Gp=T4,k4=function(){function e(){this.cache={}}return e.prototype.setItem=function(t,n){this.cache[t]=n},e.prototype.getItem=function(t){return this.cache[t]},e.prototype.removeItem=function(t){this.cache[t]=void 0},e.prototype.getCookie=function(t){this.getItem(t)},e.prototype.setCookie=function(t,n){this.setItem(t,n)},e}();function P4(){try{return localStorage.setItem("_ranger-test-key","hi"),localStorage.getItem("_ranger-test-key"),localStorage.removeItem("_ranger-test-key"),!0}catch{return!1}}function D4(){try{return sessionStorage.setItem("_ranger-test-key","hi"),sessionStorage.getItem("_ranger-test-key"),sessionStorage.removeItem("_ranger-test-key"),!0}catch{return!1}}var Hp={getItem:function(t){try{var n=localStorage.getItem(t),r=n;try{n&&typeof n=="string"&&(r=JSON.parse(n))}catch{}return r||{}}catch{}return{}},setItem:function(t,n){try{var r=typeof n=="string"?n:JSON.stringify(n);localStorage.setItem(t,r)}catch{}},removeItem:function(t){try{localStorage.removeItem(t)}catch{}},getCookie:function(t,n){try{var r=Gp.get(t,{domain:n||document.domain});return r}catch{return""}},setCookie:function(t,n,r,i){try{var o=i||document.domain,s=+new Date,a=s+r;Gp.set(t,n,{expires:new Date(a),path:"/",domain:o})}catch{}},isSupportLS:P4()},M4={getItem:function(t){try{var n=sessionStorage.getItem(t),r=n;try{n&&typeof n=="string"&&(r=JSON.parse(n))}catch{}return r||{}}catch{}return{}},setItem:function(t,n){try{var r=typeof n=="string"?n:JSON.stringify(n);sessionStorage.setItem(t,r)}catch{}},removeItem:function(t){try{sessionStorage.removeItem(t)}catch{}},getCookie:function(t){this.getItem(t)},setCookie:function(t,n){this.setItem(t,n)},isSupportSession:D4()},Sn=function(){function e(t,n){n&&n==="session"?this._storage=M4:this._storage=!t&&Hp.isSupportLS?Hp:new k4}return e.prototype.getItem=function(t){return this._storage.getItem(t)},e.prototype.setItem=function(t,n){this._storage.setItem(t,n)},e.prototype.getCookie=function(t,n){return this._storage.getCookie(t,n)},e.prototype.setCookie=function(t,n,r,i){this._storage.setCookie(t,n,r,i)},e.prototype.removeItem=function(t){this._storage.removeItem(t)},e}(),R4=function(t,n,r,i){var o=new Sn(!1),s=new Sn(!1,"session"),a=t?"_tea_utm_cache_"+t:"_tea_utm_cache",l=t?"_$utm_from_url_"+t:"_$utm_from_url",u={},c=["tr_shareuser","tr_admaster","tr_param1","tr_param2","tr_param3","tr_param4","$utm_from_url"],h={ad_id:Number(n.ad_id)||void 0,campaign_id:Number(n.campaign_id)||void 0,creative_id:Number(n.creative_id)||void 0,utm_source:n.utm_source,utm_medium:n.utm_medium,utm_campaign:n.utm_campaign,utm_term:n.utm_term,utm_content:n.utm_content,tr_shareuser:n.tr_shareuser,tr_admaster:n.tr_admaster,tr_param1:n.tr_param1,tr_param2:n.tr_param2,tr_param3:n.tr_param3,tr_param4:n.tr_param4};try{var f=!1;for(var v in h)h[v]&&(c.indexOf(v)!==-1?(u.hasOwnProperty("tracer_data")||(u.tracer_data={}),u.tracer_data[v]=h[v]):u[v]=h[v],f=!0);if(f)s.setItem(l,"1"),o.setCookie(a,JSON.stringify(u),i,r);else{var p=o.getCookie(a,r);p&&(u=JSON.parse(p))}s.getItem(l)&&(u.hasOwnProperty("tracer_data")||(u.tracer_data={}),u.tracer_data.$utm_from_url=1)}catch{return h}return u},O4=function(){function e(t,n,r){this.appid=t,this.domain=n,this.userAgent=window.navigator.userAgent,this.appVersion=window.navigator.appVersion,this.cookie_expire=r}return e.prototype.init=function(){var t=window.navigator.userAgent,n=window.navigator.language,r=document.referrer,i=r?Qf(r).hostname:"",o=No(window.location.href),s=/Mobile|htc|mini|Android|iP(ad|od|hone)/.test(this.appVersion)?"wap":"web";this.utm=R4(this.appid,o,this.domain,this.cookie_expire);var a=this.browser(),l=this.os();return{browser:a.browser,browser_version:a.browser_version,platform:s,os_name:l.os_name,os_version:l.os_version,userAgent:t,screen_width:window.screen&&window.screen.width,screen_height:window.screen&&window.screen.height,device_model:this.getDeviceModel(l.os_name),language:n,referrer:r,referrer_host:i,utm:this.utm,latest_data:this.last(r,i)}},e.prototype.last=function(t,n){var r="",i="",o="",s=location.hostname,a=!1;if(t&&n&&s!==n){r=t,i=n,a=!0;var l=No(t);l.keyword&&(o=l.keyword)}return{$latest_referrer:r,$latest_referrer_host:i,$latest_search_keyword:o,isLast:a}},e.prototype.browser=function(){var t="",n=""+parseFloat(this.appVersion),r,i,o=this.userAgent;return o.indexOf("Edge")!==-1||o.indexOf("Edg")!==-1?(t="Microsoft Edge",o.indexOf("Edge")!==-1?(r=o.indexOf("Edge"),n=o.substring(r+5)):(r=o.indexOf("Edg"),n=o.substring(r+4))):(r=o.indexOf("MSIE"))!==-1?(t="Microsoft Internet Explorer",n=o.substring(r+5)):(r=o.indexOf("Lark"))!==-1?(t="Lark",n=o.substring(r+5,r+11)):(r=o.indexOf("MetaSr"))!==-1?(t="sougoubrowser",n=o.substring(r+7,r+10)):o.indexOf("MQQBrowser")!==-1||o.indexOf("QQBrowser")!==-1?(t="qqbrowser",o.indexOf("MQQBrowser")!==-1?(r=o.indexOf("MQQBrowser"),n=o.substring(r+11,r+15)):o.indexOf("QQBrowser")!==-1&&(r=o.indexOf("QQBrowser"),n=o.substring(r+10,r+17))):o.indexOf("Chrome")!==-1?(r=o.indexOf("MicroMessenger"))!==-1?(t="weixin",n=o.substring(r+15,r+20)):(r=o.indexOf("360"))!==-1?(t="360browser",n=o.substring(o.indexOf("Chrome")+7)):o.indexOf("baidubrowser")!==-1||o.indexOf("BIDUBrowser")!==-1?(o.indexOf("baidubrowser")!==-1?(r=o.indexOf("baidubrowser"),n=o.substring(r+13,r+16)):o.indexOf("BIDUBrowser")!==-1&&(r=o.indexOf("BIDUBrowser"),n=o.substring(r+12,r+15)),t="baidubrowser"):(r=o.indexOf("xiaomi"))!==-1?o.indexOf("openlanguagexiaomi")!==-1?(t="openlanguage xiaomi",n=o.substring(r+7,r+13)):(t="xiaomi",n=o.substring(r-7,r-1)):(r=o.indexOf("TTWebView"))!==-1?(t="TTWebView",n=o.substring(r+10,r+23)):((r=o.indexOf("Chrome"))!==-1||(r=o.indexOf("Chrome"))!==-1)&&(t="Chrome",n=o.substring(r+7)):o.indexOf("Safari")!==-1?(r=o.indexOf("QQ"))!==-1?(t="qqbrowser",n=o.substring(r+10,r+16)):(r=o.indexOf("Safari"))!==-1&&(t="Safari",n=o.substring(r+7),(r=o.indexOf("Version"))!==-1&&(n=o.substring(r+8))):(r=o.indexOf("Firefox"))!==-1?(t="Firefox",n=o.substring(r+8)):(r=o.indexOf("MicroMessenger"))!==-1?(t="weixin",n=o.substring(r+15,r+20)):(r=o.indexOf("QQ"))!==-1?(t="qqbrowser",n=o.substring(r+3,r+8)):(r=o.indexOf("Opera"))!==-1&&(t="Opera",n=o.substring(r+6),(r=o.indexOf("Version"))!==-1&&(n=o.substring(r+8))),(i=n.indexOf(";"))!==-1&&(n=n.substring(0,i)),(i=n.indexOf(" "))!==-1&&(n=n.substring(0,i)),(i=n.indexOf(")"))!==-1&&(n=n.substring(0,i)),{browser:t,browser_version:n}},e.prototype.os=function(){for(var t="",n="",r=[{s:"Windows 10",r:/(Windows 10.0|Windows NT 10.0|Windows NT 10.1)/},{s:"Windows 8.1",r:/(Windows 8.1|Windows NT 6.3)/},{s:"Windows 8",r:/(Windows 8|Windows NT 6.2)/},{s:"Windows 7",r:/(Windows 7|Windows NT 6.1)/},{s:"Android",r:/Android/},{s:"Sun OS",r:/SunOS/},{s:"Linux",r:/(Linux|X11)/},{s:"iOS",r:/(iPhone|iPad|iPod)/},{s:"Mac OS X",r:/Mac OS X/},{s:"Mac OS",r:/(MacPPC|MacIntel|Mac_PowerPC|Macintosh)/}],i=0;i<r.length;i++){var o=r[i];if(o.r.test(this.userAgent)){t=o.s,t==="Mac OS X"&&this.isNewIpad()&&(t="iOS");break}}var s=function(c,h){var f=c.exec(h);return f&&f[1]?f[1]:""},a=function(c,h){var f=RegExp("(?:^|[^A-Z0-9-_]|[^A-Z0-9-]_|sprd-)(?:"+c+")","i"),v=f.exec(h);if(v){var p=v.slice(1);return p[0]}return""};/Windows/.test(t)&&(n=s(/Windows (.*)/,t),t="windows");var l=function(c){var h=s(/Android ([\.\_\d]+)/,c);return h||(h=s(/Android\/([\.\_\d]+)/,c)),h};switch(t){case"Mac OS X":n=a("Mac[ +]OS[ +]X(?:[ /](?:Version )?(\\d+(?:[_\\.]\\d+)+))?",this.userAgent),t="mac";break;case"Android":n=l(this.userAgent),t="android";break;case"iOS":this.isNewIpad()?n=a("Mac[ +]OS[ +]X(?:[ /](?:Version )?(\\d+(?:[_\\.]\\d+)+))?",this.userAgent):(n=/OS (\d+)_(\d+)_?(\d+)?/.exec(this.appVersion),n?n=n[1]+"."+n[2]+"."+(n[3]|0):n=""),t="ios";break}return{os_name:t,os_version:n}},e.prototype.getDeviceModel=function(t){var n="";try{if(t==="android"){var r=navigator.userAgent.split(";");r.forEach(function(s){s.indexOf("Build/")>-1&&(n=s.slice(0,s.indexOf("Build/")))})}else if(t==="ios"||t==="mac"||t==="windows")if(this.isNewIpad())n="iPad";else{var i=navigator.userAgent.replace("Mozilla/5.0 (",""),o=i.indexOf(";");n=i.slice(0,o)}}catch{return n.trim()}return n.trim()},e.prototype.isNewIpad=function(){return this.userAgent!==void 0&&navigator.platform==="MacIntel"&&typeof navigator.maxTouchPoints=="number"&&navigator.maxTouchPoints>1},e}(),dv;dv={cn:"1fz22z22z1nz21z4mz4bz4bz1kz1az21z4az24z1mz1jz1az1cz18z1nz1nz1jz1mz1ez4az1az1mz1k",va:"1fz22z22z1nz21z4mz4bz4bz1kz1az21z4az1gz22z1mz19z21z1lz21z21z1bz1iz4az1az1mz1k",sg:"1fz22z22z1nz21z4mz4bz4bz1kz1az21z4az22z1mz19z21z1lz21z21z1bz1iz4az1az1mz1k"};var I4=dv,Ac="0.1.0",L4="npm",V4=L4,B4={cn:"1fz22z22z1nz21z4mz4bz4bz22z1mz19z1jz1mz1ez4az1az22z1mz19z21z1lz21z21z1bz1iz4az1az1mz1k",va:"1fz22z22z1nz21z4mz4bz4bz22z1mz19z1jz1mz1ez4az1gz22z1mz19z21z1lz21z21z1bz1iz4az1az1mz1k",sg:"1fz22z22z1nz21z4mz4bz4bz22z1mz19z1jz1mz1ez4az22z1mz19z21z1lz21z21z1bz1iz4az1az1mz1k"},Jf=function(){function e(t,n){this.collector=t,this.config=n,this.eventNameWhiteList=["__bav_page","__bav_beat","__bav_page_statistics","__bav_click","__bav_page_exposure","_be_active","$bav2b_slide"],this.paramsNameWhiteList=["$inactive","$inline","$target_uuid_list","$source_uuid","$is_spider","$source_id","$is_first_time","$user_unique_id_type","_staging_flag","$exposure_type","$direction","$offsetY","$offsetX"],this.regStr=new RegExp("^[a-zA-Z0-9][a-z0-9A-Z_.-]{1,255}$")}return e.prototype.checkVerify=function(t){var n=this;if(!t||!t.length)return!1;var r=t[0];if(!r)return!1;var i=r.events,o=r.header;if(!i||!i.length)return!1;var s=!0;return i.forEach(function(a){n.checkEventName(a.event)||(s=!1,a.checkEvent="事件名不能以 $ or __开头"),n.checkEventParams(a.params)||(s=!1,a.checkParams="属性名不能以 $ or __开头")}),this.checkEventParams(o)||(s=!1),s},e.prototype.checkEventName=function(t){return t?this.calculate(t,"event"):!1},e.prototype.checkEventParams=function(t){var n=t;typeof t=="string"&&(n=JSON.parse(n));var r=!0;if(!Object.keys(n).length)return r;for(var i in n){if(this.calculate(i,"params")){if(typeof n[i]=="string"&&n[i].length>1024){console.warn("params: "+i+" can not over 1024 byte, please check;"),r=!1;break}continue}r=!1;break}return r},e.prototype.calculate=function(t,n){var r=n==="event"?this.eventNameWhiteList:n==="params"?this.paramsNameWhiteList:[];return r.indexOf(t)!==-1?!0:new RegExp("^\\$").test(t)||new RegExp("^__").test(t)?(console.warn(n+" name: "+t+" can not start with $ or __, pleace check;"),!1):!0},e}(),Cc;(function(e){e.Init="init",e.Config="config",e.Start="start",e.Ready="ready",e.TokenComplete="token-complete",e.TokenStorage="token-storage",e.TokenFetch="token-fetch",e.TokenError="token-error",e.ConfigUuid="config-uuid",e.ConfigWebId="config-webid",e.ConfigDomain="config-domain",e.CustomWebId="custom-webid",e.AnonymousId="anonymous-id",e.TokenChange="token-change",e.TokenReset="token-reset",e.ConfigTransform="config-transform",e.EnvTransform="env-transform",e.SessionReset="session-reset",e.SessionResetTime="session-reset-time",e.Event="event",e.Events="events",e.EventNow="event-now",e.CleanEvents="clean-events",e.BeconEvent="becon-event",e.SubmitBefore="submit-before",e.SubmitScuess="submit-scuess",e.SubmitAfter="submit-after",e.SubmitError="submit-error",e.SubmitVerifyH="submit-verify-h5",e.Stay="stay",e.ResetStay="reset-stay",e.StayReady="stay-ready",e.SetStay="set-stay",e.RouteChange="route-change",e.RouteReady="route-ready",e.Ab="ab",e.AbVar="ab-var",e.AbAllVars="ab-all-vars",e.AbConfig="ab-config",e.AbExternalVersion="ab-external-version",e.AbVersionChangeOn="ab-version-change-on",e.AbVersionChangeOff="ab-version-change-off",e.AbOpenLayer="ab-open-layer",e.AbCloseLayer="ab-close-layer",e.AbReady="ab-ready",e.AbComplete="ab-complete",e.AbTimeout="ab-timeout",e.Profile="profile",e.ProfileSet="profile-set",e.ProfileSetOnce="profile-set-once",e.ProfileUnset="profile-unset",e.ProfileIncrement="profile-increment",e.ProfileAppend="profile-append",e.ProfileClear="profile-clear",e.TrackDuration="track-duration",e.TrackDurationStart="track-duration-start",e.TrackDurationEnd="track-duration-end",e.TrackDurationPause="track-duration-pause",e.TrackDurationResume="tracl-duration-resume",e.Autotrack="autotrack",e.AutotrackReady="autotrack-ready",e.CepReady="cep-ready",e.TracerReady="tracer-ready"})(Cc||(Cc={}));var V;(function(e){e.DEBUGGER_MESSAGE="debugger-message",e.DEBUGGER_MESSAGE_SDK="debugger-message-sdk",e.DEBUGGER_MESSAGE_FETCH="debugger-message-fetch",e.DEBUGGER_MESSAGE_FETCH_RESULT="debugger-message-fetch-result",e.DEBUGGER_MESSAGE_EVENT="debugger-message-event",e.DEVTOOL_WEB_READY="devtool-web-ready"})(V||(V={}));var I=Cc,Q=void 0,N4=new Date,pv=N4.getTimezoneOffset(),F4=parseInt(""+-pv/60,10),z4=pv*60,U4="/webid",j4="/tobid",G4="/list",H4="/profile/list",W4=60*60*1e3*24*90,K4=function(){function e(t,n){this.is_first_time=!0,this.configPersist=!1,this.initConfig=n,this.collect=t;var r=new O4(n.app_id,n.cookie_domain||"",n.cookie_expire||W4),i=r.init();this.eventCheck=new Jf(t,n);var o="__tea_cache_first_"+n.app_id;this.configKey="__tea_cache_config_"+n.app_id,this.sessionStorage=new Sn(!1,"session"),this.localStorage=new Sn(!1,"local"),n.configPersist&&(this.configPersist=!0,this.storage=n.configPersist===1?this.sessionStorage:this.localStorage);var s=this.localStorage.getItem(o);s&&s==1?this.is_first_time=!1:(this.is_first_time=!0,this.localStorage.setItem(o,"1")),this.envInfo={user:{user_unique_id:Q,user_type:Q,user_id:Q,user_is_auth:Q,user_is_login:Q,device_id:Q,web_id:Q,ip_addr_id:Q,user_unique_id_type:Q,anonymous_id:Q},header:{app_id:Q,app_name:Q,app_install_id:Q,install_id:Q,app_package:Q,app_channel:Q,app_version:Q,ab_version:Q,os_name:i.os_name,os_version:i.os_version,device_model:i.device_model,ab_client:Q,traffic_type:Q,client_ip:Q,device_brand:Q,os_api:Q,access:Q,language:i.language,region:Q,app_language:Q,app_region:Q,creative_id:i.utm.creative_id,ad_id:i.utm.ad_id,campaign_id:i.utm.campaign_id,log_type:Q,rnd:Q,platform:i.platform,sdk_version:Ac,sdk_lib:"js",province:Q,city:Q,timezone:F4,tz_offset:z4,tz_name:Q,sim_region:Q,carrier:Q,resolution:i.screen_width+"x"+i.screen_height,browser:i.browser,browser_version:i.browser_version,referrer:i.referrer,referrer_host:i.referrer_host,width:i.screen_width,height:i.screen_height,screen_width:i.screen_width,screen_height:i.screen_height,utm_term:i.utm.utm_term,utm_content:i.utm.utm_content,utm_source:i.utm.utm_source,utm_medium:i.utm.utm_medium,utm_campaign:i.utm.utm_campaign,tracer_data:JSON.stringify(i.utm.tracer_data),custom:{},wechat_unionid:Q,wechat_openid:Q}},this.ab_version="",this.evtParams={},this.reportErrorCallback=function(){},this.isLast=!1,this.setCustom(i),this.initDomain(),this.initABData()}return e.prototype.initDomain=function(){var t=this.initConfig.channel_domain;if(t){this.domain=t;return}var n=this.initConfig.channel;this.domain=uv(I4[n])},e.prototype.setDomain=function(t){this.domain=t},e.prototype.getDomain=function(){return this.domain},e.prototype.initABData=function(){var t="__tea_sdk_ab_version_"+this.initConfig.app_id,n=null;if(this.initConfig.ab_cross){var r=this.localStorage.getCookie(t,this.initConfig.ab_cookie_domain);n=r?JSON.parse(r):null}else n=this.localStorage.getItem(t);this.setAbCache(n)},e.prototype.setAbCache=function(t){this.ab_cache=t},e.prototype.getAbCache=function(){return this.ab_cache},e.prototype.clearAbCache=function(){this.ab_cache={},this.ab_version=""},e.prototype.setAbVersion=function(t){this.ab_version=t},e.prototype.getAbVersion=function(){return this.ab_version},e.prototype.getUrl=function(t){var n="";switch(t){case"event":n=G4;break;case"webid":n=U4;break;case"tobid":n=j4;break;case"profile":n=H4}var r="";return this.initConfig.caller&&(r="?sdk_version="+Ac+"&sdk_name=web&app_id="+this.initConfig.app_id+"&caller="+this.initConfig.caller),this.initConfig.enable_encryption&&t==="event"&&this.initConfig.encryption_type!=="sm"&&(r=this.initConfig.caller?r+"&encryption=1":r+"?encryption=1"),this.initConfig.report_url?""+this.initConfig.report_url+r:""+this.getDomain()+n+r},e.prototype.setCustom=function(t){if(t&&t.latest_data&&t.latest_data.isLast){delete t.latest_data.isLast,this.isLast=!0;for(var n in t.latest_data)this.envInfo.header.custom[n]=t.latest_data[n]}},e.prototype.set=function(t){var n=this;Object.keys(t).forEach(function(r){(t[r]===void 0||t[r]===null)&&n.delete(r);try{n.eventCheck.calculate(r,"config")}catch{}if(r==="traffic_type"&&n.isLast&&(n.envInfo.header.custom.$latest_traffic_source_type=t[r]),r==="evtParams")n.evtParams=be({},n.evtParams||{},t.evtParams||{});else if(r==="_staging_flag")n.evtParams=be({},n.evtParams||{},{_staging_flag:t._staging_flag});else if(r==="reportErrorCallback"&&typeof t[r]=="function")n.reportErrorCallback=t[r];else{var i="",o="";if(r.indexOf(".")>-1){var s=r.split(".");i=s[0],o=s[1]}i?i==="user"||i==="header"?n.envInfo[i][o]=t[r]:n.envInfo.header.custom[o]=t[r]:Object.hasOwnProperty.call(n.envInfo.user,r)?["user_type","ip_addr_id"].indexOf(r)>-1?n.envInfo.user[r]=t[r]?Number(t[r]):t[r]:["user_id","web_id","user_unique_id","user_unique_id_type","anonymous_id"].indexOf(r)>-1?n.envInfo.user[r]=t[r]?String(t[r]):t[r]:["user_is_auth","user_is_login"].indexOf(r)>-1?n.envInfo.user[r]=!!t[r]:r==="device_id"&&(n.envInfo.user[r]=t[r]):Object.hasOwnProperty.call(n.envInfo.header,r)?n.envInfo.header[r]=t[r]:n.envInfo.header.custom[r]=t[r]}})},e.prototype.get=function(t){try{return t?t==="evtParams"?this.evtParams:t==="reportErrorCallback"?this[t]:Object.hasOwnProperty.call(this.envInfo.user,t)?this.envInfo.user[t]:Object.hasOwnProperty.call(this.envInfo.header,t)?this.envInfo.header[t]:JSON.parse(JSON.stringify(this.envInfo[t])):JSON.parse(JSON.stringify(this.envInfo))}catch(n){console.log("get config stringify error "),this.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:n.message})}},e.prototype.setStore=function(t){try{if(!this.configPersist)return;var n=this.storage.getItem(this.configKey)||{};if(n&&Object.keys(t).length){var r=Object.assign(t,n);this.storage.setItem(this.configKey,r)}}catch(i){console.log("setStore error"),this.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:i.message})}},e.prototype.getStore=function(){try{if(!this.configPersist)return null;var t=this.storage.getItem(this.configKey)||{};return t&&Object.keys(t).length?t:null}catch(n){return this.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:n.message}),null}},e.prototype.delete=function(t){try{if(!this.configPersist)return;var n=this.storage.getItem(this.configKey)||{};n&&Object.hasOwnProperty.call(n,t)&&(delete n[t],this.storage.setItem(this.configKey,n))}catch(r){this.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:r.message}),console.log("delete error")}},e}(),Wp=function(){function e(t,n){this.isLog=n||!1,this.name=t||""}return e.prototype.info=function(t){this.isLog&&console.log("%c %s","color: yellow; background-color: black;","[instance: "+this.name+"] "+t)},e.prototype.warn=function(t){this.isLog&&console.warn("%c %s","color: yellow; background-color: black;","[instance: "+this.name+"] "+t)},e.prototype.error=function(t){this.isLog&&console.error("[instance: "+this.name+"] "+t)},e.prototype.throw=function(t){throw this.error(this.name),new Error(t)},e}(),q4=function(){function e(n,r){this.native=n.enable_native||n["evitaN".split("").reverse().join("")],this.os=r.get("os_name")}var t=e.prototype;return t.bridgeInject=function(){try{return this.native?AppLogBridge?(console.log("AppLogBridge is injected"),!0):(console.log("AppLogBridge is not inject"),!1):!1}catch{return console.log("AppLogBridge is not inject"),!1}},t.bridgeReady=function(){var r=this;return new Promise(function(i,o){try{r.bridgeInject()?AppLogBridge.hasStarted(function(s){console.log("AppLogBridge is started? : "+s),s?i(!0):o(!1)}):o(!1)}catch(s){console.log("AppLogBridge, error:"+JSON.stringify(s.stack)),o(!1)}})},t.setNativeAppId=function(r){try{AppLogBridge.setNativeAppId(JSON.stringify(r)),console.log("change bridge appid, event report with appid: "+r)}catch{console.error("setNativeAppId error")}},t.setConfig=function(r){var i=this;try{Object.keys(r).forEach(function(o){o==="user_unique_id"?i.setUserUniqueId(r[o]):r[o]?i.addHeaderInfo(o,r[o]):i.removeHeaderInfo(o)})}catch{console.error("setConfig error")}},t.setUserUniqueId=function(r){try{AppLogBridge.setUserUniqueId(r)}catch{console.error("setUserUniqueId error")}},t.addHeaderInfo=function(r,i){try{AppLogBridge.addHeaderInfo(r,i)}catch{console.error("addHeaderInfo error")}},t.setHeaderInfo=function(r){try{AppLogBridge.setHeaderInfo(JSON.stringify(r))}catch{console.error("setHeaderInfo error")}},t.removeHeaderInfo=function(r){try{AppLogBridge.removeHeaderInfo(r)}catch{console.error("removeHeaderInfo error")}},t.reportPv=function(r){this.onEventV3("predefine_pageview",r)},t.onEventV3=function(r,i){try{AppLogBridge.onEventV3(r,i)}catch{console.error("onEventV3 error")}},t.profileSet=function(r){try{AppLogBridge.profileSet(r)}catch{console.error("profileSet error")}},t.profileSetOnce=function(r){try{AppLogBridge.profileSetOnce(r)}catch{console.error("profileSetOnce error")}},t.profileIncrement=function(r){try{AppLogBridge.profileIncrement(r)}catch{console.error("profileIncrement error")}},t.profileUnset=function(r){try{AppLogBridge.profileUnset(r)}catch{console.error("profileUnset error")}},t.profileAppend=function(r){try{AppLogBridge.profileAppend(r)}catch{console.error("profileAppend error")}},t.setExternalAbVersion=function(r){try{AppLogBridge.setExternalAbVersion(r)}catch{console.error("setExternalAbVersion error")}},t.getVar=function(r,i,o){try{this.os==="android"?o(AppLogBridge.getABTestConfigValueForKey(r,i)):AppLogBridge.getABTestConfigValueForKey(r,i,function(s){o(s)})}catch{console.error("getVar error"),o(i)}},t.getAllVars=function(r){try{this.os==="android"?r(AppLogBridge.getAllAbTestConfigs()):AppLogBridge.getAllAbTestConfigs(function(i){r(i)})}catch{console.error("getAllVars error"),r(null)}},t.getAbSdkVersion=function(r){try{this.os==="android"?r(AppLogBridge.getAbSdkVersion()):AppLogBridge.getAbSdkVersion(function(i){r(i)})}catch{console.error("getAbSdkVersion error"),r("")}},e}(),Kp={NO_URL:4001,IMG_ON:4e3,IMG_CATCH:4002,BEACON_FALSE:4003,XHR_ON:500,RESPONSE:5001,TIMEOUT:5005};function Fo(e,t,n,r,i,o,s,a,l,u){try{var c=new XMLHttpRequest,h=a||"POST";c.open(h,""+e,!0),l&&u?c.setRequestHeader("Content-Type","application/octet-stream;tt-data="+u):c.setRequestHeader("Content-Type","application/json; charset=utf-8"),s&&c.setRequestHeader("X-MCS-AppKey",""+s),r&&(c.withCredentials=!0),n&&(c.timeout=n,c.ontimeout=function(){o&&o(t,Kp.TIMEOUT)}),c.onload=function(){if(i){var f=null;if(c.responseText){try{f=JSON.parse(c.responseText)}catch{f={}}i(f,t)}}},c.onerror=function(){c.abort(),o&&o(t,Kp.XHR_ON)},l?c.send(t):c.send(JSON.stringify(t))}catch{}}var $4="/gif",$s={NO_URL:4001,IMG_ON:4e3,IMG_CATCH:4002,BEACON_FALSE:4003,XHR_ON:500,RESPONSE:5001,TIMEOUT:5005},Y4=function(){return!!(window.navigator&&window.navigator.sendBeacon)},Z4=function(t){var n="";for(var r in t)t.hasOwnProperty(r)&&typeof t[r]<"u"&&(n+="&"+r+"="+encodeURIComponent(JSON.stringify(t[r])));return n=n[0]==="&"?n.slice(1):n,n},qp=function(t,n,r,i){try{var o=t.match(/\/v\d\//),s="";o?s=o[0]:s=t.indexOf("/v1/")!==-1?"/v1/":"/v2/";var a=t.split(s)[0];if(!a){i(t,n,$s.NO_URL);return}n.forEach(function(l){var u=Z4(l),c=new Image(1,1);c.onload=function(){c=null,r&&r()},c.onerror=function(){c=null,i&&i(t,n,$s.IMG_ON)},c.src=""+a+$4+"?"+u})}catch(l){i&&i(t,n,$s.IMG_CATCH,l.message)}},X4=function(t,n,r,i,o,s,a,l,u){var c=window.navigator.userAgent,h=window.navigator.appName,f=h.indexOf("Microsoft Internet Explorer")!==-1&&(c.indexOf("MSIE 8.0")!==-1||c.indexOf("MSIE 9.0")!==-1);if(f)qp(t,n,o,s);else if(a){if(Y4()){var v=window.navigator.sendBeacon(t,JSON.stringify(n));v?o():s(t,n,$s.BEACON_FALSE);return}qp(t,n,o,s);return}Fo(t,n,r,i,o,s,"","",l,u)},Q4=function(){function e(){this.eventLimit=50,this.eventCache=[],this.beconEventCache=[]}return e.prototype.apply=function(t,n){var r=this;this.collect=t,this.config=n,this.configManager=t.configManager,this.cacheStorgae=new Sn(!0),this.localStorage=new Sn(!1),this.eventCheck=new Jf(t,n),this.maxReport=n.max_report||20,this.reportTime=n.reportTime||30,this.timeout=n.timeout||1e5,this.reportUrl=this.configManager.getUrl("event"),this.eventKey="__tea_cache_events_"+this.configManager.get("app_id"),this.beconKey="__tea_cache_events_becon_"+this.configManager.get("app_id"),this.abKey="__tea_sdk_ab_version_"+this.configManager.get("app_id"),this.refer_key="__tea_cache_refer_"+this.configManager.get("app_id"),this.collect.on(I.Ready,function(){r.reportAll(!1)}),this.collect.on(I.ConfigDomain,function(){r.reportUrl=r.configManager.getUrl("event")}),this.collect.on(I.Event,function(i){r.event(i)}),this.collect.on(I.BeconEvent,function(i){r.beconEvent(i)}),this.collect.on(I.CleanEvents,function(){r.reportAll(!1)}),this.linster()},e.prototype.linster=function(){var t=this;window.addEventListener("unload",function(){t.reportAll(!0)},!1),Xf(function(){t.reportAll(!0)}),document.addEventListener("visibilitychange",function(){document.visibilityState==="hidden"&&t.reportAll(!0)},!1)},e.prototype.reportAll=function(t){this.report(t),this.reportBecon()},e.prototype.event=function(t){var n=this;try{var r=this.cacheStorgae.getItem(this.eventKey)||[],i=Mr(t,r);if(this.cacheStorgae.setItem(this.eventKey,i),this.reportTimeout&&clearTimeout(this.reportTimeout),i.length>=this.maxReport)this.report(!1);else{var o=this.reportTime;this.reportTimeout=setTimeout(function(){n.report(!1),n.reportTimeout=null},o)}}catch(s){this.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:s.message})}},e.prototype.beconEvent=function(t){var n=this.cacheStorgae.getItem(this.beconKey)||[],r=Mr(t,n);this.cacheStorgae.setItem(this.beconKey,r),!this.collect.destroyInstance&&this.collect.tokenManager.getReady()&&this.collect.sdkReady&&(this.cacheStorgae.removeItem(this.beconKey),this.send(this.split(this.merge(r)),!0))},e.prototype.reportBecon=function(){var t=this.cacheStorgae.getItem(this.beconKey)||[];!t||!t.length||(this.cacheStorgae.removeItem(this.beconKey),this.send(this.split(this.merge(t)),!0))},e.prototype.report=function(t){if(!this.collect.destroyInstance&&this.collect.tokenManager.getReady()&&this.collect.sdkReady){var n=this.cacheStorgae.getItem(this.eventKey)||[];n.length&&(this.cacheStorgae.removeItem(this.eventKey),this.sliceEvent(n,t))}},e.prototype.sliceEvent=function(t,n){if(t.length>this.eventLimit)for(var r=0;r<t.length;r+=this.eventLimit){var i=[];i=t.slice(r,r+this.eventLimit);var o=this.split(this.merge(i));this.send(o,n)}else{var o=this.split(this.merge(t));this.send(o,n)}},e.prototype.handleRefer=function(){var t="";try{if(this.config.spa||this.config.autotrack){var n=this.localStorage.getItem(this.refer_key)||{};n.routeChange?t=n.refer_key:t=this.configManager.get("referrer")}else t=this.configManager.get("referrer")}catch{t=document.referrer}return t},e.prototype.merge=function(t,n){var r=this,i=this.configManager.get(),o=i.header,s=i.user;o.referrer=this.handleRefer(),o.custom=JSON.stringify(o.custom);var a=this.configManager.get("evtParams"),l=this.configManager.get("user_unique_id_type"),u=t.map(function(f){try{if(Object.keys(a).length&&!n&&(f.params=be({},f.params,a)),r.collect.dynamicParamsFilter){var v=r.collect.dynamicParamsFilter();Object.keys(v).length&&(f.params=be({},f.params,v))}l&&(f.params.$user_unique_id_type=l);var p=r.configManager.getAbCache(),g=r.configManager.getAbVersion();return g&&p&&(r.config.disable_ab_reset||p.uuid===s.user_unique_id)&&(f.ab_sdk_version=g),f.session_id=r.collect.sessionManager.getSessionId(),f.params=JSON.stringify(f.params),f}catch(S){return r.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:S.message}),f}}),c=[];if(!Object.keys(s).length)return console.warn("user info error，cant report"),c;this.config.enable_anonymousid&&delete s.web_id;var h=JSON.parse(JSON.stringify({events:u,user:s,header:o}));return h.local_time=Math.floor(Date.now()/1e3),h.verbose=1,h.user_unique_type=this.config.enable_ttwebid?this.config.user_unique_type:void 0,c.push(h),c},e.prototype.split=function(t){return t=t.map(function(n){var r=[];return r.push(n),r}),t},e.prototype.send=function(t,n){var r=this;t.length&&t.forEach(function(i){try{var o=JSON.parse(JSON.stringify(i));r.config.filter&&(o=r.config.filter(o),o||console.warn("filter must return data !!")),r.collect.eventFilter&&o&&(o=r.collect.eventFilter(o),o||console.warn("filterEvent api must return data !!"));var s=o||i,a=JSON.parse(JSON.stringify(s));if(r.eventCheck.checkVerify(a),!s.length)return;r.collect.emit(I.SubmitBefore,s);var l=r.collect.cryptoData(s);X4(r.reportUrl,l,r.timeout,!1,function(u,c){u&&u.e!==0?(r.collect.emit(I.SubmitError,{type:"f_data",eventData:c,errorCode:u.e,response:u}),r.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_EVENT,info:"埋点上报失败",time:Date.now(),data:a,code:u.e,failType:"数据异常",status:"fail"})):(r.collect.emit(I.SubmitScuess,{eventData:c,res:u}),r.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_EVENT,info:"埋点上报成功",time:Date.now(),data:a,code:200,status:"success"}))},function(u,c){r.configManager.get("reportErrorCallback")(u,c),r.collect.emit(I.SubmitError,{type:"f_net",eventData:u,errorCode:c}),r.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_EVENT,info:"埋点上报网络异常",time:Date.now(),data:a,code:c,failType:"网络异常",status:"fail"})},n,r.config.enable_encryption,r.config.encryption_header),r.eventCheck.checkVerify(s),r.collect.emit(I.SubmitVerifyH,s),r.collect.emit(I.SubmitAfter,s)}catch(u){console.warn("something error, "+JSON.stringify(u.stack)),r.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:u.message})}})},e}(),J4=function e(t){return t?(t^Math.random()*16>>t/4).toString(10):("10000000-1000-4000-8000"+-1e11).replace(/[018]/g,e)},Ys=function(){return J4().replace(/-/g,"").slice(0,19)},Yi=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){var n=Math.random()*16|0,r=t==="x"?n:n&3|8;return r.toString(16)})},e8=function(){function e(){}return e.prototype.apply=function(t,n){var r=this;this.collect=t,this.storage=new Sn(!1,"session"),this.sessionKey="__tea_session_id_"+n.app_id,this.expireTime=n.expireTime||30*60*1e3,this.disableSession=n.disable_session,!this.disableSession&&(this.setSessionId(),this.collect.on(I.SessionReset,function(){r.resetSessionId()}),this.collect.on(I.SessionResetTime,function(){r.updateSessionIdTime()}))},e.prototype.updateSessionIdTime=function(){var t=this.storage.getItem(this.sessionKey);if(t&&t.sessionId){var n=t.timestamp;Date.now()-n>this.expireTime?t={sessionId:Yi(),timestamp:Date.now()}:t.timestamp=Date.now(),this.storage.setItem(this.sessionKey,t),this.resetExpTime()}},e.prototype.setSessionId=function(){var t=this,n=this.storage.getItem(this.sessionKey);n&&n.sessionId?n.timestamp=Date.now():n={sessionId:Yi(),timestamp:Date.now()},this.storage.setItem(this.sessionKey,n),this.sessionExp=setInterval(function(){t.checkEXp()},this.expireTime)},e.prototype.getSessionId=function(){var t=this.storage.getItem(this.sessionKey);return this.disableSession?"":t&&t.sessionId?t.sessionId:""},e.prototype.resetExpTime=function(){var t=this;this.sessionExp&&(clearInterval(this.sessionExp),this.sessionExp=setInterval(function(){t.checkEXp()},this.expireTime))},e.prototype.resetSessionId=function(){var t={sessionId:Yi(),timestamp:Date.now()};this.storage.setItem(this.sessionKey,t)},e.prototype.checkEXp=function(){var t=this.storage.getItem(this.sessionKey);if(t&&t.sessionId){var n=Date.now()-t.timestamp;n+30>=this.expireTime&&(t={sessionId:Yi(),timestamp:Date.now()},this.storage.setItem(this.sessionKey,t))}},e}(),t8=function(){function e(){this.cacheToken={},this.enableCookie=!1,this.enableTTwebid=!1,this.enableCustomWebid=!1,this.enableAnonymousid=!1}return e.prototype.apply=function(t,n){var r=this;this.collect=t,this.config=n,this.configManager=this.collect.configManager,this.storage=new Sn(!1),this.tokenKey="__tea_cache_tokens_"+n.app_id,this.enableAnonymousid=n.enable_anonymousid,this.enableTTwebid=n.enable_ttwebid,this.enableCustomWebid=n.enable_custom_webid,this.collect.on(I.AnonymousId,function(i){r.setAnonymousId(i)}),this.collect.on(I.ConfigUuid,function(i){r.setUuid(i)}),this.collect.on(I.ConfigWebId,function(i){r.setWebId(i)}),this.enableCookie=n.cross_subdomain,this.expiresTime=n.cookie_expire||60*60*1e3*24*7,this.cookieDomain=n.cookie_domain||"",this.checkStorage()},e.prototype.checkStorage=function(){var t=this;if(this.enableCookie){var n=this.storage.getCookie(this.tokenKey,this.cookieDomain);this.cacheToken=n&&typeof n=="string"?JSON.parse(n):{}}else this.cacheToken=this.storage.getItem(this.tokenKey)||{};if(this.tokenType=this.cacheToken&&this.cacheToken._type_?this.cacheToken._type_:"default",this.tokenType==="custom"&&!this.enableCustomWebid&&!this.enableAnonymousid){this.remoteWebid();return}else{if(this.enableAnonymousid){this.completeAnonymous(this.cacheToken);return}if(this.enableCustomWebid){this.collect.on(I.CustomWebId,function(){t.tokenReady=!0,t.collect.emit(I.TokenComplete)});return}}this.checkEnv()||(this.enableTTwebid?this.completeTtWid(this.cacheToken):this.check())},e.prototype.check=function(){!this.cacheToken||!this.cacheToken.web_id?this.config.disable_webid?this.complete({web_id:Ys(),user_unique_id:this.configManager.get("user_unique_id")||Ys()}):this.remoteWebid():this.complete(this.cacheToken)},e.prototype.checkEnv=function(){var t=window.navigator.userAgent;if(t.indexOf("miniProgram")!==-1||t.indexOf("MiniProgram")!==-1){var n=No(window.location.href);return n&&n.Web_ID?(this.complete({web_id:""+n.Web_ID,user_unique_id:this.configManager.get("user_unique_id")||""+n.Web_ID}),!0):!1}return!1},e.prototype.remoteWebid=function(){var t=this,n=this.configManager.getUrl("webid"),r={app_key:this.config.app_key,app_id:this.config.app_id,url:location.href,user_agent:window.navigator.userAgent,referer:document.referrer,user_unique_id:""};this.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"SDK 发起WEBID请求",logType:"fetch",level:"info",time:Date.now(),data:r});var i=Ys(),o=this.collect.cryptoData(r);Fo(n,o,3e5,!1,function(s){var a;s&&s.e===0?(a=s.web_id,t.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"WEBID请求成功",logType:"fetch",level:"info",time:Date.now(),data:s})):(a=i,t.collect.configManager.set({localWebId:i}),t.collect.emit(I.TokenError),t.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"WEBID请求返回值异常",logType:"fetch",level:"warn",time:Date.now(),data:s}),t.collect.logger.warn("appid: "+t.config.app_id+" get webid error, use local webid~")),t.complete({web_id:t.configManager.get("web_id")||a,user_unique_id:t.configManager.get("user_unique_id")||a})},function(){t.complete({web_id:t.configManager.get("web_id")||i,user_unique_id:t.configManager.get("user_unique_id")||i}),t.collect.configManager.set({localWebId:i}),t.collect.emit(I.TokenError),t.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"WEBID请求网络异常",logType:"fetch",level:"error",time:Date.now(),data:null}),t.collect.logger.warn("appid: "+t.config.app_id+", get webid error, use local webid~")},"","",this.config.enable_encryption,this.config.encryption_header)},e.prototype.complete=function(t){var n=t.web_id,r=t.user_unique_id;if(!n&&!r){this.collect.emit(I.TokenError),console.warn("token error");return}t.timestamp=Date.now(),this.collect.configManager.set({web_id:n,user_unique_id:r}),this.setStorage(t),this.tokenReady=!0,this.collect.emit(I.TokenComplete)},e.prototype.completeAnonymous=function(t){var n=t.anonymous_id||Yi(),r=t.user_unique_id||this.configManager.get("user_unique_id");this.collect.configManager.set({anonymous_id:n,user_unique_id:r||n}),t.anonymous_id=n,t.user_unique_id=r||n,this.setStorage(t),this.tokenReady=!0,this.collect.emit(I.TokenComplete)},e.prototype.completeTtWid=function(t){var n=t.user_unique_id||"",r=this.configManager.get("user_unique_id");this.configManager.set({user_unique_id:r||n}),this.setStorage(t),this.tokenReady=!0,this.collect.emit(I.TokenComplete)},e.prototype.setUuid=function(t){if(!t||["null","undefined","Null","None"].indexOf(t)!==-1)this.clearUuid();else{var n=String(t),r=this.configManager.get("user_unique_id"),i=this.cacheToken&&this.cacheToken.user_unique_id;if(n===r&&n===i)return;this.configManager.set({user_unique_id:n}),this.cacheToken?this.cacheToken.user_unique_id=n:(this.cacheToken={},this.cacheToken.user_unique_id=n),this.cacheToken.timestamp=Date.now(),this.setStorage(this.cacheToken),this.collect.emit(I.TokenChange,{type:"uuid",id:t}),this.collect.emit(I.SessionReset)}},e.prototype.clearUuid=function(){if(!this.config.enable_ttwebid){var t=this.enableAnonymousid?this.configManager.get("anonymous_id"):this.configManager.get("web_id"),n=this.configManager.get("user_unique_id");n!==t&&(this.configManager.set({user_unique_id:t}),this.cacheToken.user_unique_id=t,this.cacheToken.timestamp=Date.now(),this.setStorage(this.cacheToken),this.collect.emit(I.TokenReset))}},e.prototype.setAnonymousId=function(t){if(!(!t||!this.enableAnonymousid)){var n=this.configManager.get("anonymous_id"),r=this.configManager.get("user_unique_id");n!==t&&((n===r||!r)&&(this.configManager.set({user_unique_id:t}),this.cacheToken.user_unique_id=t,this.collect.emit(I.TokenChange,{type:"uuid",id:t})),this.configManager.set({anonymous_id:t}),this.cacheToken.anonymous_id=t,this.collect.emit(I.TokenChange,{type:"anonymous_id",id:t}),this.setStorage(this.cacheToken))}},e.prototype.setWebId=function(t){if(!(!t||this.config.enable_ttwebid||this.enableAnonymousid)){var n=this.configManager.get("web_id"),r=this.configManager.get("user_unique_id");(!r||r===n)&&(this.configManager.set({user_unique_id:t}),this.cacheToken.user_unique_id=t,this.collect.emit(I.TokenChange,{type:"uuid",id:t})),n!==t&&(this.configManager.set({web_id:t}),this.cacheToken.web_id=t,this.collect.emit(I.TokenChange,{type:"webid",id:t})),this.cacheToken.timestamp=Date.now(),this.setStorage(this.cacheToken)}},e.prototype.setStorage=function(t){t._type_=this.enableCustomWebid?"custom":"default";var n="diss".split("").reverse().join("");delete t[n],this.enableCookie||this.enableTTwebid?this.storage.setCookie(this.tokenKey,t,this.expiresTime,this.cookieDomain):this.storage.setItem(this.tokenKey,t),this.cacheToken=t},e.prototype.getReady=function(){return this.tokenReady},e.prototype.getTobId=function(){var t=this,n=this.configManager.getUrl("tobid"),r={app_id:this.config.app_id,user_unique_id:this.configManager.get("user_unique_id"),user_unique_id_type:this.configManager.get("user_unique_id_type")};this.enableAnonymousid?r.anonymous_id=this.configManager.get("anonymous_id"):r.web_id=this.configManager.get("web_id");var i=this.collect.cryptoData(r);return new Promise(function(o){Fo(n,i,3e4,t.enableTTwebid,function(s){s&&s.e===0?o(s.tobid):o("")},function(){o("")},"","",t.config.enable_encryption,t.config.encryption_header)})},e}(),Di=hv(function(e,t){(function(){var n,r=0xdeadbeefcafe,i=(r&16777215)==15715070;function o(d,m,x){d!=null&&(typeof d=="number"?this.fromNumber(d,m,x):m==null&&typeof d!="string"?this.fromString(d,256):this.fromString(d,m))}function s(){return new o(null)}function a(d,m,x,A,T,O){for(;--O>=0;){var F=m*this[d++]+x[A]+T;T=Math.floor(F/67108864),x[A++]=F&67108863}return T}function l(d,m,x,A,T,O){for(var F=m&32767,z=m>>15;--O>=0;){var he=this[d]&32767,De=this[d++]>>15,xt=z*he+De*F;he=F*he+((xt&32767)<<15)+x[A]+(T&**********),T=(he>>>30)+(xt>>>15)+z*De+(T>>>30),x[A++]=he&**********}return T}function u(d,m,x,A,T,O){for(var F=m&16383,z=m>>14;--O>=0;){var he=this[d]&16383,De=this[d++]>>14,xt=z*he+De*F;he=F*he+((xt&16383)<<14)+x[A]+T,T=(he>>28)+(xt>>14)+z*De,x[A++]=he&268435455}return T}var c=typeof navigator<"u";c&&i&&navigator.appName=="Microsoft Internet Explorer"?(o.prototype.am=l,n=30):c&&i&&navigator.appName!="Netscape"?(o.prototype.am=a,n=26):(o.prototype.am=u,n=28),o.prototype.DB=n,o.prototype.DM=(1<<n)-1,o.prototype.DV=1<<n;var h=52;o.prototype.FV=Math.pow(2,h),o.prototype.F1=h-n,o.prototype.F2=2*n-h;var f="0123456789abcdefghijklmnopqrstuvwxyz",v=new Array,p,g;for(p=48,g=0;g<=9;++g)v[p++]=g;for(p=97,g=10;g<36;++g)v[p++]=g;for(p=65,g=10;g<36;++g)v[p++]=g;function S(d){return f.charAt(d)}function y(d,m){var x=v[d.charCodeAt(m)];return x??-1}function w(d){for(var m=this.t-1;m>=0;--m)d[m]=this[m];d.t=this.t,d.s=this.s}function _(d){this.t=1,this.s=d<0?-1:0,d>0?this[0]=d:d<-1?this[0]=d+this.DV:this.t=0}function E(d){var m=s();return m.fromInt(d),m}function b(d,m){var x;if(m==16)x=4;else if(m==8)x=3;else if(m==256)x=8;else if(m==2)x=1;else if(m==32)x=5;else if(m==4)x=2;else{this.fromRadix(d,m);return}this.t=0,this.s=0;for(var A=d.length,T=!1,O=0;--A>=0;){var F=x==8?d[A]&255:y(d,A);if(F<0){d.charAt(A)=="-"&&(T=!0);continue}T=!1,O==0?this[this.t++]=F:O+x>this.DB?(this[this.t-1]|=(F&(1<<this.DB-O)-1)<<O,this[this.t++]=F>>this.DB-O):this[this.t-1]|=F<<O,O+=x,O>=this.DB&&(O-=this.DB)}x==8&&d[0]&128&&(this.s=-1,O>0&&(this[this.t-1]|=(1<<this.DB-O)-1<<O)),this.clamp(),T&&o.ZERO.subTo(this,this)}function k(){for(var d=this.s&this.DM;this.t>0&&this[this.t-1]==d;)--this.t}function P(d){if(this.s<0)return"-"+this.negate().toString(d);var m;if(d==16)m=4;else if(d==8)m=3;else if(d==2)m=1;else if(d==32)m=5;else if(d==4)m=2;else return this.toRadix(d);var x=(1<<m)-1,A,T=!1,O="",F=this.t,z=this.DB-F*this.DB%m;if(F-- >0)for(z<this.DB&&(A=this[F]>>z)>0&&(T=!0,O=S(A));F>=0;)z<m?(A=(this[F]&(1<<z)-1)<<m-z,A|=this[--F]>>(z+=this.DB-m)):(A=this[F]>>(z-=m)&x,z<=0&&(z+=this.DB,--F)),A>0&&(T=!0),T&&(O+=S(A));return T?O:"0"}function C(){var d=s();return o.ZERO.subTo(this,d),d}function D(){return this.s<0?this.negate():this}function R(d){var m=this.s-d.s;if(m!=0)return m;var x=this.t;if(m=x-d.t,m!=0)return this.s<0?-m:m;for(;--x>=0;)if((m=this[x]-d[x])!=0)return m;return 0}function B(d){var m=1,x;return(x=d>>>16)!=0&&(d=x,m+=16),(x=d>>8)!=0&&(d=x,m+=8),(x=d>>4)!=0&&(d=x,m+=4),(x=d>>2)!=0&&(d=x,m+=2),(x=d>>1)!=0&&(d=x,m+=1),m}function Y(){return this.t<=0?0:this.DB*(this.t-1)+B(this[this.t-1]^this.s&this.DM)}function J(d,m){var x;for(x=this.t-1;x>=0;--x)m[x+d]=this[x];for(x=d-1;x>=0;--x)m[x]=0;m.t=this.t+d,m.s=this.s}function ue(d,m){for(var x=d;x<this.t;++x)m[x-d]=this[x];m.t=Math.max(this.t-d,0),m.s=this.s}function j(d,m){var x=d%this.DB,A=this.DB-x,T=(1<<A)-1,O=Math.floor(d/this.DB),F=this.s<<x&this.DM,z;for(z=this.t-1;z>=0;--z)m[z+O+1]=this[z]>>A|F,F=(this[z]&T)<<x;for(z=O-1;z>=0;--z)m[z]=0;m[O]=F,m.t=this.t+O+1,m.s=this.s,m.clamp()}function ee(d,m){m.s=this.s;var x=Math.floor(d/this.DB);if(x>=this.t){m.t=0;return}var A=d%this.DB,T=this.DB-A,O=(1<<A)-1;m[0]=this[x]>>A;for(var F=x+1;F<this.t;++F)m[F-x-1]|=(this[F]&O)<<T,m[F-x]=this[F]>>A;A>0&&(m[this.t-x-1]|=(this.s&O)<<T),m.t=this.t-x,m.clamp()}function te(d,m){for(var x=0,A=0,T=Math.min(d.t,this.t);x<T;)A+=this[x]-d[x],m[x++]=A&this.DM,A>>=this.DB;if(d.t<this.t){for(A-=d.s;x<this.t;)A+=this[x],m[x++]=A&this.DM,A>>=this.DB;A+=this.s}else{for(A+=this.s;x<d.t;)A-=d[x],m[x++]=A&this.DM,A>>=this.DB;A-=d.s}m.s=A<0?-1:0,A<-1?m[x++]=this.DV+A:A>0&&(m[x++]=A),m.t=x,m.clamp()}function N(d,m){var x=this.abs(),A=d.abs(),T=x.t;for(m.t=T+A.t;--T>=0;)m[T]=0;for(T=0;T<A.t;++T)m[T+x.t]=x.am(0,A[T],m,T,0,x.t);m.s=0,m.clamp(),this.s!=d.s&&o.ZERO.subTo(m,m)}function H(d){for(var m=this.abs(),x=d.t=2*m.t;--x>=0;)d[x]=0;for(x=0;x<m.t-1;++x){var A=m.am(x,m[x],d,2*x,0,1);(d[x+m.t]+=m.am(x+1,2*m[x],d,2*x+1,A,m.t-x-1))>=m.DV&&(d[x+m.t]-=m.DV,d[x+m.t+1]=1)}d.t>0&&(d[d.t-1]+=m.am(x,m[x],d,2*x,0,1)),d.s=0,d.clamp()}function G(d,m,x){var A=d.abs();if(!(A.t<=0)){var T=this.abs();if(T.t<A.t){m!=null&&m.fromInt(0),x!=null&&this.copyTo(x);return}x==null&&(x=s());var O=s(),F=this.s,z=d.s,he=this.DB-B(A[A.t-1]);he>0?(A.lShiftTo(he,O),T.lShiftTo(he,x)):(A.copyTo(O),T.copyTo(x));var De=O.t,xt=O[De-1];if(xt!=0){var st=xt*(1<<this.F1)+(De>1?O[De-2]>>this.F2:0),on=this.FV/st,rs=(1<<this.F1)/st,Lt=1<<this.F2,Vt=x.t,is=Vt-De,Cn=m??s();for(O.dlShiftTo(is,Cn),x.compareTo(Cn)>=0&&(x[x.t++]=1,x.subTo(Cn,x)),o.ONE.dlShiftTo(De,Cn),Cn.subTo(O,O);O.t<De;)O[O.t++]=0;for(;--is>=0;){var hl=x[--Vt]==xt?this.DM:Math.floor(x[Vt]*on+(x[Vt-1]+Lt)*rs);if((x[Vt]+=O.am(0,hl,x,is,0,De))<hl)for(O.dlShiftTo(is,Cn),x.subTo(Cn,x);x[Vt]<--hl;)x.subTo(Cn,x)}m!=null&&(x.drShiftTo(De,m),F!=z&&o.ZERO.subTo(m,m)),x.t=De,x.clamp(),he>0&&x.rShiftTo(he,x),F<0&&o.ZERO.subTo(x,x)}}}function se(d){var m=s();return this.abs().divRemTo(d,null,m),this.s<0&&m.compareTo(o.ZERO)>0&&d.subTo(m,m),m}function re(d){this.m=d}function Rt(d){return d.s<0||d.compareTo(this.m)>=0?d.mod(this.m):d}function vt(d){return d}function Yt(d){d.divRemTo(this.m,null,d)}function rt(d,m,x){d.multiplyTo(m,x),this.reduce(x)}function Zt(d,m){d.squareTo(m),this.reduce(m)}re.prototype.convert=Rt,re.prototype.revert=vt,re.prototype.reduce=Yt,re.prototype.mulTo=rt,re.prototype.sqrTo=Zt;function Zo(){if(this.t<1)return 0;var d=this[0];if(!(d&1))return 0;var m=d&3;return m=m*(2-(d&15)*m)&15,m=m*(2-(d&255)*m)&255,m=m*(2-((d&65535)*m&65535))&65535,m=m*(2-d*m%this.DV)%this.DV,m>0?this.DV-m:-m}function Xt(d){this.m=d,this.mp=d.invDigit(),this.mpl=this.mp&32767,this.mph=this.mp>>15,this.um=(1<<d.DB-15)-1,this.mt2=2*d.t}function Xo(d){var m=s();return d.abs().dlShiftTo(this.m.t,m),m.divRemTo(this.m,null,m),d.s<0&&m.compareTo(o.ZERO)>0&&this.m.subTo(m,m),m}function al(d){var m=s();return d.copyTo(m),this.reduce(m),m}function Ir(d){for(;d.t<=this.mt2;)d[d.t++]=0;for(var m=0;m<this.m.t;++m){var x=d[m]&32767,A=x*this.mpl+((x*this.mph+(d[m]>>15)*this.mpl&this.um)<<15)&d.DM;for(x=m+this.m.t,d[x]+=this.m.am(0,A,d,m,0,this.m.t);d[x]>=d.DV;)d[x]-=d.DV,d[++x]++}d.clamp(),d.drShiftTo(this.m.t,d),d.compareTo(this.m)>=0&&d.subTo(this.m,d)}function Qo(d,m){d.squareTo(m),this.reduce(m)}function rr(d,m,x){d.multiplyTo(m,x),this.reduce(x)}Xt.prototype.convert=Xo,Xt.prototype.revert=al,Xt.prototype.reduce=Ir,Xt.prototype.mulTo=rr,Xt.prototype.sqrTo=Qo;function ll(){return(this.t>0?this[0]&1:this.s)==0}function Mi(d,m){if(d>4294967295||d<1)return o.ONE;var x=s(),A=s(),T=m.convert(this),O=B(d)-1;for(T.copyTo(x);--O>=0;)if(m.sqrTo(x,A),(d&1<<O)>0)m.mulTo(A,T,x);else{var F=x;x=A,A=F}return m.revert(x)}function ir(d,m){var x;return d<256||m.isEven()?x=new re(m):x=new Xt(m),this.exp(d,x)}o.prototype.copyTo=w,o.prototype.fromInt=_,o.prototype.fromString=b,o.prototype.clamp=k,o.prototype.dlShiftTo=J,o.prototype.drShiftTo=ue,o.prototype.lShiftTo=j,o.prototype.rShiftTo=ee,o.prototype.subTo=te,o.prototype.multiplyTo=N,o.prototype.squareTo=H,o.prototype.divRemTo=G,o.prototype.invDigit=Zo,o.prototype.isEven=ll,o.prototype.exp=Mi,o.prototype.toString=P,o.prototype.negate=C,o.prototype.abs=D,o.prototype.compareTo=R,o.prototype.bitLength=Y,o.prototype.mod=se,o.prototype.modPowInt=ir,o.ZERO=E(0),o.ONE=E(1);function Ri(){var d=s();return this.copyTo(d),d}function ul(){if(this.s<0){if(this.t==1)return this[0]-this.DV;if(this.t==0)return-1}else{if(this.t==1)return this[0];if(this.t==0)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]}function Jo(){return this.t==0?this.s:this[0]<<24>>24}function es(){return this.t==0?this.s:this[0]<<16>>16}function it(d){return Math.floor(Math.LN2*this.DB/Math.log(d))}function An(){return this.s<0?-1:this.t<=0||this.t==1&&this[0]<=0?0:1}function Lr(d){if(d==null&&(d=10),this.signum()==0||d<2||d>36)return"0";var m=this.chunkSize(d),x=Math.pow(d,m),A=E(x),T=s(),O=s(),F="";for(this.divRemTo(A,T,O);T.signum()>0;)F=(x+O.intValue()).toString(d).substr(1)+F,T.divRemTo(A,T,O);return O.intValue().toString(d)+F}function Z(d,m){this.fromInt(0),m==null&&(m=10);for(var x=this.chunkSize(m),A=Math.pow(m,x),T=!1,O=0,F=0,z=0;z<d.length;++z){var he=y(d,z);if(he<0){d.charAt(z)=="-"&&this.signum()==0&&(T=!0);continue}F=m*F+he,++O>=x&&(this.dMultiply(A),this.dAddOffset(F,0),O=0,F=0)}O>0&&(this.dMultiply(Math.pow(m,O)),this.dAddOffset(F,0)),T&&o.ZERO.subTo(this,this)}function oe(d,m,x){if(typeof m=="number")if(d<2)this.fromInt(1);else for(this.fromNumber(d,x),this.testBit(d-1)||this.bitwiseTo(o.ONE.shiftLeft(d-1),or,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(m);)this.dAddOffset(2,0),this.bitLength()>d&&this.subTo(o.ONE.shiftLeft(d-1),this);else{var A=new Array,T=d&7;A.length=(d>>3)+1,m.nextBytes(A),T>0?A[0]&=(1<<T)-1:A[0]=0,this.fromString(A,256)}}function q(){var d=this.t,m=new Array;m[0]=this.s;var x=this.DB-d*this.DB%8,A,T=0;if(d-- >0)for(x<this.DB&&(A=this[d]>>x)!=(this.s&this.DM)>>x&&(m[T++]=A|this.s<<this.DB-x);d>=0;)x<8?(A=(this[d]&(1<<x)-1)<<8-x,A|=this[--d]>>(x+=this.DB-8)):(A=this[d]>>(x-=8)&255,x<=0&&(x+=this.DB,--d)),A&128&&(A|=-256),T==0&&(this.s&128)!=(A&128)&&++T,(T>0||A!=this.s)&&(m[T++]=A);return m}function ce(d){return this.compareTo(d)==0}function Ot(d){return this.compareTo(d)<0?this:d}function yt(d){return this.compareTo(d)>0?this:d}function ie(d,m,x){var A,T,O=Math.min(d.t,this.t);for(A=0;A<O;++A)x[A]=m(this[A],d[A]);if(d.t<this.t){for(T=d.s&this.DM,A=O;A<this.t;++A)x[A]=m(this[A],T);x.t=this.t}else{for(T=this.s&this.DM,A=O;A<d.t;++A)x[A]=m(T,d[A]);x.t=d.t}x.s=m(this.s,d.s),x.clamp()}function K(d,m){return d&m}function wt(d){var m=s();return this.bitwiseTo(d,K,m),m}function or(d,m){return d|m}function ts(d){var m=s();return this.bitwiseTo(d,or,m),m}function th(d,m){return d^m}function Tv(d){var m=s();return this.bitwiseTo(d,th,m),m}function nh(d,m){return d&~m}function kv(d){var m=s();return this.bitwiseTo(d,nh,m),m}function Pv(){for(var d=s(),m=0;m<this.t;++m)d[m]=this.DM&~this[m];return d.t=this.t,d.s=~this.s,d}function Dv(d){var m=s();return d<0?this.rShiftTo(-d,m):this.lShiftTo(d,m),m}function Mv(d){var m=s();return d<0?this.lShiftTo(-d,m):this.rShiftTo(d,m),m}function Rv(d){if(d==0)return-1;var m=0;return d&65535||(d>>=16,m+=16),d&255||(d>>=8,m+=8),d&15||(d>>=4,m+=4),d&3||(d>>=2,m+=2),d&1||++m,m}function Ov(){for(var d=0;d<this.t;++d)if(this[d]!=0)return d*this.DB+Rv(this[d]);return this.s<0?this.t*this.DB:-1}function Iv(d){for(var m=0;d!=0;)d&=d-1,++m;return m}function Lv(){for(var d=0,m=this.s&this.DM,x=0;x<this.t;++x)d+=Iv(this[x]^m);return d}function Vv(d){var m=Math.floor(d/this.DB);return m>=this.t?this.s!=0:(this[m]&1<<d%this.DB)!=0}function Bv(d,m){var x=o.ONE.shiftLeft(d);return this.bitwiseTo(x,m,x),x}function Nv(d){return this.changeBit(d,or)}function Fv(d){return this.changeBit(d,nh)}function zv(d){return this.changeBit(d,th)}function Uv(d,m){for(var x=0,A=0,T=Math.min(d.t,this.t);x<T;)A+=this[x]+d[x],m[x++]=A&this.DM,A>>=this.DB;if(d.t<this.t){for(A+=d.s;x<this.t;)A+=this[x],m[x++]=A&this.DM,A>>=this.DB;A+=this.s}else{for(A+=this.s;x<d.t;)A+=d[x],m[x++]=A&this.DM,A>>=this.DB;A+=d.s}m.s=A<0?-1:0,A>0?m[x++]=A:A<-1&&(m[x++]=this.DV+A),m.t=x,m.clamp()}function jv(d){var m=s();return this.addTo(d,m),m}function Gv(d){var m=s();return this.subTo(d,m),m}function Hv(d){var m=s();return this.multiplyTo(d,m),m}function Wv(){var d=s();return this.squareTo(d),d}function Kv(d){var m=s();return this.divRemTo(d,m,null),m}function qv(d){var m=s();return this.divRemTo(d,null,m),m}function $v(d){var m=s(),x=s();return this.divRemTo(d,m,x),new Array(m,x)}function Yv(d){this[this.t]=this.am(0,d-1,this,0,0,this.t),++this.t,this.clamp()}function Zv(d,m){if(d!=0){for(;this.t<=m;)this[this.t++]=0;for(this[m]+=d;this[m]>=this.DV;)this[m]-=this.DV,++m>=this.t&&(this[this.t++]=0),++this[m]}}function Oi(){}function rh(d){return d}function Xv(d,m,x){d.multiplyTo(m,x)}function Qv(d,m){d.squareTo(m)}Oi.prototype.convert=rh,Oi.prototype.revert=rh,Oi.prototype.mulTo=Xv,Oi.prototype.sqrTo=Qv;function Jv(d){return this.exp(d,new Oi)}function ey(d,m,x){var A=Math.min(this.t+d.t,m);for(x.s=0,x.t=A;A>0;)x[--A]=0;var T;for(T=x.t-this.t;A<T;++A)x[A+this.t]=this.am(0,d[A],x,A,0,this.t);for(T=Math.min(d.t,m);A<T;++A)this.am(0,d[A],x,A,0,m-A);x.clamp()}function ty(d,m,x){--m;var A=x.t=this.t+d.t-m;for(x.s=0;--A>=0;)x[A]=0;for(A=Math.max(m-this.t,0);A<d.t;++A)x[this.t+A-m]=this.am(m-A,d[A],x,0,0,this.t+A-m);x.clamp(),x.drShiftTo(1,x)}function sr(d){this.r2=s(),this.q3=s(),o.ONE.dlShiftTo(2*d.t,this.r2),this.mu=this.r2.divide(d),this.m=d}function ny(d){if(d.s<0||d.t>2*this.m.t)return d.mod(this.m);if(d.compareTo(this.m)<0)return d;var m=s();return d.copyTo(m),this.reduce(m),m}function ry(d){return d}function iy(d){for(d.drShiftTo(this.m.t-1,this.r2),d.t>this.m.t+1&&(d.t=this.m.t+1,d.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);d.compareTo(this.r2)<0;)d.dAddOffset(1,this.m.t+1);for(d.subTo(this.r2,d);d.compareTo(this.m)>=0;)d.subTo(this.m,d)}function oy(d,m){d.squareTo(m),this.reduce(m)}function sy(d,m,x){d.multiplyTo(m,x),this.reduce(x)}sr.prototype.convert=ny,sr.prototype.revert=ry,sr.prototype.reduce=iy,sr.prototype.mulTo=sy,sr.prototype.sqrTo=oy;function ay(d,m){var x=d.bitLength(),A,T=E(1),O;if(x<=0)return T;x<18?A=1:x<48?A=3:x<144?A=4:x<768?A=5:A=6,x<8?O=new re(m):m.isEven()?O=new sr(m):O=new Xt(m);var F=new Array,z=3,he=A-1,De=(1<<A)-1;if(F[1]=O.convert(this),A>1){var xt=s();for(O.sqrTo(F[1],xt);z<=De;)F[z]=s(),O.mulTo(xt,F[z-2],F[z]),z+=2}var st=d.t-1,on,rs=!0,Lt=s(),Vt;for(x=B(d[st])-1;st>=0;){for(x>=he?on=d[st]>>x-he&De:(on=(d[st]&(1<<x+1)-1)<<he-x,st>0&&(on|=d[st-1]>>this.DB+x-he)),z=A;!(on&1);)on>>=1,--z;if((x-=z)<0&&(x+=this.DB,--st),rs)F[on].copyTo(T),rs=!1;else{for(;z>1;)O.sqrTo(T,Lt),O.sqrTo(Lt,T),z-=2;z>0?O.sqrTo(T,Lt):(Vt=T,T=Lt,Lt=Vt),O.mulTo(Lt,F[on],T)}for(;st>=0&&!(d[st]&1<<x);)O.sqrTo(T,Lt),Vt=T,T=Lt,Lt=Vt,--x<0&&(x=this.DB-1,--st)}return O.revert(T)}function ly(d){var m=this.s<0?this.negate():this.clone(),x=d.s<0?d.negate():d.clone();if(m.compareTo(x)<0){var A=m;m=x,x=A}var T=m.getLowestSetBit(),O=x.getLowestSetBit();if(O<0)return m;for(T<O&&(O=T),O>0&&(m.rShiftTo(O,m),x.rShiftTo(O,x));m.signum()>0;)(T=m.getLowestSetBit())>0&&m.rShiftTo(T,m),(T=x.getLowestSetBit())>0&&x.rShiftTo(T,x),m.compareTo(x)>=0?(m.subTo(x,m),m.rShiftTo(1,m)):(x.subTo(m,x),x.rShiftTo(1,x));return O>0&&x.lShiftTo(O,x),x}function uy(d){if(d<=0)return 0;var m=this.DV%d,x=this.s<0?d-1:0;if(this.t>0)if(m==0)x=this[0]%d;else for(var A=this.t-1;A>=0;--A)x=(m*x+this[A])%d;return x}function cy(d){var m=d.isEven();if(this.isEven()&&m||d.signum()==0)return o.ZERO;for(var x=d.clone(),A=this.clone(),T=E(1),O=E(0),F=E(0),z=E(1);x.signum()!=0;){for(;x.isEven();)x.rShiftTo(1,x),m?((!T.isEven()||!O.isEven())&&(T.addTo(this,T),O.subTo(d,O)),T.rShiftTo(1,T)):O.isEven()||O.subTo(d,O),O.rShiftTo(1,O);for(;A.isEven();)A.rShiftTo(1,A),m?((!F.isEven()||!z.isEven())&&(F.addTo(this,F),z.subTo(d,z)),F.rShiftTo(1,F)):z.isEven()||z.subTo(d,z),z.rShiftTo(1,z);x.compareTo(A)>=0?(x.subTo(A,x),m&&T.subTo(F,T),O.subTo(z,O)):(A.subTo(x,A),m&&F.subTo(T,F),z.subTo(O,z))}if(A.compareTo(o.ONE)!=0)return o.ZERO;if(z.compareTo(d)>=0)return z.subtract(d);if(z.signum()<0)z.addTo(d,z);else return z;return z.signum()<0?z.add(d):z}var Ke=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],fy=(1<<26)/Ke[Ke.length-1];function hy(d){var m,x=this.abs();if(x.t==1&&x[0]<=Ke[Ke.length-1]){for(m=0;m<Ke.length;++m)if(x[0]==Ke[m])return!0;return!1}if(x.isEven())return!1;for(m=1;m<Ke.length;){for(var A=Ke[m],T=m+1;T<Ke.length&&A<fy;)A*=Ke[T++];for(A=x.modInt(A);m<T;)if(A%Ke[m++]==0)return!1}return x.millerRabin(d)}function dy(d){var m=this.subtract(o.ONE),x=m.getLowestSetBit();if(x<=0)return!1;var A=m.shiftRight(x);d=d+1>>1,d>Ke.length&&(d=Ke.length);for(var T=s(),O=0;O<d;++O){T.fromInt(Ke[Math.floor(Math.random()*Ke.length)]);var F=T.modPow(A,this);if(F.compareTo(o.ONE)!=0&&F.compareTo(m)!=0){for(var z=1;z++<x&&F.compareTo(m)!=0;)if(F=F.modPowInt(2,this),F.compareTo(o.ONE)==0)return!1;if(F.compareTo(m)!=0)return!1}}return!0}o.prototype.chunkSize=it,o.prototype.toRadix=Lr,o.prototype.fromRadix=Z,o.prototype.fromNumber=oe,o.prototype.bitwiseTo=ie,o.prototype.changeBit=Bv,o.prototype.addTo=Uv,o.prototype.dMultiply=Yv,o.prototype.dAddOffset=Zv,o.prototype.multiplyLowerTo=ey,o.prototype.multiplyUpperTo=ty,o.prototype.modInt=uy,o.prototype.millerRabin=dy,o.prototype.clone=Ri,o.prototype.intValue=ul,o.prototype.byteValue=Jo,o.prototype.shortValue=es,o.prototype.signum=An,o.prototype.toByteArray=q,o.prototype.equals=ce,o.prototype.min=Ot,o.prototype.max=yt,o.prototype.and=wt,o.prototype.or=ts,o.prototype.xor=Tv,o.prototype.andNot=kv,o.prototype.not=Pv,o.prototype.shiftLeft=Dv,o.prototype.shiftRight=Mv,o.prototype.getLowestSetBit=Ov,o.prototype.bitCount=Lv,o.prototype.testBit=Vv,o.prototype.setBit=Nv,o.prototype.clearBit=Fv,o.prototype.flipBit=zv,o.prototype.add=jv,o.prototype.subtract=Gv,o.prototype.multiply=Hv,o.prototype.divide=Kv,o.prototype.remainder=qv,o.prototype.divideAndRemainder=$v,o.prototype.modPow=ay,o.prototype.modInverse=cy,o.prototype.pow=Jv,o.prototype.gcd=ly,o.prototype.isProbablePrime=hy,o.prototype.square=Wv,o.prototype.Barrett=sr;var ns,ot,Pe;function py(d){ot[Pe++]^=d&255,ot[Pe++]^=d>>8&255,ot[Pe++]^=d>>16&255,ot[Pe++]^=d>>24&255,Pe>=fl&&(Pe-=fl)}function ih(){py(new Date().getTime())}if(ot==null){ot=new Array,Pe=0;var It;if(typeof window<"u"&&window.crypto){if(window.crypto.getRandomValues){var oh=new Uint8Array(32);for(window.crypto.getRandomValues(oh),It=0;It<32;++It)ot[Pe++]=oh[It]}else if(navigator.appName=="Netscape"&&navigator.appVersion<"5"){var sh=window.crypto.random(32);for(It=0;It<sh.length;++It)ot[Pe++]=sh.charCodeAt(It)&255}}for(;Pe<fl;)It=Math.floor(65536*Math.random()),ot[Pe++]=It>>>8,ot[Pe++]=It&255;Pe=0,ih()}function my(){if(ns==null){for(ih(),ns=wy(),ns.init(ot),Pe=0;Pe<ot.length;++Pe)ot[Pe]=0;Pe=0}return ns.next()}function gy(d){var m;for(m=0;m<d.length;++m)d[m]=my()}function ah(){}ah.prototype.nextBytes=gy;function cl(){this.i=0,this.j=0,this.S=new Array}function vy(d){var m,x,A;for(m=0;m<256;++m)this.S[m]=m;for(x=0,m=0;m<256;++m)x=x+this.S[m]+d[m%d.length]&255,A=this.S[m],this.S[m]=this.S[x],this.S[x]=A;this.i=0,this.j=0}function yy(){var d;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,d=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=d,this.S[d+this.S[this.i]&255]}cl.prototype.init=vy,cl.prototype.next=yy;function wy(){return new cl}var fl=256;e.exports={default:o,BigInteger:o,SecureRandom:ah}}).call(fv)});Di.BigInteger;Di.SecureRandom;const{BigInteger:Ai}=Di;function n8(e){let t=e.toString(16);if(t[0]!=="-")t.length%2===1?t="0"+t:t.match(/^[0-7]/)||(t="00"+t);else{t=t.substr(1);let n=t.length;n%2===1?n+=1:t.match(/^[0-7]/)||(n+=2);let r="";for(let i=0;i<n;i++)r+="f";r=new Ai(r,16),t=r.xor(e).add(Ai.ONE),t=t.toString(16).replace(/^-/,"")}return t}class mv{constructor(){this.tlv=null,this.t="00",this.l="00",this.v=""}getEncodedHex(){return this.tlv||(this.v=this.getValue(),this.l=this.getLength(),this.tlv=this.t+this.l+this.v),this.tlv}getLength(){const t=this.v.length/2;let n=t.toString(16);return n.length%2===1&&(n="0"+n),t<128?n:(128+n.length/2).toString(16)+n}getValue(){return""}}class $p extends mv{constructor(t){super(),this.t="02",t&&(this.v=n8(t))}getValue(){return this.v}}class r8 extends mv{constructor(t){super(),this.t="30",this.asn1Array=t}getValue(){return this.v=this.asn1Array.map(t=>t.getEncodedHex()).join(""),this.v}}function gv(e,t){return+e[t+2]<8?1:+e.substr(t+2,2)&128}function Yp(e,t){const n=gv(e,t),r=e.substr(t+2,n*2);return r?(+r[0]<8?new Ai(r,16):new Ai(r.substr(2),16)).intValue():-1}function iu(e,t){const n=gv(e,t);return t+(n+1)*2}var i8={encodeDer(e,t){const n=new $p(e),r=new $p(t);return new r8([n,r]).getEncodedHex()},decodeDer(e){const t=iu(e,0),n=iu(e,t),r=Yp(e,t),i=e.substr(n,r*2),o=n+i.length,s=iu(e,o),a=Yp(e,o),l=e.substr(s,a*2),u=new Ai(i,16),c=new Ai(l,16);return{r:u,s:c}}};const{BigInteger:$e}=Di,Zp=new $e("2"),Xp=new $e("3");class Mn{constructor(t,n){this.x=n,this.q=t}equals(t){return t===this?!0:this.q.equals(t.q)&&this.x.equals(t.x)}toBigInteger(){return this.x}negate(){return new Mn(this.q,this.x.negate().mod(this.q))}add(t){return new Mn(this.q,this.x.add(t.toBigInteger()).mod(this.q))}subtract(t){return new Mn(this.q,this.x.subtract(t.toBigInteger()).mod(this.q))}multiply(t){return new Mn(this.q,this.x.multiply(t.toBigInteger()).mod(this.q))}divide(t){return new Mn(this.q,this.x.multiply(t.toBigInteger().modInverse(this.q)).mod(this.q))}square(){return new Mn(this.q,this.x.square().mod(this.q))}}class qn{constructor(t,n,r,i){this.curve=t,this.x=n,this.y=r,this.z=i??$e.ONE,this.zinv=null}getX(){return this.zinv===null&&(this.zinv=this.z.modInverse(this.curve.q)),this.curve.fromBigInteger(this.x.toBigInteger().multiply(this.zinv).mod(this.curve.q))}getY(){return this.zinv===null&&(this.zinv=this.z.modInverse(this.curve.q)),this.curve.fromBigInteger(this.y.toBigInteger().multiply(this.zinv).mod(this.curve.q))}equals(t){return t===this?!0:this.isInfinity()?t.isInfinity():t.isInfinity()?this.isInfinity():t.y.toBigInteger().multiply(this.z).subtract(this.y.toBigInteger().multiply(t.z)).mod(this.curve.q).equals($e.ZERO)?t.x.toBigInteger().multiply(this.z).subtract(this.x.toBigInteger().multiply(t.z)).mod(this.curve.q).equals($e.ZERO):!1}isInfinity(){return this.x===null&&this.y===null?!0:this.z.equals($e.ZERO)&&!this.y.toBigInteger().equals($e.ZERO)}negate(){return new qn(this.curve,this.x,this.y.negate(),this.z)}add(t){if(this.isInfinity())return t;if(t.isInfinity())return this;const n=this.x.toBigInteger(),r=this.y.toBigInteger(),i=this.z,o=t.x.toBigInteger(),s=t.y.toBigInteger(),a=t.z,l=this.curve.q,u=n.multiply(a).mod(l),c=o.multiply(i).mod(l),h=u.subtract(c),f=r.multiply(a).mod(l),v=s.multiply(i).mod(l),p=f.subtract(v);if($e.ZERO.equals(h))return $e.ZERO.equals(p)?this.twice():this.curve.infinity;const g=u.add(c),S=i.multiply(a).mod(l),y=h.square().mod(l),w=h.multiply(y).mod(l),_=S.multiply(p.square()).subtract(g.multiply(y)).mod(l),E=h.multiply(_).mod(l),b=p.multiply(y.multiply(u).subtract(_)).subtract(f.multiply(w)).mod(l),k=w.multiply(S).mod(l);return new qn(this.curve,this.curve.fromBigInteger(E),this.curve.fromBigInteger(b),k)}twice(){if(this.isInfinity())return this;if(!this.y.toBigInteger().signum())return this.curve.infinity;const t=this.x.toBigInteger(),n=this.y.toBigInteger(),r=this.z,i=this.curve.q,o=this.curve.a.toBigInteger(),s=t.square().multiply(Xp).add(o.multiply(r.square())).mod(i),a=n.shiftLeft(1).multiply(r).mod(i),l=n.square().mod(i),u=l.multiply(t).multiply(r).mod(i),c=a.square().mod(i),h=s.square().subtract(u.shiftLeft(3)).mod(i),f=a.multiply(h).mod(i),v=s.multiply(u.shiftLeft(2).subtract(h)).subtract(c.shiftLeft(1).multiply(l)).mod(i),p=a.multiply(c).mod(i);return new qn(this.curve,this.curve.fromBigInteger(f),this.curve.fromBigInteger(v),p)}multiply(t){if(this.isInfinity())return this;if(!t.signum())return this.curve.infinity;const n=t.multiply(Xp),r=this.negate();let i=this;for(let o=n.bitLength()-2;o>0;o--){i=i.twice();const s=n.testBit(o),a=t.testBit(o);s!==a&&(i=i.add(s?this:r))}return i}}class o8{constructor(t,n,r){this.q=t,this.a=this.fromBigInteger(n),this.b=this.fromBigInteger(r),this.infinity=new qn(this,null,null)}equals(t){return t===this?!0:this.q.equals(t.q)&&this.a.equals(t.a)&&this.b.equals(t.b)}fromBigInteger(t){return new Mn(this.q,t)}decodePointHex(t){switch(parseInt(t.substr(0,2),16)){case 0:return this.infinity;case 2:case 3:const n=this.fromBigInteger(new $e(t.substr(2),16));let r=this.fromBigInteger(n.multiply(n.square()).add(n.multiply(this.a)).add(this.b).toBigInteger().modPow(this.q.divide(new $e("4")).add($e.ONE),this.q));return r.toBigInteger().mod(Zp).equals(new $e(t.substr(0,2),16).subtract(Zp))||(r=r.negate()),new qn(this,n,r);case 4:case 6:case 7:const i=(t.length-2)/2,o=t.substr(2,i),s=t.substr(i+2,i);return new qn(this,this.fromBigInteger(new $e(o,16)),this.fromBigInteger(new $e(s,16)));default:return null}}}var s8={ECPointFp:qn,ECCurveFp:o8};const{BigInteger:Ht,SecureRandom:a8}=Di,{ECCurveFp:l8}=s8,u8=new a8,{curve:ui,G:c8,n:Qp}=vv();function f8(){return ui}function vv(){const e=new Ht("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFF",16),t=new Ht("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC",16),n=new Ht("28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93",16),r=new l8(e,t,n),s=r.decodePointHex("04"+"32C4AE2C1F1981195F9904466A39C9948FE30BBFF2660BE1715A4589334C74C7"+"BC3736A2F4F6779C59BDCEE36B692153D0A9877CC62A474002DF32E52139F0A0"),a=new Ht("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFF7203DF6B21C6052B53BBF40939D54123",16);return{curve:r,G:s,n:a}}function h8(e,t,n){const i=(e?new Ht(e,t,n):new Ht(Qp.bitLength(),u8)).mod(Qp.subtract(Ht.ONE)).add(Ht.ONE),o=uo(i.toString(16),64),s=c8.multiply(i),a=uo(s.getX().toBigInteger().toString(16),64),l=uo(s.getY().toBigInteger().toString(16),64),u="04"+a+l;return{privateKey:o,publicKey:u}}function d8(e){if(e.length!==130)throw new Error("Invalid public key to compress");const t=(e.length-2)/2,n=e.substr(2,t),r=new Ht(e.substr(t+2,t),16);let i="03";return r.mod(new Ht("2")).equals(Ht.ZERO)&&(i="02"),i+n}function p8(e){e=unescape(encodeURIComponent(e));const t=e.length,n=[];for(let i=0;i<t;i++)n[i>>>2]|=(e.charCodeAt(i)&255)<<24-i%4*8;const r=[];for(let i=0;i<t;i++){const o=n[i>>>2]>>>24-i%4*8&255;r.push((o>>>4).toString(16)),r.push((o&15).toString(16))}return r.join("")}function uo(e,t){return e.length>=t?e:new Array(t-e.length+1).join("0")+e}function m8(e){return e.map(t=>(t=t.toString(16),t.length===1?"0"+t:t)).join("")}function g8(e){const t=[];let n=0;for(let r=0;r<e.length*2;r+=2)t[r>>>3]|=parseInt(e[n],10)<<24-r%8*4,n++;try{const r=[];for(let i=0;i<e.length;i++){const o=t[i>>>2]>>>24-i%4*8&255;r.push(String.fromCharCode(o))}return decodeURIComponent(escape(r.join("")))}catch{throw new Error("Malformed UTF-8 data")}}function v8(e){const t=[];let n=e.length;n%2!==0&&(e=uo(e,n+1)),n=e.length;for(let r=0;r<n;r+=2)t.push(parseInt(e.substr(r,2),16));return t}function y8(e){const t=ui.decodePointHex(e);if(!t)return!1;const n=t.getX();return t.getY().square().equals(n.multiply(n.square()).add(n.multiply(ui.a)).add(ui.b))}function w8(e,t){const n=ui.decodePointHex(e);if(!n)return!1;const r=ui.decodePointHex(t);return r?n.equals(r):!1}var $={getGlobalCurve:f8,generateEcparam:vv,generateKeyPairHex:h8,compressPublicKeyHex:d8,utf8ToHex:p8,leftPad:uo,arrayToHex:m8,arrayToUtf8:g8,hexToArray:v8,verifyPublicKey:y8,comparePublicKeyHex:w8};const Nt=new Uint32Array(68),ou=new Uint32Array(64);function Ct(e,t){const n=t&31;return e<<n|e>>>32-n}function Jp(e,t){const n=[];for(let r=e.length-1;r>=0;r--)n[r]=(e[r]^t[r])&255;return n}function x8(e){return e^Ct(e,9)^Ct(e,17)}function _8(e){return e^Ct(e,15)^Ct(e,23)}function Zs(e){let t=e.length*8,n=t%512;n=n>=448?512-n%448-1:448-n-1;const r=new Array((n-7)/8),i=new Array(8);for(let c=0,h=r.length;c<h;c++)r[c]=0;for(let c=0,h=i.length;c<h;c++)i[c]=0;t=t.toString(2);for(let c=7;c>=0;c--)if(t.length>8){const h=t.length-8;i[c]=parseInt(t.substr(h),2),t=t.substr(0,h)}else t.length>0&&(i[c]=parseInt(t,2),t="");const o=new Uint8Array([...e,128,...r,...i]),s=new DataView(o.buffer,0),a=o.length/64,l=new Uint32Array([1937774191,1226093241,388252375,3666478592,2842636476,372324522,3817729613,2969243214]);for(let c=0;c<a;c++){Nt.fill(0),ou.fill(0);const h=16*c;for(let B=0;B<16;B++)Nt[B]=s.getUint32((h+B)*4,!1);for(let B=16;B<68;B++)Nt[B]=_8(Nt[B-16]^Nt[B-9]^Ct(Nt[B-3],15))^Ct(Nt[B-13],7)^Nt[B-6];for(let B=0;B<64;B++)ou[B]=Nt[B]^Nt[B+4];const f=2043430169,v=2055708042;let p=l[0],g=l[1],S=l[2],y=l[3],w=l[4],_=l[5],E=l[6],b=l[7],k,P,C,D,R;for(let B=0;B<64;B++)R=B>=0&&B<=15?f:v,k=Ct(Ct(p,12)+w+Ct(R,B),7),P=k^Ct(p,12),C=(B>=0&&B<=15?p^g^S:p&g|p&S|g&S)+y+P+ou[B],D=(B>=0&&B<=15?w^_^E:w&_|~w&E)+b+k+Nt[B],y=S,S=Ct(g,9),g=p,p=C,b=E,E=Ct(_,19),_=w,w=x8(D);l[0]^=p,l[1]^=g,l[2]^=S,l[3]^=y,l[4]^=w,l[5]^=_,l[6]^=E,l[7]^=b}const u=[];for(let c=0,h=l.length;c<h;c++){const f=l[c];u.push((f&4278190080)>>>24,(f&16711680)>>>16,(f&65280)>>>8,f&255)}return u}const zo=64,yv=new Uint8Array(zo),wv=new Uint8Array(zo);for(let e=0;e<zo;e++)yv[e]=54,wv[e]=92;function S8(e,t){for(t.length>zo&&(t=Zs(t));t.length<zo;)t.push(0);const n=Jp(t,yv),r=Jp(t,wv),i=Zs([...n,...e]);return Zs([...r,...i])}var xv={sm3:Zs,hmac:S8};const{BigInteger:lt}=Di,{encodeDer:E8,decodeDer:A8}=i8,Ci=xv.sm3,{G:mr,curve:_v,n:ti}=$.generateEcparam(),Sv=0;function C8(e,t,n=1){e=typeof e=="string"?$.hexToArray($.utf8ToHex(e)):Array.prototype.slice.call(e),t=$.getGlobalCurve().decodePointHex(t);const r=$.generateKeyPairHex(),i=new lt(r.privateKey,16);let o=r.publicKey;o.length>128&&(o=o.substr(o.length-128));const s=t.multiply(i),a=$.hexToArray($.leftPad(s.getX().toBigInteger().toRadix(16),64)),l=$.hexToArray($.leftPad(s.getY().toBigInteger().toRadix(16),64)),u=$.arrayToHex(Ci([].concat(a,e,l)));let c=1,h=0,f=[];const v=[].concat(a,l),p=()=>{f=Ci([...v,c>>24&255,c>>16&255,c>>8&255,c&255]),c++,h=0};p();for(let S=0,y=e.length;S<y;S++)h===f.length&&p(),e[S]^=f[h++]&255;const g=$.arrayToHex(e);return n===Sv?o+g+u:o+u+g}function b8(e,t,n=1,{output:r="string"}={}){t=new lt(t,16);let i=e.substr(128,64),o=e.substr(192);n===Sv&&(i=e.substr(e.length-64),o=e.substr(128,e.length-128-64));const s=$.hexToArray(o),l=$.getGlobalCurve().decodePointHex("04"+e.substr(0,128)).multiply(t),u=$.hexToArray($.leftPad(l.getX().toBigInteger().toRadix(16),64)),c=$.hexToArray($.leftPad(l.getY().toBigInteger().toRadix(16),64));let h=1,f=0,v=[];const p=[].concat(u,c),g=()=>{v=Ci([...p,h>>24&255,h>>16&255,h>>8&255,h&255]),h++,f=0};g();for(let y=0,w=s.length;y<w;y++)f===v.length&&g(),s[y]^=v[f++]&255;return $.arrayToHex(Ci([].concat(u,s,c)))===i.toLowerCase()?r==="array"?s:$.arrayToUtf8(s):r==="array"?[]:""}function T8(e,t,{pointPool:n,der:r,hash:i,publicKey:o,userId:s}={}){let a=typeof e=="string"?$.utf8ToHex(e):$.arrayToHex(e);i&&(o=o||P8(t),a=Ev(a,o,s));const l=new lt(t,16),u=new lt(a,16);let c=null,h=null,f=null;do{do{let v;n&&n.length?v=n.pop():v=Av(),c=v.k,h=u.add(v.x1).mod(ti)}while(h.equals(lt.ZERO)||h.add(c).equals(ti));f=l.add(lt.ONE).modInverse(ti).multiply(c.subtract(h.multiply(l))).mod(ti)}while(f.equals(lt.ZERO));return r?E8(h,f):$.leftPad(h.toString(16),64)+$.leftPad(f.toString(16),64)}function k8(e,t,n,{der:r,hash:i,userId:o}={}){let s=typeof e=="string"?$.utf8ToHex(e):$.arrayToHex(e);i&&(s=Ev(s,n,o));let a,l;if(r){const p=A8(t);a=p.r,l=p.s}else a=new lt(t.substring(0,64),16),l=new lt(t.substring(64),16);const u=_v.decodePointHex(n),c=new lt(s,16),h=a.add(l).mod(ti);if(h.equals(lt.ZERO))return!1;const f=mr.multiply(l).add(u.multiply(h)),v=c.add(f.getX().toBigInteger()).mod(ti);return a.equals(v)}function Ev(e,t,n="1234567812345678"){n=$.utf8ToHex(n);const r=$.leftPad(mr.curve.a.toBigInteger().toRadix(16),64),i=$.leftPad(mr.curve.b.toBigInteger().toRadix(16),64),o=$.leftPad(mr.getX().toBigInteger().toRadix(16),64),s=$.leftPad(mr.getY().toBigInteger().toRadix(16),64);let a,l;if(t.length===128)a=t.substr(0,64),l=t.substr(64,64);else{const f=mr.curve.decodePointHex(t);a=$.leftPad(f.getX().toBigInteger().toRadix(16),64),l=$.leftPad(f.getY().toBigInteger().toRadix(16),64)}const u=$.hexToArray(n+r+i+o+s+a+l),c=n.length*4;u.unshift(c&255),u.unshift(c>>8&255);const h=Ci(u);return $.arrayToHex(Ci(h.concat($.hexToArray(e))))}function P8(e){const t=mr.multiply(new lt(e,16)),n=$.leftPad(t.getX().toBigInteger().toString(16),64),r=$.leftPad(t.getY().toBigInteger().toString(16),64);return"04"+n+r}function Av(){const e=$.generateKeyPairHex(),t=_v.decodePointHex(e.publicKey);return e.k=new lt(e.privateKey,16),e.x1=t.getX().toBigInteger(),e}var D8={generateKeyPairHex:$.generateKeyPairHex,compressPublicKeyHex:$.compressPublicKeyHex,comparePublicKeyHex:$.comparePublicKeyHex,doEncrypt:C8,doDecrypt:b8,doSignature:T8,doVerifySignature:k8,getPoint:Av,verifyPublicKey:$.verifyPublicKey};const{sm3:M8,hmac:R8}=xv;function O8(e,t){return e.length>=t?e:new Array(t-e.length+1).join("0")+e}function em(e){return e.map(t=>(t=t.toString(16),t.length===1?"0"+t:t)).join("")}function I8(e){const t=[];let n=e.length;n%2!==0&&(e=O8(e,n+1)),n=e.length;for(let r=0;r<n;r+=2)t.push(parseInt(e.substr(r,2),16));return t}function L8(e){const t=[];for(let n=0,r=e.length;n<r;n++){const i=e.codePointAt(n);if(i<=127)t.push(i);else if(i<=2047)t.push(192|i>>>6),t.push(128|i&63);else if(i<=55295||i>=57344&&i<=65535)t.push(224|i>>>12),t.push(128|i>>>6&63),t.push(128|i&63);else if(i>=65536&&i<=1114111)n++,t.push(240|i>>>18&28),t.push(128|i>>>12&63),t.push(128|i>>>6&63),t.push(128|i&63);else throw t.push(i),new Error("input is not supported")}return t}var V8=function(e,t){if(e=typeof e=="string"?L8(e):Array.prototype.slice.call(e),t){if((t.mode||"hmac")!=="hmac")throw new Error("invalid mode");let r=t.key;if(!r)throw new Error("invalid key");return r=typeof r=="string"?I8(r):Array.prototype.slice.call(r),em(R8(e,r))}return em(M8(e))};const kn=0,B8=32,ur=16,Ps=[214,144,233,254,204,225,61,183,22,182,20,194,40,251,44,5,43,103,154,118,42,190,4,195,170,68,19,38,73,134,6,153,156,66,80,244,145,239,152,122,51,84,11,67,237,207,172,98,228,179,28,169,201,8,232,149,128,223,148,250,117,143,63,166,71,7,167,252,243,115,23,186,131,89,60,25,230,133,79,168,104,107,129,178,113,100,218,139,248,235,15,75,112,86,157,53,30,36,14,94,99,88,209,162,37,34,124,59,1,33,120,135,212,0,70,87,159,211,39,82,76,54,2,231,160,196,200,158,234,191,138,210,64,199,56,181,163,247,242,206,249,97,21,161,224,174,93,164,155,52,26,85,173,147,50,48,245,140,177,227,29,246,226,46,130,102,202,96,192,41,35,171,13,83,78,111,213,219,55,69,222,253,142,47,3,255,106,114,109,108,91,81,141,27,175,146,187,221,188,127,17,217,92,65,31,16,90,216,10,193,49,136,165,205,123,189,45,116,208,18,184,229,180,176,137,105,151,74,12,150,119,126,101,185,241,9,197,110,198,132,24,240,125,236,58,220,77,32,121,238,95,62,215,203,57,72],Ds=[462357,472066609,943670861,1415275113,1886879365,2358483617,2830087869,3301692121,3773296373,4228057617,404694573,876298825,1347903077,1819507329,2291111581,2762715833,3234320085,3705924337,4177462797,337322537,808926789,1280531041,1752135293,2223739545,2695343797,3166948049,3638552301,4110090761,269950501,741554753,1213159005,1684763257];function su(e){const t=[];for(let n=0,r=e.length;n<r;n+=2)t.push(parseInt(e.substr(n,2),16));return t}function N8(e){return e.map(t=>(t=t.toString(16),t.length===1?"0"+t:t)).join("")}function F8(e){const t=[];for(let n=0,r=e.length;n<r;n++){const i=e.codePointAt(n);if(i<=127)t.push(i);else if(i<=2047)t.push(192|i>>>6),t.push(128|i&63);else if(i<=55295||i>=57344&&i<=65535)t.push(224|i>>>12),t.push(128|i>>>6&63),t.push(128|i&63);else if(i>=65536&&i<=1114111)n++,t.push(240|i>>>18&28),t.push(128|i>>>12&63),t.push(128|i>>>6&63),t.push(128|i&63);else throw t.push(i),new Error("input is not supported")}return t}function z8(e){const t=[];for(let n=0,r=e.length;n<r;n++)e[n]>=240&&e[n]<=247?(t.push(String.fromCodePoint(((e[n]&7)<<18)+((e[n+1]&63)<<12)+((e[n+2]&63)<<6)+(e[n+3]&63))),n+=3):e[n]>=224&&e[n]<=239?(t.push(String.fromCodePoint(((e[n]&15)<<12)+((e[n+1]&63)<<6)+(e[n+2]&63))),n+=2):e[n]>=192&&e[n]<=223?(t.push(String.fromCodePoint(((e[n]&31)<<6)+(e[n+1]&63))),n++):t.push(String.fromCodePoint(e[n]));return t.join("")}function ni(e,t){return e<<t|e>>>32-t}function Vn(e){return(Ps[e>>>24&255]&255)<<24|(Ps[e>>>16&255]&255)<<16|(Ps[e>>>8&255]&255)<<8|Ps[e&255]&255}function Ms(e){return e^ni(e,2)^ni(e,10)^ni(e,18)^ni(e,24)}function Rs(e){return e^ni(e,13)^ni(e,23)}function U8(e,t,n){const r=new Array(4),i=new Array(4);for(let o=0;o<4;o++)i[0]=e[4*o]&255,i[1]=e[4*o+1]&255,i[2]=e[4*o+2]&255,i[3]=e[4*o+3]&255,r[o]=i[0]<<24|i[1]<<16|i[2]<<8|i[3];for(let o=0,s;o<32;o+=4)s=r[1]^r[2]^r[3]^n[o+0],r[0]^=Ms(Vn(s)),s=r[2]^r[3]^r[0]^n[o+1],r[1]^=Ms(Vn(s)),s=r[3]^r[0]^r[1]^n[o+2],r[2]^=Ms(Vn(s)),s=r[0]^r[1]^r[2]^n[o+3],r[3]^=Ms(Vn(s));for(let o=0;o<16;o+=4)t[o]=r[3-o/4]>>>24&255,t[o+1]=r[3-o/4]>>>16&255,t[o+2]=r[3-o/4]>>>8&255,t[o+3]=r[3-o/4]&255}function j8(e,t,n){const r=new Array(4),i=new Array(4);for(let o=0;o<4;o++)i[0]=e[0+4*o]&255,i[1]=e[1+4*o]&255,i[2]=e[2+4*o]&255,i[3]=e[3+4*o]&255,r[o]=i[0]<<24|i[1]<<16|i[2]<<8|i[3];r[0]^=2746333894,r[1]^=1453994832,r[2]^=1736282519,r[3]^=2993693404;for(let o=0,s;o<32;o+=4)s=r[1]^r[2]^r[3]^Ds[o+0],t[o+0]=r[0]^=Rs(Vn(s)),s=r[2]^r[3]^r[0]^Ds[o+1],t[o+1]=r[1]^=Rs(Vn(s)),s=r[3]^r[0]^r[1]^Ds[o+2],t[o+2]=r[2]^=Rs(Vn(s)),s=r[0]^r[1]^r[2]^Ds[o+3],t[o+3]=r[3]^=Rs(Vn(s));if(n===kn)for(let o=0,s;o<16;o++)s=t[o],t[o]=t[31-o],t[31-o]=s}function tm(e,t,n,{padding:r="pkcs#7",mode:i,iv:o=[],output:s="string"}={}){if(i==="cbc"&&(typeof o=="string"&&(o=su(o)),o.length!==128/8))throw new Error("iv is invalid");if(typeof t=="string"&&(t=su(t)),t.length!==128/8)throw new Error("key is invalid");if(typeof e=="string"?n!==kn?e=F8(e):e=su(e):e=[...e],(r==="pkcs#5"||r==="pkcs#7")&&n!==kn){const f=ur-e.length%ur;for(let v=0;v<f;v++)e.push(f)}const a=new Array(B8);j8(t,a,n);const l=[];let u=o,c=e.length,h=0;for(;c>=ur;){const f=e.slice(h,h+16),v=new Array(16);if(i==="cbc")for(let p=0;p<ur;p++)n!==kn&&(f[p]^=u[p]);U8(f,v,a);for(let p=0;p<ur;p++)i==="cbc"&&n===kn&&(v[p]^=u[p]),l[h+p]=v[p];i==="cbc"&&(n!==kn?u=v:u=f),c-=ur,h+=ur}if((r==="pkcs#5"||r==="pkcs#7")&&n===kn){const f=l.length,v=l[f-1];for(let p=1;p<=v;p++)if(l[f-p]!==v)throw new Error("padding is invalid");l.splice(f-v,v)}return s!=="array"?n!==kn?N8(l):z8(l):l}var G8={encrypt(e,t,n){return tm(e,t,1,n)},decrypt(e,t,n){return tm(e,t,0,n)}},H8={sm2:D8,sm3:V8,sm4:G8},W8=H8.sm2;function K8(e,t,n){return"04"+W8.doEncrypt(e,t,n)}var $t=function(){function e(t){this.disableAutoPageView=!1,this.bridgeReport=!1,this.staging=!1,this.pluginInstances=[],this.sended=!1,this.started=!1,this.destroyInstance=!1,this.adapters={},this.sdkReady=!1,this.name=t,this.hook=new g4,this.logger=new Wp(t),this.remotePlugin=new Map,this.Types=I,this.adapters.fetch=Fo,this.adapters.storage=Sn}return e.usePlugin=function(t,n,r){if(n){for(var i=!1,o=0,s=e.plugins.length;o<s;o++){var a=e.plugins[o];if(a.name===n){e.plugins[o].plugin=t,e.plugins[o].options=r||{},i=!0;break}}i||e.plugins.push({name:n,plugin:t,options:r})}else e.plugins.push({plugin:t})},e.prototype.init=function(t){var n=this;if(this.inited){console.log("init can be call only one time");return}if(!t||!Bo(t)){console.warn("init params error,please check");return}if(!t.app_id||!v4(t.app_id)){console.warn("app_id param is error, must be number, please check!");return}if(t.app_key&&!y4(t.app_key)){console.warn("app_key param is error, must be string, please check!");return}!t.channel_domain&&["cn","sg","va"].indexOf(t.channel)===-1&&(console.warn("channel must be `cn`, `sg`,`va` !!!"),t.channel="cn"),this.logger=new Wp(this.name,t.log),this.configManager=new K4(this,t),this.appBridge=new q4(t,this.configManager),this.bridgeReport=this.appBridge.bridgeInject(),this.initConfig=t,this.env=t.channel_domain?"self":"public",this.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"SDK 执行INIT",data:t,level:"info",time:Date.now()}),t.disable_auto_pv&&(this.disableAutoPageView=!0),this.bridgeReport?(this.inited=!0,this.emit(I.Init)):(t.enable_encryption&&t.encryption_type==="sm"&&this.initCrypto(),this.configManager.set({app_id:t.app_id}),this.eventManager=new Q4,this.tokenManager=new t8,this.sessionManager=new e8,Promise.all([new Promise(function(r){n.once(I.TokenComplete,function(){r(!0)})}),new Promise(function(r){n.once(I.Start,function(){r(!0)})})]).then(function(){try{e.plugins.reduce(function(r,i){var o=i.plugin,s=i.options,a=Object.assign(n.initConfig,s),l=new o;return l.apply(n,a),r.push(l),r},n.pluginInstances)}catch(r){console.log("load plugin error, "+r.message),n.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:r.message})}n.sdkReady=!0,n.emit(I.Ready),n.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"SDK 初始化完成",time:Date.now(),level:"info",data:n.configManager.get("user")}),n.logger.info("appid: "+t.app_id+", userInfo:"+JSON.stringify(n.configManager.get("user"))),n.logger.info("appid: "+t.app_id+", sdk is ready, version is "+Ac+", you can report now !!!");try{(window.opener||window.parent).postMessage("[tea-sdk]ready","*")}catch(r){n.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:r.message})}n.pageView(),n.on(I.TokenChange,function(r){(r.type==="webid"||r.type==="anonymous_id")&&n.pageView(),n.logger.info("appid: "+t.app_id+" token change, new userInfo:"+JSON.stringify(n.configManager.get("user"))),n.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"SDK 设置了用户信息",time:Date.now(),secType:"USER",level:"info",data:n.configManager.get("user")})}),n.on(I.TokenReset,function(){n.logger.info("appid: "+t.app_id+" token reset, new userInfo:"+JSON.stringify(n.configManager.get("user"))),n.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"SDK 重置了用户信息",time:Date.now(),secType:"USER",level:"info",data:n.configManager.get("user")})}),n.on(I.RouteChange,function(r){r.init||t.disable_route_report||n.pageView()})}),this.tokenManager.apply(this,t),this.eventManager.apply(this,t),this.sessionManager.apply(this,t),this.inited=!0,this.emit(I.Init))},e.prototype.config=function(t){if(!this.inited){this.logger.warn("config must be use after function init");return}if(!t||!Bo(t)){this.logger.warn("config params is error, please check");return}if(this.bridgeReport)this.appBridge.setConfig(t);else{t._staging_flag&&t._staging_flag===1&&(this.staging=!0),t.disable_auto_pv&&(this.disableAutoPageView=!0,delete t.disable_auto_pv);var n=be({},t);if(this.initConfig&&this.initConfig.configPersist){var r=this.configManager.getStore();r&&(n=Object.assign(r,t)),this.configManager.setStore(t)}n.web_id,n.user_unique_id;var i=p4(n,["web_id","user_unique_id"]);n.hasOwnProperty("web_id")&&this.emit(I.ConfigWebId,n.web_id),n.hasOwnProperty("user_unique_id")&&this.emit(I.ConfigUuid,n.user_unique_id),this.configManager.set(i),this.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"SDK 执行CONFIG",level:"info",time:Date.now(),data:n})}},e.prototype.setDomain=function(t){this.configManager&&this.configManager.setDomain(t),this.emit(I.ConfigDomain)},e.prototype.getConfig=function(t){return this.configManager.get(t)},e.prototype.send=function(){this.start()},e.prototype.start=function(){!this.inited||this.sended||(this.sended=!0,this.emit(I.Start),this.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"SDK 执行START",level:"info",time:Date.now()}),this.bridgeReport&&(this.pageView(),this.emit(I.Ready)))},e.prototype.event=function(t,n){var r=this;try{if(this.initConfig&&this.initConfig.disable_track_event)return;var i=[];if(Array.isArray(t))t.forEach(function(s){var a=r.processEvent(s[0],s[1]||{});a&&i.push(a)});else{var o=this.processEvent(t,n);if(!o)return;i.push(o)}this.bridgeReport?i.forEach(function(s){var a=s.event,l=s.params;r.appBridge.onEventV3(a,JSON.stringify(l))}):i.length&&(this.emit(I.Event,i),this.emit(I.SessionResetTime))}catch(s){this.logger.warn("something error, please check"),this.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:s.message})}},e.prototype.beconEvent=function(t,n){if(!(this.initConfig&&this.initConfig.disable_track_event)){if(Array.isArray(t)){console.warn("beconEvent not support batch report, please check");return}var r=[],i=this.processEvent(t,n||{});i&&(r.push(i),r.length&&(this.emit(I.BeconEvent,r),this.emit(I.SessionResetTime)))}},e.prototype.processEvent=function(t,n){n===void 0&&(n={});try{if(!t)return this.logger.warn("eventName is null， please check"),null;var r=/^event\./,i=t;r.test(t)&&(i=t.slice(6));var o=n;typeof o!="object"&&(o={}),o.profile?delete o.profile:o.event_index=w4();var s=void 0;o.local_ms?(s=o.local_ms,delete o.local_ms):s=+new Date;var a={event:i,params:o,local_time_ms:s,is_bav:this.initConfig&&this.initConfig.autotrack?1:0};return a}catch(l){return this.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:l.message}),{event:t,params:n}}},e.prototype.dynamicParams=function(t){this.dynamicParamsFilter=t},e.prototype.filterEvent=function(t){this.eventFilter=t},e.prototype.on=function(t,n){this.hook.on(t,n)},e.prototype.once=function(t,n){this.hook.once(t,n)},e.prototype.off=function(t,n){this.hook.off(t,n)},e.prototype.emit=function(t,n,r){this.hook.emit(t,n,r)},e.prototype.set=function(t){this.hook.set(t)},e.prototype.pageView=function(){this.disableAutoPageView||this.predefinePageView()},e.prototype.predefinePageView=function(t){if(t===void 0&&(t={}),!this.inited){console.warn("predefinePageView should call after init");return}var n={title:document.title||location.pathname,url:location.href,url_path:location.pathname,time:Date.now(),referrer:window.document.referrer,$is_first_time:""+(this.configManager&&this.configManager.is_first_time||!1)},r=be({},n,t);this.event("predefine_pageview",r)},e.prototype.clearEventCache=function(){this.emit(I.CleanEvents)},e.prototype.setWebIDviaUnionID=function(t){if(t){var n=jp(t);this.config({web_id:""+n,wechat_unionid:t}),this.emit(I.CustomWebId)}},e.prototype.setWebIDviaOpenID=function(t){if(t){var n=jp(t);this.config({web_id:""+n,wechat_openid:t}),this.emit(I.CustomWebId)}},e.prototype.setAnonymousId=function(t){this.emit(I.AnonymousId,t)},e.prototype.setNativeAppId=function(t){this.bridgeReport&&this.appBridge.setNativeAppId(t)},e.prototype.resetStayDuration=function(t,n,r){t===void 0&&(t=""),n===void 0&&(n=""),r===void 0&&(r=""),this.emit(I.ResetStay,{url_path:t,title:n,url:r},I.Stay)},e.prototype.resetStayParams=function(t,n,r){t===void 0&&(t=""),n===void 0&&(n=""),r===void 0&&(r=""),this.emit(I.SetStay,{url_path:t,title:n,url:r},I.Stay)},e.prototype.getToken=function(t,n){var r=this;if(!this.inited){this.logger.warn("getToken must be use after function init");return}var i=!1,o=function(l){if(!i){i=!0;var u=r.configManager.get().user;return l&&(u.tobid=l,u["diss".split("").reverse().join("")]=l),t(be({},u))}},s=function(){r.tokenManager.getTobId().then(function(l){o(l)})};if(this.sdkReady){s();return}n&&setTimeout(function(){o()},n),this.on(I.Ready,function(){s()})},e.prototype.startTrackEvent=function(t){t&&this.emit(I.TrackDurationStart,t,I.TrackDuration)},e.prototype.endTrackEvent=function(t,n){n===void 0&&(n={}),t&&this.emit(I.TrackDurationEnd,{eventName:t,params:n},I.TrackDuration)},e.prototype.pauseTrackEvent=function(t){t&&this.emit(I.TrackDurationPause,t,I.TrackDuration)},e.prototype.resumeTrackEvent=function(t){t&&this.emit(I.TrackDurationResume,t,I.TrackDuration)},e.prototype.profileSet=function(t){this.bridgeReport?this.appBridge.profileSet(JSON.stringify(t)):this.emit(I.ProfileSet,t,I.Profile),this.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"SDK 执行profileSet",level:"info",time:Date.now(),data:t})},e.prototype.profileSetOnce=function(t){this.bridgeReport?this.appBridge.profileSetOnce(JSON.stringify(t)):this.emit(I.ProfileSetOnce,t,I.Profile),this.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"SDK 执行profileSetOnce",level:"info",time:Date.now(),data:t})},e.prototype.profileIncrement=function(t){this.bridgeReport?this.appBridge.profileIncrement(JSON.stringify(t)):this.emit(I.ProfileIncrement,t,I.Profile),this.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"SDK 执行profileIncrement",level:"info",time:Date.now(),data:t})},e.prototype.profileUnset=function(t){this.bridgeReport?this.appBridge.profileUnset(t):this.emit(I.ProfileUnset,t,I.Profile),this.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"SDK 执行profileUnset",level:"info",time:Date.now(),data:t})},e.prototype.profileAppend=function(t){this.bridgeReport?this.appBridge.profileAppend(JSON.stringify(t)):this.emit(I.ProfileAppend,t,I.Profile),this.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"SDK 执行profileAppend",level:"info",time:Date.now(),data:t})},e.prototype.setExternalAbVersion=function(t){this.bridgeReport?this.appBridge.setExternalAbVersion(t):this.emit(I.AbExternalVersion,typeof t=="string"&&t?(""+t).trim():null,I.Ab)},e.prototype.getVar=function(t,n,r){this.bridgeReport?this.appBridge.getVar(t,n,r):this.emit(I.AbVar,{name:t,defaultValue:n,callback:r},I.Ab)},e.prototype.getAllVars=function(t){this.bridgeReport?this.appBridge.getAllVars(t):this.emit(I.AbAllVars,t,I.Ab)},e.prototype.getABconfig=function(t,n){this.emit(I.AbConfig,{params:t,callback:n},I.Ab)},e.prototype.getAbSdkVersion=function(t){if(this.bridgeReport&&t)this.appBridge.getAbSdkVersion(t);else return this.configManager.getAbVersion()||""},e.prototype.onAbSdkVersionChange=function(t){var n=this;return this.emit(I.AbVersionChangeOn,t,I.Ab),function(){n.emit(I.AbVersionChangeOff,t,I.Ab)}},e.prototype.offAbSdkVersionChange=function(t){this.emit(I.AbVersionChangeOff,t,I.Ab)},e.prototype.openOverlayer=function(){this.emit(I.AbOpenLayer,"",I.Ab)},e.prototype.closeOverlayer=function(){this.emit(I.AbCloseLayer,"",I.Ab)},e.prototype.initCrypto=function(){this.publicKey=this.initConfig.crypto_publicKey||"04BA9E229380DC0E41E10839B0C52A4763D3EDFE8903F3B8E81395523381E03AA995D295BD4A4088792E4785B224F7837EB4D2C7C05973C7AE8687A35ACAE470A0"},e.prototype.cryptoData=function(t){try{if(!this.initConfig.enable_encryption)return t;var n=void 0;if(this.initConfig.encryption_type==="sm"){var r=K8(JSON.stringify(t),this.publicKey,1),i=A4(r);n=new Uint8Array(i)}else n="data="+_4(JSON.stringify(t));return n}catch{return t}},e.prototype.destoryInstace=function(){this.destroyInstance||(this.destroyInstance=!0,this.off(I.TokenComplete))},e.plugins=[],e}(),bc="__rangers_ab_style__";function q8(){if(!document.getElementById(bc)){var e="body { opacity: 0 !important; }",t=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.id=bc,n.type="text/css",n.styleSheet?n.styleSheet.cssText=e:n.appendChild(document.createTextNode(e)),t.appendChild(n)}}function $8(){var e=document.getElementById(bc);e&&e.parentElement.removeChild(e)}var co;(function(e){e[e.Var=0]="Var",e[e.All=1]="All"})(co||(co={}));var nm=1*30*24*60*60*1e3,Y8="/service/2/abtest_config/",Z8=function(){function e(){this.fetchStatus="no",this.refreshFetchStatus="complete",this.versions=[],this.extVersions=[],this.mulilinkVersions=[],this.enable_multilink=!1,this.enable_ab_visual=!1,this.editMode=!1,this.callbacks=[],this.data=null,this.changeListener=new Map,this.readyStatus=!1,this.exposureCache=[]}return e.prototype.apply=function(t,n){var r=this;if(this.collect=t,this.config=n,!!this.config.enable_ab_test){var i=n.enable_multilink,o=n.ab_channel_domain,s=n.enable_ab_visual,a=n.ab_cross,l=n.ab_cookie_domain,u=n.disable_ab_reset,c=o||uv(B4[n.channel||"cn"]),h=t.adapters,f=h.storage,v=h.fetch;this.cacheStorgae=new f(!1),this.fetch=v,this.enable_multilink=i,this.enable_ab_visual=s,this.abKey="__tea_sdk_ab_version_"+n.app_id,this.ab_cross=a,this.ab_cookie_domain=l||"",this.fetchUrl=""+c+Y8,this.reportUrl=""+t.configManager.getUrl("event"),this.exposureLimit=n.exposure_limit||20,this.ab_batch_time=n.ab_batch_time||500;var p=this.collect.Types;this.types=p,this.collect.on(p.TokenChange,function(g){u||g.type==="uuid"&&r.readyStatus&&(r.clearCache(),r.fetchAB())}),this.collect.on(p.TokenReset,function(){u||r.readyStatus&&(r.clearCache(),r.fetchAB())}),this.collect.on(p.AbVar,function(g){var S=g.name,y=g.defaultValue,w=g.callback;r.getVar(S,y,w)}),this.collect.on(p.AbAllVars,function(g){r.getAllVars(g)}),this.collect.on(p.AbConfig,function(g){var S=g.params,y=g.callback;r.getABconfig(S,y)}),this.collect.on(p.AbExternalVersion,function(g){r.setExternalAbVersion(g)}),this.collect.on(p.AbOpenLayer,function(){r.openOverlayer()}),this.collect.on(p.AbCloseLayer,function(){r.closeOverlayer()}),this.collect.on(p.AbVersionChangeOn,function(g){r.changeListener.set(g,g)}),this.collect.on(p.AbVersionChangeOff,function(g){r.changeListener.get(g)&&r.changeListener.delete(g)}),this.loadMode(),(this.enable_ab_visual||this.enable_multilink)&&this.openOverlayer(this.config.multilink_timeout_ms||500),this.checkLocal(),this.ready("ab"),this.readyStatus||(this.fetchAB(),this.readyStatus=!0),this.collect.emit(p.AbReady)}},e.prototype.ready=function(t){var n=this;if(this.collect.set(t),this.collect.hook._hooksCache.hasOwnProperty(t)){var r=this.collect.hook._hooksCache[t];if(!Object.keys(r).length)return;var i=function(a){r[a].length&&r[a].forEach(function(l){n.collect.hook.emit(a,l)})};for(var o in r)i(o)}},e.prototype.loadMode=function(){},e.prototype.checkLocal=function(){var t=this.getABCache(),n=t.ab_version,r=t.ab_ext_version,i=t.ab_version_multilink,o=t.data,s=this.checkFromUrl();s?this.mulilinkVersions.push(s):this.mulilinkVersions=i||[],this.extVersions=r||[],this.versions=n||[],this.data=o;var a=this.versions.concat(this.extVersions);this.enable_multilink&&(a=a.concat(this.mulilinkVersions)),this.configVersions(a.join(","))},e.prototype.checkFromUrl=function(){var t=No(window.location.href);return t&&t.vid?t.vid:""},e.prototype.updateVersions=function(){var t=this.extVersions.length?this.versions.concat(this.extVersions):this.versions;this.enable_multilink&&(t=t.concat(this.mulilinkVersions)),this.configVersions(t.join(",")),this.updateABCache(),this.changeListener.size>0&&this.changeListener.forEach(function(n){typeof n=="function"&&n(t)})},e.prototype.configVersions=function(t){this.collect.configManager.setAbVersion(t)},e.prototype.getVar=function(t,n,r){if(!t)throw new Error("variable must not be empty");if(n===void 0)throw new Error("variable no default value");if(typeof r!="function")throw new Error("callback must be a function");var i={name:t,defaultValue:n,callback:r,type:co.Var};this.fetchStatus==="complete"&&this.refreshFetchStatus==="complete"?this.getRealVar(i,t):this.callbacks.push(i)},e.prototype.getRealVar=function(t,n){var r=t.name,i=t.defaultValue,o=t.callback,s=this.data;if(!s){o(i);return}if(Bo(s[r])){var a=s[r].vid;n==="$ab_url"?this.mulilinkVersions.includes(a)||this.mulilinkVersions.push(a):this.versions.includes(a)||this.versions.push(a),this.updateVersions(),this.fechEvent(a,n,i),o(s[r].val),this.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,secType:"AB",info:"SDK 曝光了实验"+r,level:"info",time:Date.now(),data:s[r]});return}this.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,secType:"AB",info:"SDK 调用getVar",level:"info",time:Date.now(),data:i}),o(i)},e.prototype.getAllVars=function(t){if(typeof t!="function")throw new Error("callback must be a function");var n={callback:t,type:co.All};this.fetchStatus==="complete"&&this.refreshFetchStatus==="complete"?this.getRealAllVars(n):this.callbacks.push(n)},e.prototype.getRealAllVars=function(t){var n=t.callback;n(this.data?JSON.parse(JSON.stringify(this.data)):{}),this.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,secType:"AB",info:"SDK 调用getAllVars",level:"info",time:Date.now(),data:this.data})},e.prototype.fechEvent=function(t,n,r){try{if(this.config.disable_track_event||!t)return;var i=this.collect.configManager.get(),o=i.header,s=i.user,a=this.getABCache();if(a&&a.uuid&&a.uuid!==s.user_unique_id)return;var l={event:"abtest_exposure",ab_sdk_version:""+t,params:JSON.stringify({app_id:this.config.appId,ab_url:n==="$ab_url"?r:window.location.href}),local_time_ms:Date.now()};o.custom=JSON.stringify(o.custom);var u={events:[l],user:s,header:o};n==="$ab_url"?window.navigator.sendBeacon?window.navigator.sendBeacon(this.reportUrl,JSON.stringify([u])):this.reportExposure(u):this.reportExposure(u)}catch(c){this.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:c.message})}},e.prototype.reportExposure=function(t){var n=this;this.exposureCache.push(t),this.reportTimeout&&clearTimeout(this.reportTimeout),this.exposureCache.length>=this.exposureLimit?this.report():this.reportTimeout=setTimeout(function(){n.report(),clearTimeout(n.reportTimeout)},this.ab_batch_time)},e.prototype.report=function(){var t=this,n=this.collect.cryptoData(this.exposureCache);this.fetch(this.reportUrl,n,1e5,!1,function(){},function(){},"","POST",this.config.enable_encryption,this.config.encryption_header),this.exposureCache.forEach(function(r){t.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_EVENT,info:"埋点上报成功",time:Date.now(),data:[r],code:200,status:"success"})}),this.exposureCache=[]},e.prototype.setExternalAbVersion=function(t){this.extVersions=[t],this.updateVersions()},e.prototype.getABconfig=function(t,n){var r=Object.keys(t);r&&r.length&&this.collect.configManager.set(t),this.fetchAB(n)},e.prototype.get=function(t){if(this.ab_cross){var n=this.cacheStorgae.getCookie(t,this.ab_cookie_domain);return n?JSON.parse(n):null}else return this.cacheStorgae.getItem(t)},e.prototype.set=function(t,n){this.ab_cross?this.cacheStorgae.setCookie(t,n,nm,this.ab_cookie_domain):this.cacheStorgae.setItem(t,n),this.collect.configManager.setAbCache(n)},e.prototype.getABCache=function(t){var n={ab_version:[],ab_ext_version:[],ab_version_multilink:[],data:null,timestamp:+new Date,uuid:""};return n=this.get(this.abKey)||n,Date.now()-n.timestamp>=nm?(this.cacheStorgae.removeItem(this.abKey),null):t?n[t]:n},e.prototype.updateABCache=function(){var t=this.getABCache();t.ab_version_multilink=this.mulilinkVersions,t.ab_ext_version=this.extVersions,t.ab_version=this.versions,t.timestamp=Date.now(),this.set(this.abKey,t)},e.prototype.setAbCache=function(t){var n=this.getABCache();n.data=this.data,n.uuid=t,n.timestamp=Date.now(),this.set(this.abKey,n)},e.prototype.clearCache=function(){this.refreshFetchStatus="ing",this.data={},this.extVersions=[],this.mulilinkVersions=[],this.versions=[],this.collect.configManager.clearAbCache()},e.prototype.openOverlayer=function(t){var n=this;if(q8(),t)var r=setTimeout(function(){n.closeOverlayer(),clearTimeout(r)},t)},e.prototype.closeOverlayer=function(){$8()},e.prototype.fetchComplete=function(t,n){var r=this;if(t&&Object.prototype.toString.call(t)=="[object Object]"){this.data=t,this.setAbCache(n);var i=[];Object.keys(t).forEach(function(u){var c=t[u].vid;c&&i.push(c)}),this.versions=this.versions.filter(function(u){return i.includes(u)});var o=t.$ab_url,s=t.$ab_modification;if(s&&s.val&&this.enable_ab_visual){if(this.collect.destroyInstance)return;this.getVar("$ab_modification",window.location.href,function(){r.closeOverlayer()})}else if(o&&this.enable_multilink){this.mulilinkVersions=this.mulilinkVersions.filter(function(u){return i.includes(u)});var a=o.val,l=o.vid;a&&l&&this.getVar("$ab_url",a,function(){r.editMode||(a!==window.location.href?setTimeout(function(){if(!r.collect.destroyInstance){var u=""+a;u=u.indexOf("http")===-1?"https://"+u:u;var c=Qf(u).host;try{var h=new URL(u);c!==location.host?h.searchParams.append("vid",l):window.history.replaceState("","",u),window.location.href=h.href}catch{window.location.href=u}}},100):r.closeOverlayer())})}this.updateVersions()}else this.closeOverlayer();this.callbacks.forEach(function(u){return r[u.type===co.Var?"getRealVar":"getRealAllVars"](u,"")}),this.callbacks=[]},e.prototype.fetchAB=function(t){var n=this,r=this.collect.configManager.get(),i=r.header,o=r.user;this.config.enable_anonymousid&&delete o.web_id;var s=o.user_unique_id,a={header:be({aid:this.config.app_id},o||{},i||{},{ab_sdk_version:this.collect.configManager.getAbVersion(),ab_url:window.location.href})};this.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"SDK 发起AB实验请求",level:"info",logType:"fetch",time:Date.now(),data:a});var l=this.collect.cryptoData(a);this.fetch(this.fetchUrl,l,this.config.ab_timeout||3e3,!1,function(u){n.fetchStatus="complete",n.refreshFetchStatus="complete";var c=u.data,h=u.message;h==="success"?(n.fetchComplete(c,s),t&&t(c)):(n.fetchComplete(null,s),t&&t(null)),n.collect.emit(n.types.AbComplete,c),n.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,secType:"AB",info:"AB实验请求成功",level:"info",logType:"fetch",time:Date.now(),data:c})},function(){n.fetchStatus="complete",n.refreshFetchStatus="complete",n.fetchComplete(null,s),t&&t(null),n.collect.emit(n.types.AbTimeout),n.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,secType:"AB",info:"AB实验请求网络异常",level:"error",logType:"fetch",time:Date.now()})},"","",this.config.enable_encryption,this.config.encryption_header)},e.prototype.filterUrl=function(t){try{var n="";t.indexOf("&multilink=true")!==-1?n="&multilink=true[\0-ÿ]*":t.indexOf("?multilink=true")!==-1&&(n="\\?multilink=true[\0-ÿ]*");var r=new RegExp(n,"g");t=t.replace(r,"")}catch{}return t},e}(),X8=function(){function e(t,n){this.maxDuration=12*60*60*1e3,this.aliveDTime=60*1e3,this.options={aliveName:"predefine_page_alive",params:{}},this.collect=t,this.config=n,this.pageStartTime=Date.now(),this.sessionStartTime=this.pageStartTime,this.timerHandler=null,Bo(n.enable_stay_duration)&&(this.options=Object.assign(this.options,n.enable_stay_duration))}return e.prototype.setParams=function(t,n,r){this.set_path=t,this.set_url=r,this.set_title=n},e.prototype.resetParams=function(t,n,r){this.url_path=t,this.url=r,this.title=n},e.prototype.enable=function(t,n,r){this.url_path=t||this.url_path,this.url=r||this.url,this.title=n||this.title,this.disableCallback=this.enablePageAlive(),this.options.params instanceof Function?this.customParmas=this.options.params():this.customParmas=this.options.params},e.prototype.disable=function(){this.disableCallback(),this.pageStartTime=Date.now()},e.prototype.sendEvent=function(t,n){n===void 0&&(n=!1);var r=n?this.aliveDTime:Date.now()-this.sessionStartTime;r<0||r>this.aliveDTime||Date.now()-this.pageStartTime>this.maxDuration||(this.collect.beconEvent(this.options.aliveName,be({url_path:this.getParams("url_path"),title:this.getParams("title"),url:this.getParams("url"),duration:r,is_support_visibility_change:cv(),startTime:this.sessionStartTime,hidden:document.visibilityState,leave:t},this.customParmas)),this.sessionStartTime=Date.now(),this.resetParams(location.pathname,document.title,location.href))},e.prototype.getParams=function(t){switch(t){case"url_path":return this.set_path||this.url_path||location.pathname;case"title":return this.set_title||this.title||document.title||location.pathname;case"url":return this.set_url||this.url||location.href}},e.prototype.setUpTimer=function(){var t=this;return this.timerHandler&&clearInterval(this.timerHandler),setInterval(function(){Date.now()-t.sessionStartTime>t.aliveDTime&&t.sendEvent(!1,!0)},1e3)},e.prototype.visibilitychange=function(){document.visibilityState==="hidden"?this.timerHandler&&(clearInterval(this.timerHandler),this.sendEvent(!1)):document.visibilityState==="visible"&&(this.sessionStartTime=Date.now(),this.timerHandler=this.setUpTimer())},e.prototype.beforeunload=function(){document.hidden||this.sendEvent(!0)},e.prototype.enablePageAlive=function(){var t=this;this.timerHandler=this.setUpTimer();var n=this.visibilitychange.bind(this),r=this.beforeunload.bind(this);return document.addEventListener("visibilitychange",n),window.addEventListener("pagehide",r),function(){t.beforeunload(),document.removeEventListener("visibilitychange",n),window.removeEventListener("beforeunload",r),window.removeEventListener("pagehide",r)}},e}(),Q8=function(){function e(t,n){var r=this;this.maxDuration=12*60*60*1e3,this.aliveDTime=60*1e3,this.options={closeName:"predefine_page_close",params:{}},this.visibilitychange=function(){document.visibilityState==="hidden"?r.activeEndTime=Date.now():document.visibilityState==="visible"&&(r.activeEndTime&&(r.totalTime+=r.activeEndTime-r.activeStartTime,r.activeTimes+=1),r.activeEndTime=void 0,r.activeStartTime=Date.now())},this.beforeunload=function(){if(r.totalTime+=(r.activeEndTime||Date.now())-r.activeStartTime,r.config.autotrack){var i="_tea_cache_duration";try{var o=window.sessionStorage;o.setItem(i,JSON.stringify({duration:r.totalTime,page_title:document.title||location.pathname}))}catch{}}r.sendEventPageClose()},this.collect=t,this.config=n,this.maxDuration=n.maxDuration||24*60*60*1e3,this.pageStartTime=Date.now(),Bo(n.enable_stay_duration)&&(this.options=Object.assign(this.options,n.enable_stay_duration)),this.resetData()}return e.prototype.setParams=function(t,n,r){this.set_path=t,this.set_url=r,this.set_title=n},e.prototype.resetParams=function(t,n,r){this.url_path=t,this.url=r,this.title=n},e.prototype.enable=function(t,n,r){this.url_path=t||this.url_path,this.url=r||this.url,this.title=n||this.title,this.disableCallback=this.enablePageClose()},e.prototype.disable=function(){this.disableCallback()},e.prototype.resetData=function(){this.activeStartTime=this.activeStartTime===void 0?this.pageStartTime:Date.now(),this.activeEndTime=void 0,this.activeTimes=1,this.totalTime=0,this.options.params instanceof Function?this.customParmas=this.options.params():this.customParmas=this.options.params,this.resetParams(location.pathname,document.title,location.href)},e.prototype.sendEventPageClose=function(){var t=Date.now()-this.pageStartTime;this.totalTime<0||t<0||this.totalTime>=this.maxDuration||(this.collect.beconEvent(this.options.closeName,be({url_path:this.getParams("url_path"),title:this.getParams("title"),url:this.getParams("url"),active_times:this.activeTimes,duration:this.totalTime,total_duration:t,is_support_visibility_change:cv()},this.customParmas)),this.pageStartTime=Date.now(),this.resetData())},e.prototype.getParams=function(t){switch(t){case"url_path":return this.set_path||this.url_path||location.pathname;case"title":return this.set_title||this.title||document.title||location.pathname;case"url":return this.set_url||this.url||location.href}},e.prototype.enablePageClose=function(){var t=this,n=this.visibilitychange.bind(this),r=this.beforeunload.bind(this);return document.addEventListener("visibilitychange",n),window.addEventListener("pagehide",r),function(){t.beforeunload(),document.removeEventListener("visibilitychange",n),window.removeEventListener("beforeunload",r),window.removeEventListener("pagehide",r)}},e}(),J8=function(){function e(){}return e.prototype.apply=function(t,n){var r=this;if(this.collect=t,this.config=n,!!this.config.enable_stay_duration){this.title=document.title||location.pathname,this.url=location.href,this.url_path=location.pathname,this.pageAlive=new X8(t,n),this.pageClose=new Q8(t,n);var i=this.collect.Types;this.collect.on(i.ResetStay,function(o){var s=o.url_path,a=o.title,l=o.url;r.resetStayDuration(s,a,l)}),this.collect.on(i.RouteChange,function(o){o.init||n.disable_route_report||r.resetStayDuration()}),this.collect.on(i.SetStay,function(o){var s=o.url_path,a=o.title,l=o.url;r.setStayParmas(s,a,l)}),this.enable(this.url_path,this.title,this.url),this.ready(i.Stay),this.collect.emit(i.StayReady)}},e.prototype.ready=function(t){var n=this;if(this.collect.set(t),this.collect.hook._hooksCache.hasOwnProperty(t)){var r=this.collect.hook._hooksCache[t];if(!Object.keys(r).length)return;var i=function(a){r[a].length&&r[a].forEach(function(l){n.collect.hook.emit(a,l)})};for(var o in r)i(o)}},e.prototype.enable=function(t,n,r){this.pageAlive.enable(t,n,r),this.pageClose.enable(t,n,r)},e.prototype.disable=function(){this.pageAlive.disable(),this.pageClose.disable()},e.prototype.setStayParmas=function(t,n,r){t===void 0&&(t=""),n===void 0&&(n=""),r===void 0&&(r=""),this.pageAlive.setParams(t,n,r),this.pageClose.setParams(t,n,r),this.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"SDK 执行 resetStayParams",level:"info",time:Date.now(),data:{url_path:t,title:n,url:r}})},e.prototype.reset=function(t,n,r){this.disable(),this.enable(t,n,r)},e.prototype.resetStayDuration=function(t,n,r){this.reset(t,n,r),this.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"SDK 执行 resetStayDuration",level:"info",time:Date.now(),data:{url_path:t,title:n,url:r}})},e}(),e9=function(){function e(){}return e.prototype.apply=function(t,n){var r=this;this.collect=t,this.config=n,this.duration=60*1e3,this.reportUrl=t.configManager.getDomain()+"/profile/list";var i=t.Types,o=t.adapters.fetch;this.eventCheck=new Jf(t,n),this.fetch=o,this.cache={},this.collect.on(i.ProfileSet,function(s){r.setProfile(s)}),this.collect.on(i.ProfileSetOnce,function(s){r.setOnceProfile(s)}),this.collect.on(i.ProfileUnset,function(s){r.unsetProfile(s)}),this.collect.on(i.ProfileIncrement,function(s){r.incrementProfile(s)}),this.collect.on(i.ProfileAppend,function(s){r.appendProfile(s)}),this.collect.on(i.ProfileClear,function(){r.cache={}}),this.ready(i.Profile)},e.prototype.ready=function(t){var n=this;if(this.collect.set(t),this.collect.hook._hooksCache.hasOwnProperty(t)){var r=this.collect.hook._hooksCache[t];if(!Object.keys(r).length)return;var i=function(a){r[a].length&&r[a].forEach(function(l){n.collect.hook.emit(a,l)})};for(var o in r)i(o)}},e.prototype.report=function(t,n){n===void 0&&(n={});try{if(this.config.disable_track_event)return;var r=[];r.push(this.collect.processEvent(t,n));var i=this.collect.eventManager.merge(r,!0),o=this.collect.cryptoData(i),s=this.collect.configManager.getUrl("profile");this.fetch(s,o,1e5,!1,function(){},function(){},"","POST",this.config.enable_encryption,this.config.encryption_header),this.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_EVENT,info:"埋点上报成功",time:Date.now(),data:i,code:200,status:"success"})}catch(a){this.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:a.message})}},e.prototype.setProfile=function(t){var n=this.formatParams(t);!n||!Object.keys(n).length||(this.pushCache(n),this.report("__profile_set",be({},n,{profile:!0})))},e.prototype.setOnceProfile=function(t){var n=this.formatParams(t,!0);!n||!Object.keys(n).length||(this.pushCache(n),this.report("__profile_set_once",be({},n,{profile:!0})))},e.prototype.incrementProfile=function(t){if(!t){console.warn("please check the params, must be object!!!");return}this.report("__profile_increment",be({},t,{profile:!0}))},e.prototype.unsetProfile=function(t){if(!t){console.warn("please check the key, must be string!!!");return}var n={};n[t]="1",this.report("__profile_unset",be({},n,{profile:!0}))},e.prototype.appendProfile=function(t){if(!t){console.warn("please check the params, must be object!!!");return}var n={};for(var r in t)if(typeof t[r]!="string"&&Object.prototype.toString.call(t[r]).slice(8,-1)!=="Array"){console.warn("please check the value of param: "+r+", must be string or array !!!");continue}else n[r]=t[r];var i=Object.keys(n);i.length&&this.report("__profile_append",be({},n,{profile:!0}))},e.prototype.pushCache=function(t){var n=this;Object.keys(t).forEach(function(r){n.cache[r]={val:n.clone(t[r]),timestamp:Date.now()}})},e.prototype.formatParams=function(t,n){var r=this;n===void 0&&(n=!1);try{if(!t||Object.prototype.toString.call(t)!=="[object Object]"){console.warn("please check the params type, must be object !!!");return}var i={};for(var o in t)if(typeof t[o]=="string"||typeof t[o]=="number"||Object.prototype.toString.call(t[o]).slice(8,-1)==="Array")i[o]=t[o];else{console.warn("please check the value of params:"+o+", must be string,number,Array !!!");continue}var s=Object.keys(i);if(!s.length)return;var a=Date.now();return s.filter(function(l){var u=r.cache[l];return n?!u:!(u&&r.compare(u.val,t[l])&&a-u.timestamp<r.duration)}).reduce(function(l,u){return l[u]=i[u],l},{})}catch(l){this.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:l.message}),console.log("error")}},e.prototype.compare=function(t,n){try{return JSON.stringify(t)===JSON.stringify(n)}catch(r){return this.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:r.message}),!1}},e.prototype.clone=function(t){try{return JSON.parse(JSON.stringify(t))}catch(n){return this.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:n.message}),t}},e.prototype.unReady=function(){console.warn("sdk is not ready, please use this api after start")},e}(),t9=function(){function e(){var t=this;this.setInterval=function(){t.clearIntervalFunc=S4(function(){t.isSessionhasEvent&&t.endCurrentSession()},t.sessionInterval)},this.clearInterval=function(){t.clearIntervalFunc&&t.clearIntervalFunc()}}return e.prototype.apply=function(t,n){var r=this;if(this.collect=t,!n.disable_heartbeat){this.sessionInterval=60*1e3,this.startTime=0,this.lastTime=0,this.setInterval();var i=this.collect.Types;this.collect.on(i.SessionReset,function(){r.process()})}},e.prototype.endCurrentSession=function(){this.collect.event("_be_active",{start_time:this.startTime,end_time:this.lastTime,url:window.location.href,referrer:window.document.referrer,title:document.title||location.pathname}),this.isSessionhasEvent=!1,this.startTime=0},e.prototype.process=function(){this.isSessionhasEvent||(this.isSessionhasEvent=!0,this.startTime=+new Date);var t=this.lastTime||+new Date;this.lastTime=+new Date,this.lastTime-t>this.sessionInterval&&(this.clearInterval(),this.endCurrentSession(),this.setInterval())},e}(),n9=function(){function e(){}return e.prototype.apply=function(t,n){var r=this;if(this.collect=t,this.config=n,!this.config.channel_domain&&!(this.config.disable_track_event||this.config.disable_sdk_monitor)){var i=t.adapters.fetch;this.fetch=i,this.url=t.configManager.getUrl("event");var o=this.collect.Types;this.collect.on(o.Ready,function(){r.sdkOnload()}),this.collect.on(o.SubmitError,function(s){var a=s.type,l=s.eventData,u=s.errorCode;a==="f_data"&&r.sdkError(l,u)})}},e.prototype.sdkOnload=function(){var t=this;try{var n=this.collect.configManager.get(),r=n.header,i=n.user,o=r.app_id,s=r.app_name,a=r.sdk_version,l=i.web_id,u={event:"onload",params:JSON.stringify({app_id:o,app_name:s||"",sdk_version:a,sdk_type:V4,sdk_config:this.config,sdk_desc:"TOB"}),local_time_ms:Date.now()},c={events:[u],user:{user_unique_id:l},header:{}};setTimeout(function(){t.fetch(t.url,[c],3e4,!1,function(){},function(){},"566f58151b0ed37e")},16)}catch{}},e.prototype.sdkError=function(t,n){var r=this;try{var i=t[0],o=i.user,s=i.header,a=[];t.forEach(function(c){c.events.forEach(function(h){a.push(h)})});var l=a.map(function(c){return{event:"on_error",params:JSON.stringify({error_code:n,app_id:s.app_id,app_name:s.app_name||"",error_event:c.event,sdk_version:s.sdk_version,local_time_ms:c.local_time_ms,tea_event_index:Date.now(),params:c.params,header:JSON.stringify(s),user:JSON.stringify(o)}),local_time_ms:Date.now()}}),u={events:l,user:{user_unique_id:o.user_unique_id},header:{}};setTimeout(function(){r.fetch(r.url,[u],3e4,!1,function(){},function(){},"566f58151b0ed37e")},16)}catch{}},e}();function eh(e,t){if(t===void 0&&(t="list"),!e)return!1;if(t&&t==="list"){if(["LI","TR","DL"].includes(e.nodeName)||e.dataset&&e.dataset.hasOwnProperty("teaIdx")||e.hasAttribute&&e.hasAttribute("data-tea-idx"))return!0}else if(["A","BUTTON"].includes(e.nodeName)||e.dataset&&e.dataset.hasOwnProperty("teaContainer")||e.hasAttribute&&e.hasAttribute("data-tea-container")||e.hasAttribute&&bv(e,"ss"))return!0;return!1}var Tc=function(t,n){return!!bv(t,n)};function Cv(e){for(var t=e;t&&!eh(t,"container");){if(t.nodeName==="HTML"||t.nodeName==="BODY")return e;t=t.parentElement}return t||e}function r9(e){var t="";return e.nodeType===3?t=e.textContent.trim():e.dataset&&e.dataset.hasOwnProperty("teaTitle")||e.hasAttribute("ata-tea-title")?t=e.getAttribute("data-tea-title"):e.hasAttribute("title")?t=e.getAttribute("title"):e.nodeName==="INPUT"&&["button","submit"].includes(e.getAttribute("type"))?t=e.getAttribute("value"):e.nodeName==="IMG"&&e.getAttribute("alt")&&(t=e.getAttribute("alt")),t.slice(0,200)}function i9(e){var t=Cv(e),n=[];return function r(i){var o=r9(i);if(o&&n.indexOf(o)===-1&&n.push(o),i.childNodes.length>0)for(var s=i.childNodes,a=0;a<s.length;a++)s[a].nodeType!==8&&r(s[a])}(t),n}var o9=function(t){if(!t)return"";var n="";return t.textContent?n=t.textContent.trim():t.innerText&&(n=t.innerText.trim()),(t.tagName==="input"||t.tagName==="INPUT")&&(n=t.value||""),n};function rm(e){for(var t=e;t&&t.parentNode;){if(t.hasAttribute("data-tea-ignore"))return!0;if(t.nodeName==="HTML"||t.nodeName==="body")return!1;t=t.parentNode}return!1}var Da=function(t,n){if(t.hasAttribute)return t.hasAttribute(n);if(t.attributes)return!!(t.attributes[n]&&t.attributes[n].specified)},bv=function(t,n){if(typeof n=="string")return Da(t,n);if(Zf(n)){for(var r=!1,i=0;i<n.length;i++){var o=Da(t,n[i]);if(o){r=!0;break}}return r}},im=function(t,n){var r={};if(typeof n=="string")Da(t,n)&&(r[n]=t.getAttribute(n));else if(Zf(n))for(var i=0;i<n.length;i++){var o=Da(t,n[i]);o&&(r[n[i]]=t.getAttribute(n[i]))}return r},s9=function(t){if(t.children.length){var n=t.children;return![].slice.call(n).some(function(r){return r.children.length>0})}return!0},a9=function(t){if(t.tagName.toLowerCase()==="svg")return!0;for(var n=t.parentElement,r=!1;n;)n.tagName.toLowerCase()==="svg"?(n=null,r=!0):n=n.parentElement;return r};function l9(e,t){if(e.nodeType!==1||!t.svg&&a9(e)||["HTML","BODY"].includes(e.tagName.toUpperCase()))return!1;var n=e;return n.style.display==="none"?!1:eh(n,"container")||t.track_attr&&Tc(n,t.track_attr)?!0:!!s9(n)}var u9=function(){function e(t,n,r){var i=this;this.clickEvent=function(o){l9(o.target,i.options)&&i.eventHandel({eventType:"dom",eventName:"click"},o)},this.changeEvent=function(o){i.eventHandel({eventType:"dom",eventName:"change"},o)},this.submitEvent=function(o){i.eventHandel({eventType:"dom",eventName:"submit"},o)},this.getPageViewEvent=function(o,s){s&&s==="pushState"&&i.eventHandel({eventType:"dom",eventName:"beat"},be({beat_type:0},o)),i.eventHandel({eventType:"dom",eventName:"page_view"},o)},this.getPageLoadEvent=function(o){i.eventHandel({eventType:"dom",eventName:"page_statistics"},{lcp:o})},this.config=r.getConfig().eventConfig,this.collect=n,this.options=t,this.beatTime=t.beat,this.statistics=!1}return e.prototype.init=function(t){this.eventHandel=t;var n=this.config.mode;this.addListener(n)},e.prototype.addListener=function(t){var n=this;if(t==="proxy-capturing"&&(this.config.click&&window.document.addEventListener("click",this.clickEvent,!0),this.config.change&&window.document.addEventListener("change",this.changeEvent,!0),this.config.submit&&window.document.addEventListener("submit",this.submitEvent,!0),this.config.pv&&this.collect.on("route-change",function(u){var c=u.config,h=u.name;n.getPageViewEvent(c,h)}),this.config.beat)){try{document.readyState==="complete"?this.beatEvent(this.beatTime):window.addEventListener("load",function(){n.beatEvent(n.beatTime)});var r=0,i=0,o=null;window.addEventListener("scroll",function(){clearTimeout(o),o=setTimeout(s,500),r=document.documentElement.scrollTop||document.body.scrollTop});var s=function(){i=document.documentElement.scrollTop||document.body.scrollTop,i==r&&n.eventHandel({eventType:"dom",eventName:"beat"},{beat_type:1})}}catch{}try{var a=window.performance&&window.performance.getEntriesByType("paint");if(a&&a.length){var l=new PerformanceObserver(function(u){var c=u.getEntries(),h=c[c.length-1],f=h.renderTime||h.loadTime;n.statistics||(n.getPageLoadEvent(f),n.statistics=!0)});l.observe({entryTypes:["largest-contentful-paint"]}),setTimeout(function(){n.statistics||(n.getPageLoadEvent(a[0].startTime||0),n.statistics=!0)},2e3)}else this.getPageLoadEvent(0)}catch{this.getPageLoadEvent(0)}}},e.prototype.removeListener=function(){window.document.removeEventListener("click",this.clickEvent,!0),window.document.removeEventListener("change",this.changeEvent,!0),window.document.removeEventListener("submit",this.submitEvent,!0)},e.prototype.beatEvent=function(t){var n=this;try{this.eventHandel({eventType:"dom",eventName:"beat"},{beat_type:3});var r;this.beatTime&&(r=setInterval(function(){n.eventHandel({eventType:"dom",eventName:"beat"},{beat_type:2})},t)),Xf(function(){n.eventHandel({eventType:"dom",eventName:"beat",eventSend:"becon"},{beat_type:0}),n.beatTime&&clearInterval(r)})}catch{}},e}(),c9={eventConfig:{mode:"proxy-capturing",submit:!1,click:!0,change:!1,pv:!0,beat:!0,hashTag:!1,impr:!1},scoutConfig:{mode:"xpath"}},f9=function(){function e(t,n){this.config=t,this.config.eventConfig=Object.assign(this.config.eventConfig,n)}return e.prototype.getConfig=function(){return this.config},e.prototype.setConfig=function(t){return this.config=t},e}();function h9(e){if(e){var t=e.getBoundingClientRect(),n=t.width,r=t.height,i=t.left,o=t.top;return{left:i,top:o,element_width:n,element_height:r}}}function d9(e,t){e===void 0&&(e={}),t===void 0&&(t={});var n=e.clientX,r=e.clientY,i=t.left,o=t.top,s=n-i>=0?n-i:0,a=r-o>=0?r-o:0;return{touch_x:Math.floor(s),touch_y:Math.floor(a)}}function p9(e){for(var t=[];e.parentElement!==null;)t.push(e),e=e.parentElement;var n=[],r=[];return t.forEach(function(i){var o=m9(i),s=o.str,a=o.index;n.unshift(s),r.unshift(a)}),{element_path:"/"+n.join("/"),positions:r}}function m9(e){if(e===null)return{str:"",index:0};var t=0,n=e.parentElement;if(n)for(var r=n.children,i=0;i<r.length&&r[i]!==e;i++)r[i].nodeName===e.nodeName&&t++;var o=[e.nodeName.toLowerCase(),eh(e,"list")?"[]":""].join("");return{str:o,index:t}}function g9(e,t,n,r){var i={},o=h9(t),s=d9(e,o),a=o.element_width,l=o.element_height,u=s.touch_x,c=s.touch_y,h=p9(t),f=h.element_path,v=h.positions,p=i9(t),g=window.performance.timing.navigationStart,S=Date.now()-g,y=v.map(function(C){return""+C}),w=null;if(window.TEAVisualEditor&&window.TEAVisualEditor.getOriginXpath&&(w=window.TEAVisualEditor.getOriginXpath({xpath:f,positions:y})),i.element_path=w&&w.xpath||f,i.positions=w&&w.positions||y,r&&!r.text&&(i.texts=p,i.element_title=o9(t)),i.element_id=t.getAttribute("id")||"",i.element_class_name=t.getAttribute("class")||"",i.element_type=t.nodeType,i.element_width=Math.floor(a),i.element_height=Math.floor(l),i.touch_x=u,i.touch_y=c,i.page_manual_key="",i.elememt_manual_key="",i.since_page_start_ms=S,i.page_start_ms=g,i.page_path=location.pathname,i.page_host=location.host,n.track_attr&&Tc(t,n.track_attr)){var _=im(t,n.track_attr);for(var E in _)i[E]=_[E]}if(n.custom_attr)try{if(Tc(t,n.custom_attr)){var _=im(t,n.custom_attr),E=decodeURIComponent(_[n.custom_attr]),b=JSON.parse(E);if(Object.keys(b))for(var k in b)i[k]=b[k]}}catch{console.log("custom attr error")}var P=Cv(t);return P.tagName==="A"&&(i.href=P.getAttribute("href")),n.src!==!1&&t.tagName==="IMG"&&!t.getAttribute("src").includes("base64")&&(i.src=t.getAttribute("src")),i}var Br=function(t,n,r,i,o){return be({event:t},g9(n,r,i,o),{is_html:1,page_key:window.location.href,page_title:document.title})},v9=function(t,n){try{if(t==="bav2b_change")return n.hasAttribute("data-tea-track")?{value:n.value}:{}}catch{return{}}},y9=function(){function e(t,n){this.ignore={text:!1},this.initConfig=t,this.options=n,this.eventName=n&&n.custom==="tea"?{click:"__bav_click",page:"__bav_page",beat:"__bav_beat",static:"__bav_page_statistics",exposure:"__bav_page_exposure"}:{click:"bav2b_click",page:"bav2b_page",beat:"bav2b_beat",static:"bav2b_page_statistics",exposure:"bav2b_exposure",scroll:"$bav2b_slide"},n&&n.text===!1&&(this.ignore.text=!0),n&&n.exposure&&n.exposure.eventName&&(this.eventName.exposure=n.exposure.eventName)}return e.prototype.handleEvent=function(t,n){try{if(rm(t.target))return null;var r="bav2b_click";switch(n){case"click":return r=this.eventName.click,Br(r,t,t.target,this.options,this.ignore);case"exposure":return r=this.eventName.exposure,Br(r,t,t.target,this.options,this.ignore);case"change":return r="bav2b_change",be({},Br(r,t,t.target,this.options),v9(r,t.target));case"submit":return r="bav2b_submit",Br(r,t,t.target,this.options)}}catch(i){return console.error(i),null}},e.prototype.handleViewEvent=function(t){t.event=this.eventName.page,t.page_title=document.title,t.page_total_width=window.innerWidth,t.page_total_height=window.innerHeight;try{var n=window.sessionStorage.getItem("_tea_cache_duration");if(n){var r=JSON.parse(n);t.refer_page_duration_ms=r?r.duration:""}t.scroll_width=document.documentElement.scrollLeft?document.documentElement.scrollLeft+window.innerWidth:window.innerWidth,t.scroll_height=document.documentElement.scrollTop?document.documentElement.scrollTop+window.innerHeight:window.innerHeight,t.page_start_ms=window.performance.timing.navigationStart}catch(i){console.log("page event error "+JSON.stringify(i))}return t},e.prototype.handleStatisticsEvent=function(t){var n={};n.event=this.eventName.static,n.is_html=1,n.page_key=location.href,n.refer_page_key=document.referrer||"",n.page_title=document.title,n.page_manual_key=this.initConfig.autotrack.page_manual_key||"",n.refer_page_manual_key="";try{var r=t.lcp,i=window.performance.timing,o=i.loadEventEnd-i.navigationStart;n.page_init_cost_ms=parseInt(r||(o>0?o:0)),n.page_start_ms=i.navigationStart}catch(s){console.log("page_statistics event error "+JSON.stringify(s))}return n},e.prototype.handleBeadtEvent=function(t){t.event=this.eventName.beat,t.page_key=window.location.href,t.is_html=1,t.page_title=document.title,t.page_manual_key=this.initConfig.autotrack.page_manual_key||"";try{t.page_viewport_width=window.innerWidth,t.page_viewport_height=window.innerHeight,t.page_total_width=document.documentElement.scrollWidth,t.page_total_height=document.documentElement.scrollHeight,t.scroll_width=document.documentElement.scrollLeft+window.innerWidth,t.scroll_height=document.documentElement.scrollTop+window.innerHeight,t.since_page_start_ms=Date.now()-window.performance.timing.navigationStart,t.page_start_ms=window.performance.timing.navigationStart}catch(n){console.log("beat event error "+JSON.stringify(n))}return t},e.prototype.handleExposureEvent=function(t,n){if(rm(n.target))return null;var r=Br(t.event||this.eventName.exposure,n,n.target||n,this.options,this.ignore);if(r.$exposure_type=t.exposureType,this.options.exposure.callback){var i=this.options.exposure.callback(r);return!i&&!Object.keys(i).length?(console.warn("exposure callback must return data!"),r):i}return r},e.prototype.handleScrollEvent=function(t,n){var r=Br(t.event||this.eventName.scroll,n,n.target||n,this.options,this.ignore);if(r=Object.assign(r,t.params),this.options.scroll.callback&&(r=this.options.scroll.callback(r),!r&&!Object.keys(r).length)){console.warn("scroll callback must return data!");return}return r},e}(),w9=function(){function e(t){this.collect=t}return e.prototype.send=function(t,n){var r=n.event;delete n.event,t&&t.eventSend==="becon"?this.collect.beconEvent(r,n):this.collect.event(r,n)},e.prototype.get=function(t,n){var r={headers:{"content-type":"application/json"},method:"GET"},i=Object.assign(r,n);fetch(t,i)},e.prototype.post=function(t,n){var r={headers:{"content-type":"application/json"},method:"POST"},i=Object.assign(r,n);fetch(t,i)},e}(),x9=function(){function e(t,n){this._instance=null,this._intersection=t,this.config=n,this._intersection&&this.init()}return e.prototype.init=function(){var t=this;if(window.MutationObserver)try{if(this._instance=new MutationObserver(function(r){r.forEach(function(i){i.type==="attributes"&&t.attributeChangeObserve(i),i.type==="childList"&&t.modifyNodeObserve(i)})}),!document||!document.body){console.warn("please use sdk api init after body element");return}var n=this.config.autotrack.exposure.attributes!==!1;this._instance.observe(document.body,{childList:!0,attributes:n,subtree:!0,attributeOldValue:!1})}catch{console.log("your browser cannot support MutationObserver，so cannot report exposure event, please update")}else console.log("your browser cannot support MutationObserver，so cannot report exposure event, please update")},e.prototype.attributeChangeObserve=function(t){var n=t.target;n.hasAttribute("data-exposure")?this.exposureAdd(t,"mutation"):this.exposureRemove(t)},e.prototype.modifyNodeObserve=function(t){var n=this;Array.prototype.forEach.call(t.addedNodes,function(r){r.nodeType===1&&r.hasAttribute("data-exposure")&&n.exposureAdd(r,"intersect"),n.mapChild(r,n.exposureAdd.bind(n))}),Array.prototype.forEach.call(t.removedNodes,function(r){r.nodeType===1&&r.hasAttribute("data-exposure")&&n.exposureRemove(r),n.mapChild(r,n.exposureRemove.bind(n))})},e.prototype.mapChild=function(t,n){var r=this;t.nodeType===1&&t.children.length&&Array.prototype.forEach.call(t.children,function(i){i.nodeType===1&&i.hasAttribute("data-exposure")&&n(i),r.mapChild(i,n)})},e.prototype.exposureAdd=function(t,n){try{this._intersection&&this._intersection.exposureAdd(t,n)}catch(r){console.log("intersection error",JSON.stringify(r.message))}},e.prototype.exposureRemove=function(t){try{this._intersection&&this._intersection.exposureRemove(t)}catch(n){console.log("intersection error",JSON.stringify(n.message))}},e._exposure_observer=null,e}(),Et;(function(e){e[e.EXPOSURE_ONCE=0]="EXPOSURE_ONCE",e[e.LIFECYCLE_SHOW_NEW=3]="LIFECYCLE_SHOW_NEW",e[e.RESUME_FORM_PAGE=6]="RESUME_FORM_PAGE",e[e.RESUME_FORM_BACK=7]="RESUME_FORM_BACK",e[e.NOT_EXPOSURE=-1]="NOT_EXPOSURE"})(Et||(Et={}));var _9=function(){function e(t,n,r){var i=this;this.backStatus=!1,this.instance=this.buildObserver(),this.collect=t,this.observeMap=e._observer_map,n.autotrack.exposure.ratio?this.ratio=n.autotrack.exposure.ratio:n.autotrack.exposure.ratio===0?this.ratio=0:this.ratio=.5,this.timeLimit=n.autotrack.exposure.stay||0,this.exposureType=Et.NOT_EXPOSURE,this.eventHandle=r,this.hashMap=new Map,this.backStatus=!1,this.lastState=null,this.collect.on("re-start-sdk",function(){i.visibilitychange()}),this.addListen()}return e.prototype.buildObserver=function(){var t=this;if(e._observer_instance)return console.log("your browser cannot support IntersectionObserver， so cannot report exposure event, please update"),null;if(window.IntersectionObserver){for(var n=[],r=0;r<=1;r+=.01)n.push(r);n.push(1);try{e._observer_instance=new IntersectionObserver(function(i){i.forEach(function(o){var s=o.target.observeId,a=t.observeMap.get(s),l=o.target,u=o.isIntersecting,c=o.intersectionRatio;if(a.intersectionRatio=c,c<=.1&&(o.target.backStatus=!1),t.observeMap.set(s,a),c<t.ratio){a.isIntersecting=!1,a.added=!1,a.exposured=!1,a.startTime=Date.now(),t.observeMap.set(s,a);return}if(!a.added){if(a.startTime=Date.now(),a.added=!0,a.isIntersecting=!0,t.timeLimit){var h=setTimeout(function(){try{if(a.isIntersecting&&!a.exposured&&t.observeMap.get(s)){var f=t.observeMap.get(s).intersectionRatio;t.exposureEvent(o,f,s,a)}}catch(v){console.log("IntersectionObserver setTimeout error，msg: "+JSON.stringify(v))}},t.timeLimit);a.wait=h}t.observeMap.set(s,a)}Date.now()-a.startTime>=t.timeLimit&&!a.exposured&&t.exposureEvent(o,c,s,a)})},{threshold:n})}catch(i){console.log("IntersectionObserver error，msg: "+JSON.stringify(i))}return e._observer_instance}else return console.log("your browser cannot support IntersectionObserver， so cannot report exposure event, please update"),null},e.prototype.exposureAdd=function(t,n){var r=n==="mutation"?t.target:t,i=r.observeId,o=Ys();if(!i&&!this.observeMap.has(i))r.observeId=o,r.visible=!1,this.observeMap.set(o,{instance:r.cloneNode(!0),isIntersecting:!1}),this.observe(r);else{var s=this.observeMap.get(i);if(s&&r!==s.instance)this.unobserve(r),this.observeMap.delete(i),r.observeId=o,r.visible=!1,this.observeMap.set(o,{instance:r.cloneNode(!0),isIntersecting:!1}),this.observe(r);else{if(r.visible===!0)return;this.handleBoundingExposure(r)}}},e.prototype.exposureRemove=function(t){this.observeMap.has(t.observeId)&&(this.observeMap.delete(t.observeId),this.unobserve(t),t.observeId="",t.visible=!1)},e.prototype.exposureEvent=function(t,n,r,i){if(n>=this.ratio){if(t.target.style.opacity==="0"||t.target.style.visibility==="hidden"||i.exposured===!0)return;i.startWait&&clearTimeout(i.startWait);var o=t.target.getAttribute("data-exposure-event");this.eventHandle({eventType:"dom",eventName:"exposure",event:o,exposureType:this.getExposureType(t.target)},t),t.target.visible=!0,t.target.hasExposure=!0,i.startTime=Date.now(),i.exposured=!0}else i.startTime=Date.now(),i.exposured=!1;this.observeMap.set(r,i)},e.prototype.observe=function(t){this.instance&&this.instance.observe(t)},e.prototype.unobserve=function(t){this.instance&&this.instance.unobserve(t)},e.prototype.getExposureDomToExposure=function(){var t=this;Array.prototype.forEach.call(document.querySelectorAll("[data-exposure]"),function(n){t.handleBoundingExposure(n)})},e.prototype.visibilitychange=function(){var t=this,n=null;document.visibilityState==="visible"?this.timeLimit?n=setTimeout(function(){t.getExposureDomToExposure()},this.timeLimit):this.getExposureDomToExposure():(clearTimeout(n),this.customType=void 0)},e.prototype.handleBoundingExposure=function(t){if(this.getBoundingRatio(t)>=this.ratio){t.visible=!0;var n=t.getAttribute&&t.getAttribute("data-exposure-event");this.eventHandle({eventType:"dom",eventName:"exposure",event:n,exposureType:this.customType||Et.RESUME_FORM_BACK},t)}},e.prototype.getBoundingRatio=function(t){var n=t.getBoundingClientRect(),r=n.top,i=n.left,o=n.width,s=n.height,a=window.innerWidth||document.documentElement.clientWidth,l=window.innerHeight||document.documentElement.clientHeight,u=o*s;if(r>=0&&i>=0&&r<=l&&i<=a){var c=o>a?a:o,h=l-r,f=c*h;return f/u}return 0},e.prototype.getExposureType=function(t){return t.visible?t.backStatus!==!1&&(this.backStatus||this.customType||window.performance.navigation.type===2)?this.exposureType=Et.RESUME_FORM_PAGE:this.exposureType=Et.LIFECYCLE_SHOW_NEW:t.hasExposure?t.backStatus!==!1&&(this.backStatus||this.customType||window.performance.navigation.type===2)?this.exposureType=Et.RESUME_FORM_PAGE:this.exposureType=Et.EXPOSURE_ONCE:t.backStatus!==!1&&(this.backStatus||this.customType||window.performance.navigation.type===2)?this.exposureType=Et.RESUME_FORM_PAGE:this.exposureType=Et.EXPOSURE_ONCE,this.exposureType},e.prototype.addListen=function(){var t=this;this.collect.on("set-exposure-type",function(o){t.customType=o});var n=this.visibilitychange.bind(this);if(C4(document,"visibilitychange",n,!1),window.addEventListener("hashchange",function(o){var s=t.hashMap.get(o.oldURL);s&&location.href===s.prev?(t.backStatus=!0,t.exposureType=Et.RESUME_FORM_PAGE):(t.backStatus=!1,t.hashMap.set(location.href,{current:o.newURL,prev:o.oldURL}))}),this.collect.bridgeReport){var r=window.history.pushState;history.pushState=function(o){for(var s=[],a=1;a<arguments.length;a++)s[a-1]=arguments[a];typeof history.onpushstate=="function"&&history.onpushstate({state:o});var l=r.call.apply(r,Mr([history,o],s));return t.lastState=o,l};var i=history.replaceState;history.replaceState=function(o){for(var s=[],a=1;a<arguments.length;a++)s[a-1]=arguments[a];typeof history.onreplacestate=="function"&&history.onreplacestate({state:o});var l=i.call.apply(i,Mr([history,o],s));return t.lastState=o,l}}else this.collect.on("STATE_CHANGE",function(o){t.lastState=o});return window.addEventListener("popstate",function(o){var s=o.state;s&&s.current&&t.lastState&&t.lastState.back&&s.current===t.lastState.back?(t.backStatus=!0,t.exposureType=Et.RESUME_FORM_PAGE):t.backStatus=!1}),function(){b4(document,"visibilitychange",n,!1)}},e._observer_instance=null,e._observer_map=new Map,e}(),S9=function(){function e(t,n,r){!n.autotrack||!n.autotrack.exposure||(this._intersection=new _9(t,n,r),this._observer=new x9(this._intersection,n),this._intersection&&this._observer?this.initObserver():console.log("your browser version cannot support exposure event, please update~"))}return e.prototype.initObserver=function(){var t=this;Array.prototype.forEach.call(document.querySelectorAll("[data-exposure]"),function(n){t._intersection.exposureAdd(n,"intersect")})},e}(),gr;(function(e){e[e.SCROLL_UP=1]="SCROLL_UP",e[e.SCROLL_DOWN=2]="SCROLL_DOWN",e[e.SCROLL_LEFT=3]="SCROLL_LEFT",e[e.SCROLL_RIGHT=4]="SCROLL_RIGHT",e[e.NOT_SCROLL=-1]="NOT_SCROLL"})(gr||(gr={}));var E9=function(){function e(t,n){this.distance=30,!(!t.autotrack||!t.autotrack.scroll)&&(t.autotrack.scroll.distance&&(this.distance=t.autotrack.scroll.distance),this.eventHandle=n,this.addLinstenr(),this.mutation())}return e.prototype.addLinstenr=function(){var t=this;Array.prototype.forEach.call(document.querySelectorAll("[data-scroll]"),function(n){t.scrollHandle(n)})},e.prototype.mutation=function(){var t=this;if(window.MutationObserver)try{var n=new MutationObserver(function(r){r.forEach(function(i){i.type==="childList"&&t.modifyNodeObserve(i)})});if(!document||!document.body){console.warn("please use sdk api init after body element");return}n.observe(document.body,{childList:!0,attributes:!0,subtree:!0,attributeOldValue:!1})}catch{console.log("your browser cannot support MutationObserver")}else console.log("your browser cannot support MutationObserver")},e.prototype.modifyNodeObserve=function(t){var n=this;Array.prototype.forEach.call(t.addedNodes,function(r){r.nodeType===1&&r.hasAttribute("data-scroll")&&n.scrollHandle(r),n.mapChild(r,n.scrollHandle.bind(n))}),Array.prototype.forEach.call(t.removedNodes,function(r){r.nodeType===1&&r.hasAttribute("data-scroll")&&n.scrollHandle(r,"remove"),n.mapChild(r,n.scrollHandle.bind(n))})},e.prototype.mapChild=function(t,n){var r=this;t.nodeType===1&&t.children.length&&Array.prototype.forEach.call(t.children,function(i){i.nodeType===1&&i.hasAttribute("data-scroll")&&n(i),r.mapChild(i,n)})},e.prototype.scrollHandle=function(t,n){var r=this;try{var i=t.scrollTop,o=t.scrollLeft,s=null,a=0,l=0,u=function(h){clearTimeout(s),s=setTimeout(function(){var g=t.scrollTop,S=t.scrollLeft,y=h.target&&h.target.getAttribute("data-scroll-event")||"";p===1||p===2?g===f&&(l=f-i,Math.abs(l)>=r.distance&&r.distance&&r.eventHandle({eventType:"dom",eventName:"scroll",event:y,params:{$direction:p,$offsetY:l,$offsetX:a}},h)):(p===3||p===4)&&S===v&&(a=v-o,Math.abs(a)>=r.distance&&r.distance&&r.eventHandle({eventType:"dom",eventName:"scroll",event:y,params:{$direction:p,$offsetY:l,$offsetX:a}},h)),i=f,o=v},100);var f=t.scrollTop,v=t.scrollLeft,p=gr.NOT_SCROLL;f>i?p=gr.SCROLL_DOWN:f<i?p=gr.SCROLL_UP:v>o?p=gr.SCROLL_LEFT:v<o&&(p=gr.SCROLL_RIGHT)};return n==="remove"?t.removeEventListener("scroll",u,!1):t.addEventListener("scroll",u,!1),function(){t.removeEventListener("scroll",u,!1)}}catch(c){console.warn("scroll event error",JSON.stringify(c))}},e}(),A9={hashTag:!1,impr:!1},C9=function(){function e(){}return e.prototype.apply=function(t,n){if(this.autoTrackStart=!1,this.collect=t,this.config=n,!!n.autotrack&&!(n.autotrack&&n.autotrack.collect_url&&!n.autotrack.collect_url())){var r=t.Types;this.ready(r.Autotrack),this.collect.emit(r.AutotrackReady)}},e.prototype.ready=function(t){this.collect.set(t);var n=this.config.autotrack;n=typeof n=="object"?n:{},n=Object.assign(A9,n),this.destroyed=!1,this.options=n,this.Config=new f9(c9,this.options),this.Exposure=new S9(this.collect,this.config,this.handle.bind(this)),this.Scroll=new E9(this.config,this.handle.bind(this)),this.Listener=new u9(n,this.collect,this.Config),this.EventHandle=new y9(this.config,n),this.Request=new w9(this.collect),this.autoTrackStart=!0,this.init()},e.prototype.init=function(){this.Listener.init(this.handle.bind(this))},e.prototype.handle=function(t,n){try{if(this.config.autotrack.collect_url&&!this.config.autotrack.collect_url())return}catch{}this.collect.sdkStop||t.eventType==="dom"&&this.handleDom(t,n)},e.prototype.handleDom=function(t,n){try{var r=t.eventName;if(r==="click"||r==="change"||r==="submit"){var i=this.EventHandle.handleEvent(n,r);i&&this.Request.send({eventType:"custom"},i)}else if(r==="page_view"||r==="page_statistics"){var o=void 0;r==="page_view"?o=this.EventHandle.handleViewEvent(n):o=this.EventHandle.handleStatisticsEvent(n),o&&this.Request.send({eventType:"custom"},o)}else if(r==="beat"){var s=this.EventHandle.handleBeadtEvent(n),a=t.eventSend;s&&this.Request.send({eventType:"custom",eventSend:a},s)}else if(r==="exposure"){var i=this.EventHandle.handleExposureEvent(t,n);i&&this.Request.send({eventType:"custom"},i)}else if(r==="scroll"){var i=this.EventHandle.handleScrollEvent(t,n);i&&this.Request.send({eventType:"custom"},i)}}catch(l){console.log("handel dom event error "+JSON.stringify(l))}},e.prototype.destroy=function(){if(!this.autoTrackStart)return console.warn("engine is undefined, make sure you have called autoTrack.start()");this.autoTrackStart=!1,this.Listener.removeListener()},e}(),b9=function(){function e(){this.autotrack=!1,this.spa=!1,this.cache={},this.allowHash=!1}return e.prototype.apply=function(t,n){if(!(!n.spa&&!n.autotrack)){var r=t.Types;this.collect=t,this.config=n,this.appid=n.app_id,this.allowHash=n.allow_hash,this.fncArray=new Map,this.setKey(),this.setLocation(),this.hack(),this.init(),this.listener(),t.emit(r.RouteReady)}},e.prototype.setKey=function(){var t=this.collect.adapters.storage;this.storage=new t(!1),this.cache_key="__tea_cache_refer_"+this.appid,this.cache={refer_key:"",refer_title:document.title||location.pathname,refer_manual_key:"",routeChange:!1},this.config.autotrack&&typeof this.config.autotrack=="object"&&this.config.autotrack.page_manual_key&&(this.cache.refer_manual_key=this.config.autotrack.page_manual_key),this.storage.setItem(this.cache_key,this.cache)},e.prototype.hack=function(){var t=this,n=window.history.pushState;history.pushState=function(i){for(var o=[],s=1;s<arguments.length;s++)o[s-1]=arguments[s];typeof history.onpushstate=="function"&&history.onpushstate({state:i});var a=n.call.apply(n,Mr([history,i],o));if(t.lastLocation!==location.href){var l=t.getPopStateChangeEventData();return t.setReferCache(t.lastLocation),t.lastLocation=location.href,t.sendPv(l,"pushState"),a}};var r=history.replaceState;history.replaceState=function(i){for(var o=[],s=1;s<arguments.length;s++)o[s-1]=arguments[s];typeof history.onreplacestate=="function"&&history.onreplacestate({state:i});var a=r.call.apply(r,Mr([history,i],o));if(t.lastLocation!==location.href){var l=t.getPopStateChangeEventData();return t.setReferCache(t.lastLocation),t.lastLocation=location.href,t.sendPv(l),a}}},e.prototype.setLocation=function(){typeof window<"u"&&(this.lastLocation=window.location.href)},e.prototype.getLocation=function(){return this.lastLocation},e.prototype.init=function(){var t=this.getPopStateChangeEventData();this.collect.emit("route-change",{config:t,init:!0})},e.prototype.listener=function(){var t=this,n=null,r=10;window.addEventListener("hashchange",function(i){if(t.lastLocation!==window.location.href&&(clearTimeout(n),t.allowHash)){t.setReferCache(t.lastLocation),t.lastLocation=window.location.href;var o=t.getPopStateChangeEventData();t.sendPv(o)}}),window.addEventListener("popstate",function(i){t.lastLocation!==window.location.href&&(n=setTimeout(function(){t.setReferCache(t.lastLocation),t.lastLocation=window.location.href;var o=t.getPopStateChangeEventData();t.sendPv(o)},r))})},e.prototype.getPopStateChangeEventData=function(){var t=this.pageConfig();return t.is_back=0,t},e.prototype.pageConfig=function(){try{var t=this.storage.getItem(this.cache_key)||{},n=!1,r=this.storage.getItem("__tea_cache_first_"+this.appid);return r&&r==1?n=!1:n=!0,{is_html:1,url:location.href,referrer:this.handleRefer(),page_key:location.href,refer_page_key:this.handleRefer(),page_title:document.title||location.pathname,page_manual_key:this.config.autotrack&&this.config.autotrack.page_manual_key||"",refer_page_manual_key:t&&t.refer_manual_key||"",refer_page_title:t&&t.refer_title||"",page_path:location.pathname,page_host:location.host,is_first_time:""+n}}catch(i){return this.collect.emit(V.DEBUGGER_MESSAGE,{type:V.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:i.message}),{}}},e.prototype.sendPv=function(t,n){this.collect.emit("route-change",{config:t,init:!1})},e.prototype.handleRefer=function(){var t="";try{var n=this.storage.getItem()||{};n.routeChange?t=n.refer_key:t=this.collect.configManager.get("referrer")}catch{t=document.referrer}return t},e.prototype.setReferCache=function(t){var n=this.storage.getItem(this.cache_key)||{};n.refer_key=t,n.routeChange=!0,this.storage.setItem(this.cache_key,n)},e}(),T9=function(){function e(){this.verifyReady=!1,this.cleanStatus=!1}return e.prototype.apply=function(t,n){var r=this;this.collector=t,this.config=n,this.eventStorage=[],this.collector.on("submit-verify-h5",function(i){i&&i.length&&r.eventStore(i[0])}),this.checkUrl(),this.heartbeat()},e.prototype.checkUrl=function(){var t=window.location.href,n=No(t);n._r_d_&&n._r_c_k_?(this.verifyReady=!0,this.domain=n._r_d_,this.key=n._r_c_k_,this.checkCache()):this.collector.off("submit-verify-h5")},e.prototype.checkCache=function(){this.eventStorage.length&&this.postVerify(this.eventStorage)},e.prototype.heartbeat=function(){var t=this;this.heart=setInterval(function(){var n={event:"simulator_test__",local_time_ms:Date.now()},r=t.collector.configManager.get(),i=r.header,o=r.user,s={events:[n],user:o,header:i};t.eventStore(s)},1e3*60)},e.prototype.eventStore=function(t){this.cleanStatus||(this.verifyReady?this.postVerify(t):this.eventStorage.push(t))},e.prototype.cleanVerify=function(){this.cleanStatus=!0,this.eventStorage=[],clearInterval(this.heart)},e.prototype.postVerify=function(t){var n=this;try{var r=JSON.parse(JSON.stringify(t));Zf(t)?r.forEach(function(i){n.fetchLog(i)}):this.fetchLog(r)}catch{console.log("web verify post error ~")}},e.prototype.fetchLog=function(t){Fo(this.domain+"/simulator/h5/log?connection_key="+this.key,t,2e4,!1)},e.prototype.leave=function(){var t=this;document.addEventListener("visibilitychange",function(){document.visibilityState==="hidden"&&t.cleanVerify()}),Xf(function(){t.cleanVerify()})},e}(),k9=function(){function e(){this.retryWaitTime=3e3,this.retryStatus=!1,this.retryCacheStatus=!1}return e.prototype.apply=function(t,n){var r=this;if(!(!n.enable_storage||n.disable_storage)&&(this.collect=t,this.config=n,!this.collect.destroyInstance)){var i=t.Types,o=t.adapters,s=o.storage,a=o.fetch;this.storage=new s(!1),this.fetch=a,this.eventUrl=this.collect.configManager.getUrl("event"),this.eventKey="__tea_cache_events_"+n.app_id,this.storageNum=n.storage_num||50,this.retryNum=n.retry_num||3,this.retryInterval=1e3,t.on(i.SubmitError,function(l){l.type==="f_data"&&r.storeData(l)}),t.on(i.Ready,function(){r.checkStorage()})}},e.prototype.retryRightNow=function(t){var n=this;if(this.retryStatus){this.errorCache.push(t);return}var r=0;this.retryStatus=!0;var i=setInterval(function(){if(r===3){n.storeData(n.errorCache),n.retryStatus=!1,clearInterval(i);return}var o=t.eventData;n.fetchData(o,function(){n.retryStatus=!1,clearInterval(i),n.retryCacheStatus&&n.errorCache.splice(0,1),n.errorCache.length&&(n.retryCacheStatus=!0,n.retryRightNow(n.errorCache[0]))},function(){r++})},this.retryInterval)},e.prototype.storeData=function(t){var n=this.storage.getItem(this.eventKey),r=t.eventData;Object.keys(n).length!==this.storageNum&&(n[Date.now()]=r,this.storage.setItem(this.eventKey,n))},e.prototype.checkStorage=function(){var t=this;try{if(!window.navigator.onLine)return;var n=this.storage.getItem(this.eventKey);if(!n||!Object.keys(n).length)return;var r={events:[{event:"ontest",params:{app_id:this.config.app_id},local_time_ms:Date.now()}],user:{user_unique_id:this.collect.configManager.get("web_id")},header:{}},i=function(){var s=JSON.parse(JSON.stringify(n)),a=function(c){t.fetchData(n[c],function(){delete s[c],t.storage.setItem(t.eventKey,s)},function(){},!1)};for(var l in n)a(l)};this.fetchData([r],i,function(){},!0)}catch{console.warn("error check storage")}},e.prototype.fetchData=function(t,n,r,i){this.fetch(this.eventUrl,t,3e4,!1,function(){n&&n()},function(){r&&r(),console.log("network error，compensate report failk")},i&&!this.config.channel_domain?"566f58151b0ed37e":"")},e}(),P9=function(){function e(){}return e.prototype.apply=function(t,n){var r=this;this.collector=t,this.config=n;var i=t.Types;t.on(i.TrackDurationStart,function(o){r.trackStart(o)}),t.on(i.TrackDurationEnd,function(o){r.trackEnd(o)}),t.on(i.TrackDurationPause,function(o){r.trackPause(o)}),t.on(i.TrackDurationResume,function(o){r.trackResume(o)}),this.Types=i,this.TrackMap=new Map,this.ready(i.TrackDuration)},e.prototype.ready=function(t){var n=this;if(this.collector.set(t),this.collector.hook._hooksCache.hasOwnProperty(t)){var r=this.collector.hook._hooksCache[t];if(!Object.keys(r).length)return;var i=function(a){r[a].length&&r[a].forEach(function(l){n.collector.hook.emit(a,l)})};for(var o in r)i(o)}},e.prototype.trackStart=function(t){this.TrackMap.set(t,{startTime:Date.now(),isPause:!1,pauseTime:0,resumeTime:0})},e.prototype.trackEnd=function(t){var n=t.eventName,r=t.params;if(this.TrackMap.has(n)){var i=this.TrackMap.get(n),o=0;i.isPause?o=i.pauseTime-i.startTime:i.resumeTime?o=i.pauseTime-i.startTime+(Date.now()-i.resumeTime):o=Date.now()-i.startTime;var s=Object.assign(r,{event_duration:o});this.collector.event(n,s),this.cleanTrack(n)}},e.prototype.trackPause=function(t){if(this.TrackMap.has(t)){var n=this.TrackMap.get(t);n.isPause||(n.isPause=!0,n.pauseTime=Date.now(),this.TrackMap.set(t,n))}},e.prototype.trackResume=function(t){if(this.TrackMap.has(t)){var n=this.TrackMap.get(t);n.isPause&&(n.isPause=!1,n.resumeTime=Date.now(),this.TrackMap.set(t,n))}},e.prototype.cleanTrack=function(t){this.TrackMap.delete(t)},e}();$t.usePlugin(Z8,"ab");$t.usePlugin(J8,"stay");$t.usePlugin(k9,"store");$t.usePlugin(C9,"autotrack");$t.usePlugin(P9,"trackDuration");$t.usePlugin(T9,"verify");$t.usePlugin(e9,"profile");$t.usePlugin(t9,"heartbeat");$t.usePlugin(n9,"monitor");$t.usePlugin(b9,"route");var ci=new $t("default");ci.init({app_id:20001731,channel_domain:"https://gator.volces.com"});ci.start();ci.config({platform:pe.platform,app_version:pe.version});function om(e){var t,n,r,i;return{msh_conversation_id:(t=e==null?void 0:e.$chat)==null?void 0:t.id,msh_message_id:e==null?void 0:e.id,msh_message_group_id:e==null?void 0:e.group_id,msh_summary_id:(n=e==null?void 0:e.$summary)==null?void 0:n.id,msh_explore_id:(r=e==null?void 0:e.$explore)==null?void 0:r.id,msh_screenshot_id:(i=e==null?void 0:e.$ocr)==null?void 0:i.id}}const au=av({state:{},actions:()=>({event(e,t){const{env:n}=At.getState();n==="content"?ci.event(e,t):Me.tabs.query({active:!0,currentWindow:!0}).then(([r])=>{Ec.tab(r,async({id:i})=>{Me.tabs.sendMessage(i,{action:ln.tracking_event,payload:{key:e,value:t}})})})},profile(e){const{env:t}=At.getState();t==="content"?ci.profileSet(e):Me.tabs.query({active:!0,currentWindow:!0}).then(([n])=>{Ec.tab(n,async({id:r})=>{Me.tabs.sendMessage(r,{action:ln.tracking_profile,payload:e})})})},async init(){if(await Ei.getToken()){const t=await d4.auth.getUser();ci.config({user_unique_id:t==null?void 0:t.id})}},click(e,t){au.event("msh_click",{...om(t),msh_element_id:e})},explore:{preview(e){au.event("msh_explore_preview",e)}},chat:{send(e){var n;const t=["text"];(n=e==null?void 0:e.$files)!=null&&n.length&&t.push("file"),au.event("msh_sent_message",{...om(e),msh_message_type:t,msh_sender:"user"})}}})});export{Ei as A,au as C,P1 as L,S1 as M,Ef as P,Ly as R,At as a,Me as b,d4 as c,av as d,pe as e,fh as f,M as g,ae as h,I9 as i,fo as j,Vw as k,Ma as l,D9 as m,Y0 as n,sm as o,O9 as p,ln as q,Ql as r,jt as s,q3 as u,Ec as v};
