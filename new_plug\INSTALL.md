# AI Assistant Chrome Extension 安装指南

## 快速开始

### 第一步：获取 OpenRouter API Key

1. 访问 [OpenRouter](https://openrouter.ai/keys)
2. 注册账号（可以使用 Google 账号快速注册）
3. 点击 "Create Key" 创建新的 API Key
4. 复制生成的 API Key（格式类似：`sk-or-v1-...`）
5. 确保账户有足够余额（建议充值 $5-10 用于测试）

### 第二步：安装插件

1. 打开 Chrome 浏览器
2. 在地址栏输入 `chrome://extensions/` 并回车
3. 在右上角开启"开发者模式"开关
4. 点击"加载已解压的扩展程序"按钮
5. 选择 `new_plug` 文件夹
6. 插件安装成功后会出现在扩展列表中

### 第三步：配置 API Key

1. 点击浏览器工具栏中的插件图标
2. 在弹出的窗口中输入您的 OpenRouter API Key
3. 点击"保存"按钮
4. 看到"API Key 保存成功"提示即可

## 基本使用

### 1. 开始对话

- **方法一**：点击插件图标 → 点击"开始对话"
- **方法二**：使用快捷键 `Ctrl+K`
- **方法三**：点击页面右侧的悬浮按钮

### 2. 文本探索

- **方法一**：选择网页上的文字 → 点击出现的工具栏按钮
- **方法二**：使用快捷键 `Ctrl+J` 启动探索模式

### 3. 截图分析

- 点击插件图标 → 点击"截图分析"
- 或在探索模式下直接点击页面

### 4. 页面总结

- 点击插件图标 → 点击"页面总结"

## 设置选项

在插件弹窗中点击设置按钮可以配置：

- **AI 模型**：选择 Gemini、Claude 或 GPT-4o
- **窗口模式**：侧边栏或浮窗模式
- **悬浮按钮**：是否显示页面悬浮按钮
- **文字下划线**：选中文字是否保留下划线

## 快捷键设置

1. 访问 `chrome://extensions/shortcuts`
2. 找到 "AI Assistant" 插件
3. 自定义快捷键组合

## 常见问题

### Q: API Key 无效怎么办？
A: 检查 API Key 是否正确复制，确认 OpenRouter 账户有余额。

### Q: 插件无响应怎么办？
A: 刷新页面或重启浏览器，检查网络连接。

### Q: 如何更换 AI 模型？
A: 在插件设置中选择不同的模型，保存后生效。

### Q: 费用如何计算？
A: 按 OpenRouter 的使用量计费，不同模型价格不同，建议查看官网价格。

## 支持的网站

插件可以在所有网站上使用，包括：
- 新闻网站
- 技术文档
- 社交媒体
- 购物网站
- 学习平台

## 注意事项

1. 首次使用建议在简单页面测试
2. 大量使用前请确认 API 余额充足
3. 插件不会保存您的对话内容
4. 建议定期更新插件获取新功能

## 卸载方法

1. 访问 `chrome://extensions/`
2. 找到 "AI Assistant" 插件
3. 点击"移除"按钮
4. 确认删除

插件数据会自动清除，不会留下残留文件。
