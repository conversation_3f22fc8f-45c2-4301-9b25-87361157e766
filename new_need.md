1. 不要图片左边的圆形图标
2. 历史图标像一个刷新图标换一个
3. 截图分析都看不到截图
4. AI正在思考放在AI回答框的前面

* D:\tmp\image.png

5. 探索模式的那个图标梅花一下，等2秒钟自己消失就行了
6. 现在探索模式也用不起来
7. API KEY不能修改
8. 模型名称只有固定的选择，改为可以手动输入


1. 探索模式的提示框太难看了，背景色和文字太接近，看不清
2. 点击解释后，也没有把文字发送给AI，不管什么模式发送给AI的信息都要在对话框中显示出来
3. 提示框要在点击了鼠标左键之后才消失，现在太容易消失了


1. 我说的是这个提示框太不容易看了

   D:\tmp\image.png
2. 发送消息后出现错误

Failed to execute 'fetch' on 'WorkerGlobalScope': Failed to read the 'headers' property from 'RequestInit': String contains non ISO-8859-1 code point.
