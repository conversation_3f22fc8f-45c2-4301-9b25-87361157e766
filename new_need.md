1. 不要图片左边的圆形图标
2. 历史图标像一个刷新图标换一个
3. 截图分析都看不到截图
4. AI正在思考放在AI回答框的前面

* D:\tmp\image.png

5. 探索模式的那个图标梅花一下，等2秒钟自己消失就行了
6. 现在探索模式也用不起来
7. API KEY不能修改
8. 模型名称只有固定的选择，改为可以手动输入


1. 探索模式的提示框太难看了，背景色和文字太接近，看不清
2. 点击解释后，也没有把文字发送给AI，不管什么模式发送给AI的信息都要在对话框中显示出来
3. 提示框要在点击了鼠标左键之后才消失，现在太容易消失了


1. 我说的是这个提示框太不容易看了

   D:\tmp\image.png
2. 发送消息后出现错误

Failed to execute 'fetch' on 'WorkerGlobalScope': Failed to read the 'headers' property from 'RequestInit': String contains non ISO-8859-1 code point.




1. api key可以切换是否显示，方便查看输入是否出错
2. 不要探索模式已启动这个UI功能，对话框本来就有提示信息了
   D:\tmp\image1.png
3. 这个功能不要写死，要用户可以配置对应的提示词
   标题1：提示词1
   标题2：提示词2
   UI只需要显示提示词部分
   D:\tmp\image.png


1. 显示方式可以在markdown和普通字符串模式之间切换 
   现在markdown模式 会把 a_b_c 两个下划线之间的b显示为斜体，但是不需要显示为斜体
2. AI的回答没有显示完整，是没有等待接收完整，还是显示不完整
3. 探索模式增加提示词的功能，怎么是固定的3个，要能增删，你可以做成一个可以弹出的表格，方便增删
   
1. AI回答内容显示方式 还是没有在markdown和普通字符串模式之间切换 的功能按钮
2. AI的回答没有显示完整，建议使用流式接收，你要检查下是不是超时时间设置太短了

1. markdown的模式切换放到每次AI回答的答案右上角，用一个切换的图标就行，不需要显示文字
2. AI回答的时候，收到什么就立刻输出，不要等到AI回答完成才全部输出，你知道AI回答完整的标志是什么吗


1. 把现在切换markdown模式的按钮放到上面历史记录的前面
2. 下面探索模式的按钮可以不要，不需要点这个，也能在页面使用 解释等功能
3. 截图分析改成用鼠标选址截图范围
4. 一个新的会话保存为历史记录，不是一个会话每次对话后都保存为历史记录
5. 现在加载历史记录，之前发送的图片没有加载出来

1. 导致历史会话
2. 