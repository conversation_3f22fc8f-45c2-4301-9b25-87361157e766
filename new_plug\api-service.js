// OpenRouter API Service
class OpenRouterService {
  constructor() {
    this.apiUrl = CONFIG.OPENROUTER_API_URL;
    this.model = CONFIG.OPENROUTER_MODEL;
    this.apiKey = null;
    this.loadSettings();
  }

  // Validate API key format
  static validateApiKey(apiKey) {
    if (!apiKey || typeof apiKey !== 'string') {
      return { valid: false, error: 'API Key is required' };
    }

    if (!/^[\x00-\x7F]+$/.test(apiKey)) {
      return { valid: false, error: 'API Key contains non-ASCII characters' };
    }

    if (!apiKey.startsWith('sk-or-')) {
      return { valid: false, error: 'Invalid OpenRouter API Key format (should start with sk-or-)' };
    }

    return { valid: true };
  }

  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get([CONFIG.STORAGE_KEYS.SETTINGS]);
      const settings = result[CONFIG.STORAGE_KEYS.SETTINGS] || CONFIG.DEFAULT_SETTINGS;
      this.apiKey = settings.apiKey;
      this.model = settings.model || CONFIG.OPENROUTER_MODEL;
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  }

  async updateApiKey(apiKey) {
    this.apiKey = apiKey;
    const settings = await this.getSettings();
    settings.apiKey = apiKey;
    await chrome.storage.sync.set({
      [CONFIG.STORAGE_KEYS.SETTINGS]: settings
    });
  }

  async getSettings() {
    try {
      const result = await chrome.storage.sync.get([CONFIG.STORAGE_KEYS.SETTINGS]);
      return result[CONFIG.STORAGE_KEYS.SETTINGS] || CONFIG.DEFAULT_SETTINGS;
    } catch (error) {
      console.error('Failed to get settings:', error);
      return CONFIG.DEFAULT_SETTINGS;
    }
  }

  async sendMessage(messages, options = {}) {
    if (!this.apiKey) {
      throw new Error('API key not configured. Please set your OpenRouter API key in the extension settings.');
    }

    // Validate API key contains only ASCII characters
    if (!/^[\x00-\x7F]+$/.test(this.apiKey)) {
      throw new Error('Invalid API key format. API key contains non-ASCII characters.');
    }

    const useStream = options.stream !== false;
    const requestBody = {
      model: this.model,
      messages: messages,
      temperature: options.temperature || 0.7,
      max_tokens: options.maxTokens || 4096,
      stream: useStream
    };

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout

      const response = await fetch(this.apiUrl, {
        method: "POST",
        headers: {
          "Authorization": "Bearer " + this.apiKey,
          "Content-Type": "application/json"
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API request failed: ${response.status} ${response.statusText}. ${errorData.error?.message || ''}`);
      }

      if (useStream) {
        return this.handleStreamResponse(response);
      } else {
        const data = await response.json();

        if (!data.choices || !data.choices[0] || !data.choices[0].message) {
          throw new Error('Invalid response format from API');
        }

        return {
          content: data.choices[0].message.content,
          usage: data.usage,
          model: data.model
        };
      }
    } catch (error) {
      if (error.name === 'AbortError') {
        throw new Error('Request timeout - the API took too long to respond');
      }
      console.error('OpenRouter API error:', error);
      throw error;
    }
  }

  async handleStreamResponse(response) {
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let content = '';
    let usage = null;
    let model = null;

    try {
      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);

            if (data === '[DONE]') {
              return { content, usage, model };
            }

            try {
              const parsed = JSON.parse(data);

              if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta) {
                const delta = parsed.choices[0].delta;
                if (delta.content) {
                  content += delta.content;
                }
              }

              if (parsed.usage) {
                usage = parsed.usage;
              }

              if (parsed.model) {
                model = parsed.model;
              }
            } catch (e) {
              // Skip invalid JSON lines
              continue;
            }
          }
        }
      }

      return { content, usage, model };
    } finally {
      reader.releaseLock();
    }
  }

  async sendTextMessage(text, context = []) {
    const messages = [
      ...context,
      {
        role: "user",
        content: text
      }
    ];

    return await this.sendMessage(messages);
  }

  async sendImageMessage(text, imageUrl, context = []) {
    const messages = [
      ...context,
      {
        role: "user",
        content: [
          {
            type: "text",
            text: text
          },
          {
            type: "image_url",
            image_url: {
              url: imageUrl
            }
          }
        ]
      }
    ];

    return await this.sendMessage(messages);
  }

  async analyzeSelectedText(selectedText, pageContext = "") {
    const prompt = "请解释以下选中的文本内容：\n\n选中文本：" + selectedText + "\n\n" +
                   (pageContext ? "页面上下文：" + pageContext + "\n\n" : "") +
                   "请提供详细的解释和分析。";

    return await this.sendTextMessage(prompt);
  }

  async summarizePage(pageContent) {
    const prompt = "请总结以下网页内容的要点：\n\n" + pageContent + "\n\n请提供简洁明了的总结。";

    return await this.sendTextMessage(prompt);
  }

  async analyzeScreenshot(imageDataUrl, prompt = "请解释这张截图的内容") {
    return await this.sendImageMessage(prompt, imageDataUrl);
  }
}

// Create global instance
const openRouterService = new OpenRouterService();

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = OpenRouterService;
} else if (typeof window !== 'undefined') {
  window.OpenRouterService = OpenRouterService;
  window.openRouterService = openRouterService;
}
