var _background=function(){"use strict";function Qt(r){return typeof r=="function"?{main:r}:r}var Jr=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Ar(r){return r&&r.__esModule&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r}var Qr={exports:{}};(function(r,t){(function(a,i){i(r)})(typeof globalThis<"u"?globalThis:typeof self<"u"?self:Jr,function(a){var i,d;if(!((d=(i=globalThis.chrome)==null?void 0:i.runtime)!=null&&d.id))throw new Error("This script should only be loaded in a browser extension.");if(typeof globalThis.browser>"u"||Object.getPrototypeOf(globalThis.browser)!==Object.prototype){const v="The message port closed before a response was received.",x=b=>{const j={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},getChildren:{minArgs:1,maxArgs:1},getRecent:{minArgs:1,maxArgs:1},getSubTree:{minArgs:1,maxArgs:1},getTree:{minArgs:0,maxArgs:0},move:{minArgs:2,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeTree:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}},browserAction:{disable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},enable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},getBadgeBackgroundColor:{minArgs:1,maxArgs:1},getBadgeText:{minArgs:1,maxArgs:1},getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},openPopup:{minArgs:0,maxArgs:0},setBadgeBackgroundColor:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setBadgeText:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},browsingData:{remove:{minArgs:2,maxArgs:2},removeCache:{minArgs:1,maxArgs:1},removeCookies:{minArgs:1,maxArgs:1},removeDownloads:{minArgs:1,maxArgs:1},removeFormData:{minArgs:1,maxArgs:1},removeHistory:{minArgs:1,maxArgs:1},removeLocalStorage:{minArgs:1,maxArgs:1},removePasswords:{minArgs:1,maxArgs:1},removePluginData:{minArgs:1,maxArgs:1},settings:{minArgs:0,maxArgs:0}},commands:{getAll:{minArgs:0,maxArgs:0}},contextMenus:{remove:{minArgs:1,maxArgs:1},removeAll:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},cookies:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:1,maxArgs:1},getAllCookieStores:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},devtools:{inspectedWindow:{eval:{minArgs:1,maxArgs:2,singleCallbackArg:!1}},panels:{create:{minArgs:3,maxArgs:3,singleCallbackArg:!0},elements:{createSidebarPane:{minArgs:1,maxArgs:1}}}},downloads:{cancel:{minArgs:1,maxArgs:1},download:{minArgs:1,maxArgs:1},erase:{minArgs:1,maxArgs:1},getFileIcon:{minArgs:1,maxArgs:2},open:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},pause:{minArgs:1,maxArgs:1},removeFile:{minArgs:1,maxArgs:1},resume:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},extension:{isAllowedFileSchemeAccess:{minArgs:0,maxArgs:0},isAllowedIncognitoAccess:{minArgs:0,maxArgs:0}},history:{addUrl:{minArgs:1,maxArgs:1},deleteAll:{minArgs:0,maxArgs:0},deleteRange:{minArgs:1,maxArgs:1},deleteUrl:{minArgs:1,maxArgs:1},getVisits:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1}},i18n:{detectLanguage:{minArgs:1,maxArgs:1},getAcceptLanguages:{minArgs:0,maxArgs:0}},identity:{launchWebAuthFlow:{minArgs:1,maxArgs:1}},idle:{queryState:{minArgs:1,maxArgs:1}},management:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},getSelf:{minArgs:0,maxArgs:0},setEnabled:{minArgs:2,maxArgs:2},uninstallSelf:{minArgs:0,maxArgs:1}},notifications:{clear:{minArgs:1,maxArgs:1},create:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:0},getPermissionLevel:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},pageAction:{getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},hide:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},permissions:{contains:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},request:{minArgs:1,maxArgs:1}},runtime:{getBackgroundPage:{minArgs:0,maxArgs:0},getPlatformInfo:{minArgs:0,maxArgs:0},openOptionsPage:{minArgs:0,maxArgs:0},requestUpdateCheck:{minArgs:0,maxArgs:0},sendMessage:{minArgs:1,maxArgs:3},sendNativeMessage:{minArgs:2,maxArgs:2},setUninstallURL:{minArgs:1,maxArgs:1}},sessions:{getDevices:{minArgs:0,maxArgs:1},getRecentlyClosed:{minArgs:0,maxArgs:1},restore:{minArgs:0,maxArgs:1}},storage:{local:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},managed:{get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1}},sync:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}}},tabs:{captureVisibleTab:{minArgs:0,maxArgs:2},create:{minArgs:1,maxArgs:1},detectLanguage:{minArgs:0,maxArgs:1},discard:{minArgs:0,maxArgs:1},duplicate:{minArgs:1,maxArgs:1},executeScript:{minArgs:1,maxArgs:2},get:{minArgs:1,maxArgs:1},getCurrent:{minArgs:0,maxArgs:0},getZoom:{minArgs:0,maxArgs:1},getZoomSettings:{minArgs:0,maxArgs:1},goBack:{minArgs:0,maxArgs:1},goForward:{minArgs:0,maxArgs:1},highlight:{minArgs:1,maxArgs:1},insertCSS:{minArgs:1,maxArgs:2},move:{minArgs:2,maxArgs:2},query:{minArgs:1,maxArgs:1},reload:{minArgs:0,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeCSS:{minArgs:1,maxArgs:2},sendMessage:{minArgs:2,maxArgs:3},setZoom:{minArgs:1,maxArgs:2},setZoomSettings:{minArgs:1,maxArgs:2},update:{minArgs:1,maxArgs:2}},topSites:{get:{minArgs:0,maxArgs:0}},webNavigation:{getAllFrames:{minArgs:1,maxArgs:1},getFrame:{minArgs:1,maxArgs:1}},webRequest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(Object.keys(j).length===0)throw new Error("api-metadata.json has not been included in browser-polyfill");class Y extends WeakMap{constructor(y,O=void 0){super(O),this.createItem=y}get(y){return this.has(y)||this.set(y,this.createItem(y)),super.get(y)}}const F=w=>w&&typeof w=="object"&&typeof w.then=="function",W=(w,y)=>(...O)=>{b.runtime.lastError?w.reject(new Error(b.runtime.lastError.message)):y.singleCallbackArg||O.length<=1&&y.singleCallbackArg!==!1?w.resolve(O[0]):w.resolve(O)},f=w=>w==1?"argument":"arguments",m=(w,y)=>function(L,...z){if(z.length<y.minArgs)throw new Error(`Expected at least ${y.minArgs} ${f(y.minArgs)} for ${w}(), got ${z.length}`);if(z.length>y.maxArgs)throw new Error(`Expected at most ${y.maxArgs} ${f(y.maxArgs)} for ${w}(), got ${z.length}`);return new Promise((B,H)=>{if(y.fallbackToNoCallback)try{L[w](...z,W({resolve:B,reject:H},y))}catch(k){console.warn(`${w} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,k),L[w](...z),y.fallbackToNoCallback=!1,y.noCallback=!0,B()}else y.noCallback?(L[w](...z),B()):L[w](...z,W({resolve:B,reject:H},y))})},o=(w,y,O)=>new Proxy(y,{apply(L,z,B){return O.call(z,w,...B)}});let s=Function.call.bind(Object.prototype.hasOwnProperty);const u=(w,y={},O={})=>{let L=Object.create(null),z={has(H,k){return k in w||k in L},get(H,k,K){if(k in L)return L[k];if(!(k in w))return;let S=w[k];if(typeof S=="function")if(typeof y[k]=="function")S=o(w,w[k],y[k]);else if(s(O,k)){let de=m(k,O[k]);S=o(w,w[k],de)}else S=S.bind(w);else if(typeof S=="object"&&S!==null&&(s(y,k)||s(O,k)))S=u(S,y[k],O[k]);else if(s(O,"*"))S=u(S,y[k],O["*"]);else return Object.defineProperty(L,k,{configurable:!0,enumerable:!0,get(){return w[k]},set(de){w[k]=de}}),S;return L[k]=S,S},set(H,k,K,S){return k in L?L[k]=K:w[k]=K,!0},defineProperty(H,k,K){return Reflect.defineProperty(L,k,K)},deleteProperty(H,k){return Reflect.deleteProperty(L,k)}},B=Object.create(w);return new Proxy(B,z)},_=w=>({addListener(y,O,...L){y.addListener(w.get(O),...L)},hasListener(y,O){return y.hasListener(w.get(O))},removeListener(y,O){y.removeListener(w.get(O))}}),R=new Y(w=>typeof w!="function"?w:function(O){const L=u(O,{},{getContent:{minArgs:0,maxArgs:0}});w(L)}),g=new Y(w=>typeof w!="function"?w:function(O,L,z){let B=!1,H,k=new Promise(me=>{H=function(re){B=!0,me(re)}}),K;try{K=w(O,L,H)}catch(me){K=Promise.reject(me)}const S=K!==!0&&F(K);if(K!==!0&&!S&&!B)return!1;const de=me=>{me.then(re=>{z(re)},re=>{let _e;re&&(re instanceof Error||typeof re.message=="string")?_e=re.message:_e="An unexpected error occurred",z({__mozWebExtensionPolyfillReject__:!0,message:_e})}).catch(re=>{console.error("Failed to send onMessage rejected reply",re)})};return de(S?K:k),!0}),p=({reject:w,resolve:y},O)=>{b.runtime.lastError?b.runtime.lastError.message===v?y():w(new Error(b.runtime.lastError.message)):O&&O.__mozWebExtensionPolyfillReject__?w(new Error(O.message)):y(O)},A=(w,y,O,...L)=>{if(L.length<y.minArgs)throw new Error(`Expected at least ${y.minArgs} ${f(y.minArgs)} for ${w}(), got ${L.length}`);if(L.length>y.maxArgs)throw new Error(`Expected at most ${y.maxArgs} ${f(y.maxArgs)} for ${w}(), got ${L.length}`);return new Promise((z,B)=>{const H=p.bind(null,{resolve:z,reject:B});L.push(H),O.sendMessage(...L)})},C={devtools:{network:{onRequestFinished:_(R)}},runtime:{onMessage:_(g),onMessageExternal:_(g),sendMessage:A.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:A.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},D={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return j.privacy={network:{"*":D},services:{"*":D},websites:{"*":D}},u(b,C,j)};a.exports=x(chrome)}else a.exports=globalThis.browser})})(Qr);var en=Qr.exports;const et=Ar(en);var Z=et;const rn="1.1.3",rt="prod",tn={dev:"https://oversea.dev.kimi.team",pre:"https://kimi-pre.msh.team",prod:"https://kimi.moonshot.cn"},ae={name:"kimi-web-extension",env:rt,version:rn,platform:"web-extension",timeZone:Intl.DateTimeFormat().resolvedOptions().timeZone,url:tn[rt],storage:{version:"",access_token:"",refresh_token:"",browser_name:"",explore_toolbar:!0,explore_underline:!0,chat_float_button:!0,chat_window_mode:"modal"},dom:{id:"data-kimi-id",disabled:"data-kimi-disabled",recommend:"data-kimi-recommend"},feature:{sidepanel:["Chrome","Edge"]}},$e={shortcuts:"chrome://extensions/shortcuts",home:ae.url,login:ae.url+"/extension/login",welcome:ae.url+"/extension/welcome",uninstall:ae.url+"/extension/uninstall",modeluse:ae.url+"/user/agreement/modeluse",userprivacy:ae.url+"/user/agreement/userprivacy"},ce={tracking_event:"tracking_event",tracking_profile:"tracking_profile",open_sidepanel:"open_sidepanel",close_sidepanel:"close_sidepanel",new_tab:"new_tab",get_commands:"get_commands",screenshot:"screenshot",command:"command",url_changed:"url_changed",get_web_info:"get_web_info",sidepanel_opened:"sidepanel_opened"};var Er={exports:{}};(function(r,t){(function(a,i){var d="1.0.38",v="",x="?",b="function",j="undefined",Y="object",F="string",W="major",f="model",m="name",o="type",s="vendor",u="version",_="architecture",R="console",g="mobile",p="tablet",A="smarttv",C="wearable",D="embedded",w=500,y="Amazon",O="Apple",L="ASUS",z="BlackBerry",B="Browser",H="Chrome",k="Edge",K="Firefox",S="Google",de="Huawei",me="LG",re="Microsoft",_e="Motorola",ie="Opera",xe="Samsung",we="Sharp",Se="Sony",Ge="Xiaomi",Pe="Zebra",qe="Facebook",je="Chromium OS",Fe="Mac OS",Vr=function(N,$){var I={};for(var G in N)$[G]&&$[G].length%2===0?I[G]=$[G].concat(N[G]):I[G]=N[G];return I},ze=function(N){for(var $={},I=0;I<N.length;I++)$[N[I].toUpperCase()]=N[I];return $},Ie=function(N,$){return typeof N===F?Me($).indexOf(Me(N))!==-1:!1},Me=function(N){return N.toLowerCase()},Ur=function(N){return typeof N===F?N.replace(/[^\d\.]/g,v).split(".")[0]:i},Ve=function(N,$){if(typeof N===F)return N=N.replace(/^\s\s*/,v),typeof $===j?N:N.substring(0,w)},ye=function(N,$){for(var I=0,G,be,ge,V,P,pe;I<$.length&&!P;){var Ye=$[I],Ee=$[I+1];for(G=be=0;G<Ye.length&&!P&&Ye[G];)if(P=Ye[G++].exec(N),P)for(ge=0;ge<Ee.length;ge++)pe=P[++be],V=Ee[ge],typeof V===Y&&V.length>0?V.length===2?typeof V[1]==b?this[V[0]]=V[1].call(this,pe):this[V[0]]=V[1]:V.length===3?typeof V[1]===b&&!(V[1].exec&&V[1].test)?this[V[0]]=pe?V[1].call(this,pe,V[2]):i:this[V[0]]=pe?pe.replace(V[1],V[2]):i:V.length===4&&(this[V[0]]=pe?V[3].call(this,pe.replace(V[1],V[2])):i):this[V]=pe||i;I+=2}},He=function(N,$){for(var I in $)if(typeof $[I]===Y&&$[I].length>0){for(var G=0;G<$[I].length;G++)if(Ie($[I][G],N))return I===x?i:I}else if(Ie($[I],N))return I===x?i:I;return N},dr={"1.0":"/8","1.2":"/1","1.3":"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"},he={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2","8.1":"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Oe={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[u,[m,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[u,[m,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[m,u],[/opios[\/ ]+([\w\.]+)/i],[u,[m,ie+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[u,[m,ie+" GX"]],[/\bopr\/([\w\.]+)/i],[u,[m,ie]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[u,[m,"Baidu"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim)\s?(?:browser)?[\/ ]?([\w\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[m,u],[/\bddg\/([\w\.]+)/i],[u,[m,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[u,[m,"UC"+B]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[u,[m,"WeChat"]],[/konqueror\/([\w\.]+)/i],[u,[m,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[u,[m,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[u,[m,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[u,[m,"Smart Lenovo "+B]],[/(avast|avg)\/([\w\.]+)/i],[[m,/(.+)/,"$1 Secure "+B],u],[/\bfocus\/([\w\.]+)/i],[u,[m,K+" Focus"]],[/\bopt\/([\w\.]+)/i],[u,[m,ie+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[u,[m,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[u,[m,"Dolphin"]],[/coast\/([\w\.]+)/i],[u,[m,ie+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[u,[m,"MIUI "+B]],[/fxios\/([-\w\.]+)/i],[u,[m,K]],[/\bqihu|(qi?ho?o?|360)browser/i],[[m,"360 "+B]],[/(oculus|sailfish|huawei|vivo)browser\/([\w\.]+)/i],[[m,/(.+)/,"$1 "+B],u],[/samsungbrowser\/([\w\.]+)/i],[u,[m,xe+" Internet"]],[/(comodo_dragon)\/([\w\.]+)/i],[[m,/_/g," "],u],[/metasr[\/ ]?([\d\.]+)/i],[u,[m,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[m,"Sogou Mobile"],u],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345Explorer)[\/ ]?([\w\.]+)/i],[m,u],[/(lbbrowser)/i,/\[(linkedin)app\]/i],[m],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[m,qe],u],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(chromium|instagram|snapchat)[\/ ]([-\w\.]+)/i],[m,u],[/\bgsa\/([\w\.]+) .*safari\//i],[u,[m,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[u,[m,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[u,[m,H+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[m,H+" WebView"],u],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[u,[m,"Android "+B]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[m,u],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[u,[m,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[u,m],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[m,[u,He,dr]],[/(webkit|khtml)\/([\w\.]+)/i],[m,u],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[m,"Netscape"],u],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[u,[m,K+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[m,u],[/(cobalt)\/([\w\.]+)/i],[m,[u,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[_,"amd64"]],[/(ia32(?=;))/i],[[_,Me]],[/((?:i[346]|x)86)[;\)]/i],[[_,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[_,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[_,"armhf"]],[/windows (ce|mobile); ppc;/i],[[_,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[_,/ower/,v,Me]],[/(sun4\w)[;\)]/i],[[_,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[_,Me]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[f,[s,xe],[o,p]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[f,[s,xe],[o,g]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[f,[s,O],[o,g]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[f,[s,O],[o,p]],[/(macintosh);/i],[f,[s,O]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[f,[s,we],[o,g]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[f,[s,de],[o,p]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[f,[s,de],[o,g]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[f,/_/g," "],[s,Ge],[o,g]],[/oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i,/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[f,/_/g," "],[s,Ge],[o,p]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[f,[s,"OPPO"],[o,g]],[/\b(opd2\d{3}a?) bui/i],[f,[s,"OPPO"],[o,p]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[f,[s,"Vivo"],[o,g]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[f,[s,"Realme"],[o,g]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[f,[s,_e],[o,g]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[f,[s,_e],[o,p]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[f,[s,me],[o,p]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[f,[s,me],[o,g]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[f,[s,"Lenovo"],[o,p]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[f,/_/g," "],[s,"Nokia"],[o,g]],[/(pixel c)\b/i],[f,[s,S],[o,p]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[f,[s,S],[o,g]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[f,[s,Se],[o,g]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[f,"Xperia Tablet"],[s,Se],[o,p]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[f,[s,"OnePlus"],[o,g]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[f,[s,y],[o,p]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[f,/(.+)/g,"Fire Phone $1"],[s,y],[o,g]],[/(playbook);[-\w\),; ]+(rim)/i],[f,s,[o,p]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[f,[s,z],[o,g]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[f,[s,L],[o,p]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[f,[s,L],[o,g]],[/(nexus 9)/i],[f,[s,"HTC"],[o,p]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[s,[f,/_/g," "],[o,g]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[f,[s,"Acer"],[o,p]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[f,[s,"Meizu"],[o,g]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[f,[s,"Ulefone"],[o,g]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[s,f,[o,g]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[s,f,[o,p]],[/(surface duo)/i],[f,[s,re],[o,p]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[f,[s,"Fairphone"],[o,g]],[/(u304aa)/i],[f,[s,"AT&T"],[o,g]],[/\bsie-(\w*)/i],[f,[s,"Siemens"],[o,g]],[/\b(rct\w+) b/i],[f,[s,"RCA"],[o,p]],[/\b(venue[\d ]{2,7}) b/i],[f,[s,"Dell"],[o,p]],[/\b(q(?:mv|ta)\w+) b/i],[f,[s,"Verizon"],[o,p]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[f,[s,"Barnes & Noble"],[o,p]],[/\b(tm\d{3}\w+) b/i],[f,[s,"NuVision"],[o,p]],[/\b(k88) b/i],[f,[s,"ZTE"],[o,p]],[/\b(nx\d{3}j) b/i],[f,[s,"ZTE"],[o,g]],[/\b(gen\d{3}) b.+49h/i],[f,[s,"Swiss"],[o,g]],[/\b(zur\d{3}) b/i],[f,[s,"Swiss"],[o,p]],[/\b((zeki)?tb.*\b) b/i],[f,[s,"Zeki"],[o,p]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[s,"Dragon Touch"],f,[o,p]],[/\b(ns-?\w{0,9}) b/i],[f,[s,"Insignia"],[o,p]],[/\b((nxa|next)-?\w{0,9}) b/i],[f,[s,"NextBook"],[o,p]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[s,"Voice"],f,[o,g]],[/\b(lvtel\-)?(v1[12]) b/i],[[s,"LvTel"],f,[o,g]],[/\b(ph-1) /i],[f,[s,"Essential"],[o,g]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[f,[s,"Envizen"],[o,p]],[/\b(trio[-\w\. ]+) b/i],[f,[s,"MachSpeed"],[o,p]],[/\btu_(1491) b/i],[f,[s,"Rotor"],[o,p]],[/(shield[\w ]+) b/i],[f,[s,"Nvidia"],[o,p]],[/(sprint) (\w+)/i],[s,f,[o,g]],[/(kin\.[onetw]{3})/i],[[f,/\./g," "],[s,re],[o,g]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[f,[s,Pe],[o,p]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[f,[s,Pe],[o,g]],[/smart-tv.+(samsung)/i],[s,[o,A]],[/hbbtv.+maple;(\d+)/i],[[f,/^/,"SmartTV"],[s,xe],[o,A]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[s,me],[o,A]],[/(apple) ?tv/i],[s,[f,O+" TV"],[o,A]],[/crkey/i],[[f,H+"cast"],[s,S],[o,A]],[/droid.+aft(\w+)( bui|\))/i],[f,[s,y],[o,A]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[f,[s,we],[o,A]],[/(bravia[\w ]+)( bui|\))/i],[f,[s,Se],[o,A]],[/(mitv-\w{5}) bui/i],[f,[s,Ge],[o,A]],[/Hbbtv.*(technisat) (.*);/i],[s,f,[o,A]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[s,Ve],[f,Ve],[o,A]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[o,A]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[s,f,[o,R]],[/droid.+; (shield) bui/i],[f,[s,"Nvidia"],[o,R]],[/(playstation [345portablevi]+)/i],[f,[s,Se],[o,R]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[f,[s,re],[o,R]],[/((pebble))app/i],[s,f,[o,C]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[f,[s,O],[o,C]],[/droid.+; (glass) \d/i],[f,[s,S],[o,C]],[/droid.+; (wt63?0{2,3})\)/i],[f,[s,Pe],[o,C]],[/(quest( \d| pro)?)/i],[f,[s,qe],[o,C]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[s,[o,D]],[/(aeobc)\b/i],[f,[s,y],[o,D]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+? mobile safari/i],[f,[o,g]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[f,[o,p]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[o,p]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[o,g]],[/(android[-\w\. ]{0,9});.+buil/i],[f,[s,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[u,[m,k+"HTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[u,[m,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[m,u],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[u,m]],os:[[/microsoft (windows) (vista|xp)/i],[m,u],[/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i],[m,[u,He,he]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[u,He,he],[m,"Windows"]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[u,/_/g,"."],[m,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[m,Fe],[u,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[u,m],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[m,u],[/\(bb(10);/i],[u,[m,z]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[u,[m,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[u,[m,K+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[u,[m,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[u,[m,"watchOS"]],[/crkey\/([\d\.]+)/i],[u,[m,H+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[m,je],u],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[m,u],[/(sunos) ?([\w\.\d]*)/i],[[m,"Solaris"],u],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[m,u]]},ue=function(N,$){if(typeof N===Y&&($=N,N=i),!(this instanceof ue))return new ue(N,$).getResult();var I=typeof a!==j&&a.navigator?a.navigator:i,G=N||(I&&I.userAgent?I.userAgent:v),be=I&&I.userAgentData?I.userAgentData:i,ge=$?Vr(Oe,$):Oe,V=I&&I.userAgent==G;return this.getBrowser=function(){var P={};return P[m]=i,P[u]=i,ye.call(P,G,ge.browser),P[W]=Ur(P[u]),V&&I&&I.brave&&typeof I.brave.isBrave==b&&(P[m]="Brave"),P},this.getCPU=function(){var P={};return P[_]=i,ye.call(P,G,ge.cpu),P},this.getDevice=function(){var P={};return P[s]=i,P[f]=i,P[o]=i,ye.call(P,G,ge.device),V&&!P[o]&&be&&be.mobile&&(P[o]=g),V&&P[f]=="Macintosh"&&I&&typeof I.standalone!==j&&I.maxTouchPoints&&I.maxTouchPoints>2&&(P[f]="iPad",P[o]=p),P},this.getEngine=function(){var P={};return P[m]=i,P[u]=i,ye.call(P,G,ge.engine),P},this.getOS=function(){var P={};return P[m]=i,P[u]=i,ye.call(P,G,ge.os),V&&!P[m]&&be&&be.platform&&be.platform!="Unknown"&&(P[m]=be.platform.replace(/chrome os/i,je).replace(/macos/i,Fe)),P},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return G},this.setUA=function(P){return G=typeof P===F&&P.length>w?Ve(P,w):P,this},this.setUA(G),this};ue.VERSION=d,ue.BROWSER=ze([m,u,W]),ue.CPU=ze([_]),ue.DEVICE=ze([f,s,o,R,g,A,p,C,D]),ue.ENGINE=ue.OS=ze([m,u]),r.exports&&(t=r.exports=ue),t.UAParser=ue;var Ae=typeof a!==j&&(a.jQuery||a.Zepto);if(Ae&&!Ae.ua){var De=new ue;Ae.ua=De.getResult(),Ae.ua.get=function(){return De.getUA()},Ae.ua.set=function(N){De.setUA(N);var $=De.getResult();for(var I in $)Ae.ua[I]=$[I]}}})(typeof window=="object"?window:Jr)})(Er,Er.exports);var nn=Er.exports,an={VITE_CJS_IGNORE_WARNING:"true",BASE_URL:"/",MODE:"prod",DEV:!1,PROD:!0,SSR:!1,MANIFEST_VERSION:3,BROWSER:"chrome",CHROME:!0,FIREFOX:!1,SAFARI:!1,EDGE:!1,OPERA:!1,COMMAND:"build",ENTRYPOINT:"background"};const tt=r=>{let t;const a=new Set,i=(F,W)=>{const f=typeof F=="function"?F(t):F;if(!Object.is(f,t)){const m=t;t=W??(typeof f!="object"||f===null)?f:Object.assign({},t,f),a.forEach(o=>o(t,m))}},d=()=>t,j={setState:i,getState:d,getInitialState:()=>Y,subscribe:F=>(a.add(F),()=>a.delete(F)),destroy:()=>{(an?"prod":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),a.clear()}},Y=t=r(i,d,j);return j},on=r=>r?tt(r):tt;var nt={exports:{}},Je={exports:{}};Je.exports,function(r,t){/**
 * @license React
 * react.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(){typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error);var a="18.3.1",i=Symbol.for("react.element"),d=Symbol.for("react.portal"),v=Symbol.for("react.fragment"),x=Symbol.for("react.strict_mode"),b=Symbol.for("react.profiler"),j=Symbol.for("react.provider"),Y=Symbol.for("react.context"),F=Symbol.for("react.forward_ref"),W=Symbol.for("react.suspense"),f=Symbol.for("react.suspense_list"),m=Symbol.for("react.memo"),o=Symbol.for("react.lazy"),s=Symbol.for("react.offscreen"),u=Symbol.iterator,_="@@iterator";function R(e){if(e===null||typeof e!="object")return null;var n=u&&e[u]||e[_];return typeof n=="function"?n:null}var g={current:null},p={transition:null},A={current:null,isBatchingLegacy:!1,didScheduleLegacyUpdate:!1},C={current:null},D={},w=null;function y(e){w=e}D.setExtraStackFrame=function(e){w=e},D.getCurrentStack=null,D.getStackAddendum=function(){var e="";w&&(e+=w);var n=D.getCurrentStack;return n&&(e+=n()||""),e};var O=!1,L=!1,z=!1,B=!1,H=!1,k={ReactCurrentDispatcher:g,ReactCurrentBatchConfig:p,ReactCurrentOwner:C};k.ReactDebugCurrentFrame=D,k.ReactCurrentActQueue=A;function K(e){{for(var n=arguments.length,c=new Array(n>1?n-1:0),l=1;l<n;l++)c[l-1]=arguments[l];de("warn",e,c)}}function S(e){{for(var n=arguments.length,c=new Array(n>1?n-1:0),l=1;l<n;l++)c[l-1]=arguments[l];de("error",e,c)}}function de(e,n,c){{var l=k.ReactDebugCurrentFrame,h=l.getStackAddendum();h!==""&&(n+="%s",c=c.concat([h]));var T=c.map(function(E){return String(E)});T.unshift("Warning: "+n),Function.prototype.apply.call(console[e],console,T)}}var me={};function re(e,n){{var c=e.constructor,l=c&&(c.displayName||c.name)||"ReactClass",h=l+"."+n;if(me[h])return;S("Can't call %s on a component that is not yet mounted. This is a no-op, but it might indicate a bug in your application. Instead, assign to `this.state` directly or define a `state = {};` class property with the desired state in the %s component.",n,l),me[h]=!0}}var _e={isMounted:function(e){return!1},enqueueForceUpdate:function(e,n,c){re(e,"forceUpdate")},enqueueReplaceState:function(e,n,c,l){re(e,"replaceState")},enqueueSetState:function(e,n,c,l){re(e,"setState")}},ie=Object.assign,xe={};Object.freeze(xe);function we(e,n,c){this.props=e,this.context=n,this.refs=xe,this.updater=c||_e}we.prototype.isReactComponent={},we.prototype.setState=function(e,n){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw new Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,n,"setState")},we.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};{var Se={isMounted:["isMounted","Instead, make sure to clean up subscriptions and pending requests in componentWillUnmount to prevent memory leaks."],replaceState:["replaceState","Refactor your code to use setState instead (see https://github.com/facebook/react/issues/3236)."]},Ge=function(e,n){Object.defineProperty(we.prototype,e,{get:function(){K("%s(...) is deprecated in plain JavaScript React classes. %s",n[0],n[1])}})};for(var Pe in Se)Se.hasOwnProperty(Pe)&&Ge(Pe,Se[Pe])}function qe(){}qe.prototype=we.prototype;function je(e,n,c){this.props=e,this.context=n,this.refs=xe,this.updater=c||_e}var Fe=je.prototype=new qe;Fe.constructor=je,ie(Fe,we.prototype),Fe.isPureReactComponent=!0;function Vr(){var e={current:null};return Object.seal(e),e}var ze=Array.isArray;function Ie(e){return ze(e)}function Me(e){{var n=typeof Symbol=="function"&&Symbol.toStringTag,c=n&&e[Symbol.toStringTag]||e.constructor.name||"Object";return c}}function Ur(e){try{return Ve(e),!1}catch{return!0}}function Ve(e){return""+e}function ye(e){if(Ur(e))return S("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",Me(e)),Ve(e)}function He(e,n,c){var l=e.displayName;if(l)return l;var h=n.displayName||n.name||"";return h!==""?c+"("+h+")":c}function dr(e){return e.displayName||"Context"}function he(e){if(e==null)return null;if(typeof e.tag=="number"&&S("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case v:return"Fragment";case d:return"Portal";case b:return"Profiler";case x:return"StrictMode";case W:return"Suspense";case f:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Y:var n=e;return dr(n)+".Consumer";case j:var c=e;return dr(c._context)+".Provider";case F:return He(e,e.render,"ForwardRef");case m:var l=e.displayName||null;return l!==null?l:he(e.type)||"Memo";case o:{var h=e,T=h._payload,E=h._init;try{return he(E(T))}catch{return null}}}return null}var Oe=Object.prototype.hasOwnProperty,ue={key:!0,ref:!0,__self:!0,__source:!0},Ae,De,N;N={};function $(e){if(Oe.call(e,"ref")){var n=Object.getOwnPropertyDescriptor(e,"ref").get;if(n&&n.isReactWarning)return!1}return e.ref!==void 0}function I(e){if(Oe.call(e,"key")){var n=Object.getOwnPropertyDescriptor(e,"key").get;if(n&&n.isReactWarning)return!1}return e.key!==void 0}function G(e,n){var c=function(){Ae||(Ae=!0,S("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",n))};c.isReactWarning=!0,Object.defineProperty(e,"key",{get:c,configurable:!0})}function be(e,n){var c=function(){De||(De=!0,S("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",n))};c.isReactWarning=!0,Object.defineProperty(e,"ref",{get:c,configurable:!0})}function ge(e){if(typeof e.ref=="string"&&C.current&&e.__self&&C.current.stateNode!==e.__self){var n=he(C.current.type);N[n]||(S('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',n,e.ref),N[n]=!0)}}var V=function(e,n,c,l,h,T,E){var M={$$typeof:i,type:e,key:n,ref:c,props:E,_owner:T};return M._store={},Object.defineProperty(M._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(M,"_self",{configurable:!1,enumerable:!1,writable:!1,value:l}),Object.defineProperty(M,"_source",{configurable:!1,enumerable:!1,writable:!1,value:h}),Object.freeze&&(Object.freeze(M.props),Object.freeze(M)),M};function P(e,n,c){var l,h={},T=null,E=null,M=null,U=null;if(n!=null){$(n)&&(E=n.ref,ge(n)),I(n)&&(ye(n.key),T=""+n.key),M=n.__self===void 0?null:n.__self,U=n.__source===void 0?null:n.__source;for(l in n)Oe.call(n,l)&&!ue.hasOwnProperty(l)&&(h[l]=n[l])}var q=arguments.length-2;if(q===1)h.children=c;else if(q>1){for(var X=Array(q),J=0;J<q;J++)X[J]=arguments[J+2];Object.freeze&&Object.freeze(X),h.children=X}if(e&&e.defaultProps){var Q=e.defaultProps;for(l in Q)h[l]===void 0&&(h[l]=Q[l])}if(T||E){var ee=typeof e=="function"?e.displayName||e.name||"Unknown":e;T&&G(h,ee),E&&be(h,ee)}return V(e,T,E,M,U,C.current,h)}function pe(e,n){var c=V(e.type,n,e.ref,e._self,e._source,e._owner,e.props);return c}function Ye(e,n,c){if(e==null)throw new Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var l,h=ie({},e.props),T=e.key,E=e.ref,M=e._self,U=e._source,q=e._owner;if(n!=null){$(n)&&(E=n.ref,q=C.current),I(n)&&(ye(n.key),T=""+n.key);var X;e.type&&e.type.defaultProps&&(X=e.type.defaultProps);for(l in n)Oe.call(n,l)&&!ue.hasOwnProperty(l)&&(n[l]===void 0&&X!==void 0?h[l]=X[l]:h[l]=n[l])}var J=arguments.length-2;if(J===1)h.children=c;else if(J>1){for(var Q=Array(J),ee=0;ee<J;ee++)Q[ee]=arguments[ee+2];h.children=Q}return V(e.type,T,E,M,U,q,h)}function Ee(e){return typeof e=="object"&&e!==null&&e.$$typeof===i}var Et=".",$n=":";function Bn(e){var n=/[=:]/g,c={"=":"=0",":":"=2"},l=e.replace(n,function(h){return c[h]});return"$"+l}var xt=!1,Kn=/\/+/g;function St(e){return e.replace(Kn,"$&/")}function Wr(e,n){return typeof e=="object"&&e!==null&&e.key!=null?(ye(e.key),Bn(""+e.key)):n.toString(36)}function mr(e,n,c,l,h){var T=typeof e;(T==="undefined"||T==="boolean")&&(e=null);var E=!1;if(e===null)E=!0;else switch(T){case"string":case"number":E=!0;break;case"object":switch(e.$$typeof){case i:case d:E=!0}}if(E){var M=e,U=h(M),q=l===""?Et+Wr(M,0):l;if(Ie(U)){var X="";q!=null&&(X=St(q)+"/"),mr(U,n,X,"",function(ja){return ja})}else U!=null&&(Ee(U)&&(U.key&&(!M||M.key!==U.key)&&ye(U.key),U=pe(U,c+(U.key&&(!M||M.key!==U.key)?St(""+U.key)+"/":"")+q)),n.push(U));return 1}var J,Q,ee=0,ne=l===""?Et:l+$n;if(Ie(e))for(var _r=0;_r<e.length;_r++)J=e[_r],Q=ne+Wr(J,_r),ee+=mr(J,n,c,Q,h);else{var Xr=R(e);if(typeof Xr=="function"){var Zt=e;Xr===Zt.entries&&(xt||K("Using Maps as children is not supported. Use an array of keyed ReactElements instead."),xt=!0);for(var La=Xr.call(Zt),Xt,Na=0;!(Xt=La.next()).done;)J=Xt.value,Q=ne+Wr(J,Na++),ee+=mr(J,n,c,Q,h)}else if(T==="object"){var Jt=String(e);throw new Error("Objects are not valid as a React child (found: "+(Jt==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":Jt)+"). If you meant to render a collection of children, use an array instead.")}}return ee}function gr(e,n,c){if(e==null)return e;var l=[],h=0;return mr(e,l,"","",function(T){return n.call(c,T,h++)}),l}function Gn(e){var n=0;return gr(e,function(){n++}),n}function qn(e,n,c){gr(e,function(){n.apply(this,arguments)},c)}function Hn(e){return gr(e,function(n){return n})||[]}function Yn(e){if(!Ee(e))throw new Error("React.Children.only expected to receive a single React element child.");return e}function Zn(e){var n={$$typeof:Y,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null};n.Provider={$$typeof:j,_context:n};var c=!1,l=!1,h=!1;{var T={$$typeof:Y,_context:n};Object.defineProperties(T,{Provider:{get:function(){return l||(l=!0,S("Rendering <Context.Consumer.Provider> is not supported and will be removed in a future major release. Did you mean to render <Context.Provider> instead?")),n.Provider},set:function(E){n.Provider=E}},_currentValue:{get:function(){return n._currentValue},set:function(E){n._currentValue=E}},_currentValue2:{get:function(){return n._currentValue2},set:function(E){n._currentValue2=E}},_threadCount:{get:function(){return n._threadCount},set:function(E){n._threadCount=E}},Consumer:{get:function(){return c||(c=!0,S("Rendering <Context.Consumer.Consumer> is not supported and will be removed in a future major release. Did you mean to render <Context.Consumer> instead?")),n.Consumer}},displayName:{get:function(){return n.displayName},set:function(E){h||(K("Setting `displayName` on Context.Consumer has no effect. You should set it directly on the context with Context.displayName = '%s'.",E),h=!0)}}}),n.Consumer=T}return n._currentRenderer=null,n._currentRenderer2=null,n}var Ze=-1,$r=0,Ot=1,Xn=2;function Jn(e){if(e._status===Ze){var n=e._result,c=n();if(c.then(function(T){if(e._status===$r||e._status===Ze){var E=e;E._status=Ot,E._result=T}},function(T){if(e._status===$r||e._status===Ze){var E=e;E._status=Xn,E._result=T}}),e._status===Ze){var l=e;l._status=$r,l._result=c}}if(e._status===Ot){var h=e._result;return h===void 0&&S(`lazy: Expected the result of a dynamic import() call. Instead received: %s

Your code should look like: 
  const MyComponent = lazy(() => import('./MyComponent'))

Did you accidentally put curly braces around the import?`,h),"default"in h||S(`lazy: Expected the result of a dynamic import() call. Instead received: %s

Your code should look like: 
  const MyComponent = lazy(() => import('./MyComponent'))`,h),h.default}else throw e._result}function Qn(e){var n={_status:Ze,_result:e},c={$$typeof:o,_payload:n,_init:Jn};{var l,h;Object.defineProperties(c,{defaultProps:{configurable:!0,get:function(){return l},set:function(T){S("React.lazy(...): It is not supported to assign `defaultProps` to a lazy component import. Either specify them where the component is defined, or create a wrapping component around it."),l=T,Object.defineProperty(c,"defaultProps",{enumerable:!0})}},propTypes:{configurable:!0,get:function(){return h},set:function(T){S("React.lazy(...): It is not supported to assign `propTypes` to a lazy component import. Either specify them where the component is defined, or create a wrapping component around it."),h=T,Object.defineProperty(c,"propTypes",{enumerable:!0})}}})}return c}function ea(e){e!=null&&e.$$typeof===m?S("forwardRef requires a render function but received a `memo` component. Instead of forwardRef(memo(...)), use memo(forwardRef(...))."):typeof e!="function"?S("forwardRef requires a render function but was given %s.",e===null?"null":typeof e):e.length!==0&&e.length!==2&&S("forwardRef render functions accept exactly two parameters: props and ref. %s",e.length===1?"Did you forget to use the ref parameter?":"Any additional parameter will be undefined."),e!=null&&(e.defaultProps!=null||e.propTypes!=null)&&S("forwardRef render functions do not support propTypes or defaultProps. Did you accidentally pass a React component?");var n={$$typeof:F,render:e};{var c;Object.defineProperty(n,"displayName",{enumerable:!1,configurable:!0,get:function(){return c},set:function(l){c=l,!e.name&&!e.displayName&&(e.displayName=l)}})}return n}var Tt;Tt=Symbol.for("react.module.reference");function kt(e){return!!(typeof e=="string"||typeof e=="function"||e===v||e===b||H||e===x||e===W||e===f||B||e===s||O||L||z||typeof e=="object"&&e!==null&&(e.$$typeof===o||e.$$typeof===m||e.$$typeof===j||e.$$typeof===Y||e.$$typeof===F||e.$$typeof===Tt||e.getModuleId!==void 0))}function ra(e,n){kt(e)||S("memo: The first argument must be a component. Instead received: %s",e===null?"null":typeof e);var c={$$typeof:m,type:e,compare:n===void 0?null:n};{var l;Object.defineProperty(c,"displayName",{enumerable:!1,configurable:!0,get:function(){return l},set:function(h){l=h,!e.name&&!e.displayName&&(e.displayName=h)}})}return c}function oe(){var e=g.current;return e===null&&S(`Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:
1. You might have mismatching versions of React and the renderer (such as React DOM)
2. You might be breaking the Rules of Hooks
3. You might have more than one copy of React in the same app
See https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.`),e}function ta(e){var n=oe();if(e._context!==void 0){var c=e._context;c.Consumer===e?S("Calling useContext(Context.Consumer) is not supported, may cause bugs, and will be removed in a future major release. Did you mean to call useContext(Context) instead?"):c.Provider===e&&S("Calling useContext(Context.Provider) is not supported. Did you mean to call useContext(Context) instead?")}return n.useContext(e)}function na(e){var n=oe();return n.useState(e)}function aa(e,n,c){var l=oe();return l.useReducer(e,n,c)}function ia(e){var n=oe();return n.useRef(e)}function oa(e,n){var c=oe();return c.useEffect(e,n)}function sa(e,n){var c=oe();return c.useInsertionEffect(e,n)}function ua(e,n){var c=oe();return c.useLayoutEffect(e,n)}function ca(e,n){var c=oe();return c.useCallback(e,n)}function la(e,n){var c=oe();return c.useMemo(e,n)}function fa(e,n,c){var l=oe();return l.useImperativeHandle(e,n,c)}function da(e,n){{var c=oe();return c.useDebugValue(e,n)}}function ma(){var e=oe();return e.useTransition()}function ga(e){var n=oe();return n.useDeferredValue(e)}function pa(){var e=oe();return e.useId()}function va(e,n,c){var l=oe();return l.useSyncExternalStore(e,n,c)}var Xe=0,Rt,Ct,Pt,It,Mt,Dt,Lt;function Nt(){}Nt.__reactDisabledLog=!0;function ha(){{if(Xe===0){Rt=console.log,Ct=console.info,Pt=console.warn,It=console.error,Mt=console.group,Dt=console.groupCollapsed,Lt=console.groupEnd;var e={configurable:!0,enumerable:!0,value:Nt,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}Xe++}}function ba(){{if(Xe--,Xe===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:ie({},e,{value:Rt}),info:ie({},e,{value:Ct}),warn:ie({},e,{value:Pt}),error:ie({},e,{value:It}),group:ie({},e,{value:Mt}),groupCollapsed:ie({},e,{value:Dt}),groupEnd:ie({},e,{value:Lt})})}Xe<0&&S("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var Br=k.ReactCurrentDispatcher,Kr;function pr(e,n,c){{if(Kr===void 0)try{throw Error()}catch(h){var l=h.stack.trim().match(/\n( *(at )?)/);Kr=l&&l[1]||""}return`
`+Kr+e}}var Gr=!1,vr;{var wa=typeof WeakMap=="function"?WeakMap:Map;vr=new wa}function jt(e,n){if(!e||Gr)return"";{var c=vr.get(e);if(c!==void 0)return c}var l;Gr=!0;var h=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var T;T=Br.current,Br.current=null,ha();try{if(n){var E=function(){throw Error()};if(Object.defineProperty(E.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(E,[])}catch(ne){l=ne}Reflect.construct(e,[],E)}else{try{E.call()}catch(ne){l=ne}e.call(E.prototype)}}else{try{throw Error()}catch(ne){l=ne}e()}}catch(ne){if(ne&&l&&typeof ne.stack=="string"){for(var M=ne.stack.split(`
`),U=l.stack.split(`
`),q=M.length-1,X=U.length-1;q>=1&&X>=0&&M[q]!==U[X];)X--;for(;q>=1&&X>=0;q--,X--)if(M[q]!==U[X]){if(q!==1||X!==1)do if(q--,X--,X<0||M[q]!==U[X]){var J=`
`+M[q].replace(" at new "," at ");return e.displayName&&J.includes("<anonymous>")&&(J=J.replace("<anonymous>",e.displayName)),typeof e=="function"&&vr.set(e,J),J}while(q>=1&&X>=0);break}}}finally{Gr=!1,Br.current=T,ba(),Error.prepareStackTrace=h}var Q=e?e.displayName||e.name:"",ee=Q?pr(Q):"";return typeof e=="function"&&vr.set(e,ee),ee}function ya(e,n,c){return jt(e,!1)}function _a(e){var n=e.prototype;return!!(n&&n.isReactComponent)}function hr(e,n,c){if(e==null)return"";if(typeof e=="function")return jt(e,_a(e));if(typeof e=="string")return pr(e);switch(e){case W:return pr("Suspense");case f:return pr("SuspenseList")}if(typeof e=="object")switch(e.$$typeof){case F:return ya(e.render);case m:return hr(e.type,n,c);case o:{var l=e,h=l._payload,T=l._init;try{return hr(T(h),n,c)}catch{}}}return""}var Ft={},zt=k.ReactDebugCurrentFrame;function br(e){if(e){var n=e._owner,c=hr(e.type,e._source,n?n.type:null);zt.setExtraStackFrame(c)}else zt.setExtraStackFrame(null)}function Aa(e,n,c,l,h){{var T=Function.call.bind(Oe);for(var E in e)if(T(e,E)){var M=void 0;try{if(typeof e[E]!="function"){var U=Error((l||"React class")+": "+c+" type `"+E+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof e[E]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw U.name="Invariant Violation",U}M=e[E](n,E,l,c,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(q){M=q}M&&!(M instanceof Error)&&(br(h),S("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",l||"React class",c,E,typeof M),br(null)),M instanceof Error&&!(M.message in Ft)&&(Ft[M.message]=!0,br(h),S("Failed %s type: %s",c,M.message),br(null))}}}function Ue(e){if(e){var n=e._owner,c=hr(e.type,e._source,n?n.type:null);y(c)}else y(null)}var qr;qr=!1;function Vt(){if(C.current){var e=he(C.current.type);if(e)return`

Check the render method of \``+e+"`."}return""}function Ea(e){if(e!==void 0){var n=e.fileName.replace(/^.*[\\\/]/,""),c=e.lineNumber;return`

Check your code at `+n+":"+c+"."}return""}function xa(e){return e!=null?Ea(e.__source):""}var Ut={};function Sa(e){var n=Vt();if(!n){var c=typeof e=="string"?e:e.displayName||e.name;c&&(n=`

Check the top-level render call using <`+c+">.")}return n}function Wt(e,n){if(!(!e._store||e._store.validated||e.key!=null)){e._store.validated=!0;var c=Sa(n);if(!Ut[c]){Ut[c]=!0;var l="";e&&e._owner&&e._owner!==C.current&&(l=" It was passed a child from "+he(e._owner.type)+"."),Ue(e),S('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',c,l),Ue(null)}}}function $t(e,n){if(typeof e=="object"){if(Ie(e))for(var c=0;c<e.length;c++){var l=e[c];Ee(l)&&Wt(l,n)}else if(Ee(e))e._store&&(e._store.validated=!0);else if(e){var h=R(e);if(typeof h=="function"&&h!==e.entries)for(var T=h.call(e),E;!(E=T.next()).done;)Ee(E.value)&&Wt(E.value,n)}}}function Bt(e){{var n=e.type;if(n==null||typeof n=="string")return;var c;if(typeof n=="function")c=n.propTypes;else if(typeof n=="object"&&(n.$$typeof===F||n.$$typeof===m))c=n.propTypes;else return;if(c){var l=he(n);Aa(c,e.props,"prop",l,e)}else if(n.PropTypes!==void 0&&!qr){qr=!0;var h=he(n);S("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",h||"Unknown")}typeof n.getDefaultProps=="function"&&!n.getDefaultProps.isReactClassApproved&&S("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}function Oa(e){{for(var n=Object.keys(e.props),c=0;c<n.length;c++){var l=n[c];if(l!=="children"&&l!=="key"){Ue(e),S("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",l),Ue(null);break}}e.ref!==null&&(Ue(e),S("Invalid attribute `ref` supplied to `React.Fragment`."),Ue(null))}}function Kt(e,n,c){var l=kt(e);if(!l){var h="";(e===void 0||typeof e=="object"&&e!==null&&Object.keys(e).length===0)&&(h+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var T=xa(n);T?h+=T:h+=Vt();var E;e===null?E="null":Ie(e)?E="array":e!==void 0&&e.$$typeof===i?(E="<"+(he(e.type)||"Unknown")+" />",h=" Did you accidentally export a JSX literal instead of a component?"):E=typeof e,S("React.createElement: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",E,h)}var M=P.apply(this,arguments);if(M==null)return M;if(l)for(var U=2;U<arguments.length;U++)$t(arguments[U],e);return e===v?Oa(M):Bt(M),M}var Gt=!1;function Ta(e){var n=Kt.bind(null,e);return n.type=e,Gt||(Gt=!0,K("React.createFactory() is deprecated and will be removed in a future major release. Consider using JSX or use React.createElement() directly instead.")),Object.defineProperty(n,"type",{enumerable:!1,get:function(){return K("Factory.type is deprecated. Access the class directly before passing it to createFactory."),Object.defineProperty(this,"type",{value:e}),e}}),n}function ka(e,n,c){for(var l=Ye.apply(this,arguments),h=2;h<arguments.length;h++)$t(arguments[h],l.type);return Bt(l),l}function Ra(e,n){var c=p.transition;p.transition={};var l=p.transition;p.transition._updatedFibers=new Set;try{e()}finally{if(p.transition=c,c===null&&l._updatedFibers){var h=l._updatedFibers.size;h>10&&K("Detected a large number of updates inside startTransition. If this is due to a subscription please re-write it to use React provided hooks. Otherwise concurrent mode guarantees are off the table."),l._updatedFibers.clear()}}}var qt=!1,wr=null;function Ca(e){if(wr===null)try{var n=("require"+Math.random()).slice(0,7),c=r&&r[n];wr=c.call(r,"timers").setImmediate}catch{wr=function(h){qt===!1&&(qt=!0,typeof MessageChannel>"u"&&S("This browser does not have a MessageChannel implementation, so enqueuing tasks via await act(async () => ...) will fail. Please file an issue at https://github.com/facebook/react/issues if you encounter this warning."));var T=new MessageChannel;T.port1.onmessage=h,T.port2.postMessage(void 0)}}return wr(e)}var We=0,Ht=!1;function Yt(e){{var n=We;We++,A.current===null&&(A.current=[]);var c=A.isBatchingLegacy,l;try{if(A.isBatchingLegacy=!0,l=e(),!c&&A.didScheduleLegacyUpdate){var h=A.current;h!==null&&(A.didScheduleLegacyUpdate=!1,Zr(h))}}catch(Q){throw yr(n),Q}finally{A.isBatchingLegacy=c}if(l!==null&&typeof l=="object"&&typeof l.then=="function"){var T=l,E=!1,M={then:function(Q,ee){E=!0,T.then(function(ne){yr(n),We===0?Hr(ne,Q,ee):Q(ne)},function(ne){yr(n),ee(ne)})}};return!Ht&&typeof Promise<"u"&&Promise.resolve().then(function(){}).then(function(){E||(Ht=!0,S("You called act(async () => ...) without await. This could lead to unexpected testing behaviour, interleaving multiple act calls and mixing their scopes. You should - await act(async () => ...);"))}),M}else{var U=l;if(yr(n),We===0){var q=A.current;q!==null&&(Zr(q),A.current=null);var X={then:function(Q,ee){A.current===null?(A.current=[],Hr(U,Q,ee)):Q(U)}};return X}else{var J={then:function(Q,ee){Q(U)}};return J}}}}function yr(e){e!==We-1&&S("You seem to have overlapping act() calls, this is not supported. Be sure to await previous act() calls before making a new one. "),We=e}function Hr(e,n,c){{var l=A.current;if(l!==null)try{Zr(l),Ca(function(){l.length===0?(A.current=null,n(e)):Hr(e,n,c)})}catch(h){c(h)}else n(e)}}var Yr=!1;function Zr(e){if(!Yr){Yr=!0;var n=0;try{for(;n<e.length;n++){var c=e[n];do c=c(!0);while(c!==null)}e.length=0}catch(l){throw e=e.slice(n+1),l}finally{Yr=!1}}}var Pa=Kt,Ia=ka,Ma=Ta,Da={map:gr,forEach:qn,count:Gn,toArray:Hn,only:Yn};t.Children=Da,t.Component=we,t.Fragment=v,t.Profiler=b,t.PureComponent=je,t.StrictMode=x,t.Suspense=W,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=k,t.act=Yt,t.cloneElement=Ia,t.createContext=Zn,t.createElement=Pa,t.createFactory=Ma,t.createRef=Vr,t.forwardRef=ea,t.isValidElement=Ee,t.lazy=Qn,t.memo=ra,t.startTransition=Ra,t.unstable_act=Yt,t.useCallback=ca,t.useContext=ta,t.useDebugValue=da,t.useDeferredValue=ga,t.useEffect=oa,t.useId=pa,t.useImperativeHandle=fa,t.useInsertionEffect=sa,t.useLayoutEffect=ua,t.useMemo=la,t.useReducer=aa,t.useRef=ia,t.useState=na,t.useSyncExternalStore=va,t.useTransition=ma,t.version=a,typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error)})()}(Je,Je.exports);var sn=Je.exports;nt.exports=sn;var xr=nt.exports;const un=Ar(xr);var at={exports:{}},it={},ot={exports:{}},st={};/**
 * @license React
 * use-sync-external-store-shim.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(){typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error);var r=xr,t=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function a(g){{for(var p=arguments.length,A=new Array(p>1?p-1:0),C=1;C<p;C++)A[C-1]=arguments[C];i("error",g,A)}}function i(g,p,A){{var C=t.ReactDebugCurrentFrame,D=C.getStackAddendum();D!==""&&(p+="%s",A=A.concat([D]));var w=A.map(function(y){return String(y)});w.unshift("Warning: "+p),Function.prototype.apply.call(console[g],console,w)}}function d(g,p){return g===p&&(g!==0||1/g===1/p)||g!==g&&p!==p}var v=typeof Object.is=="function"?Object.is:d,x=r.useState,b=r.useEffect,j=r.useLayoutEffect,Y=r.useDebugValue,F=!1,W=!1;function f(g,p,A){F||r.startTransition!==void 0&&(F=!0,a("You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release."));var C=p();if(!W){var D=p();v(C,D)||(a("The result of getSnapshot should be cached to avoid an infinite loop"),W=!0)}var w=x({inst:{value:C,getSnapshot:p}}),y=w[0].inst,O=w[1];return j(function(){y.value=C,y.getSnapshot=p,m(y)&&O({inst:y})},[g,C,p]),b(function(){m(y)&&O({inst:y});var L=function(){m(y)&&O({inst:y})};return g(L)},[g]),Y(C),C}function m(g){var p=g.getSnapshot,A=g.value;try{var C=p();return!v(A,C)}catch{return!0}}function o(g,p,A){return p()}var s=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",u=!s,_=u?o:f,R=r.useSyncExternalStore!==void 0?r.useSyncExternalStore:_;st.useSyncExternalStore=R,typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error)})(),ot.exports=st;var cn=ot.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(){typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error);var r=xr,t=cn;function a(F,W){return F===W&&(F!==0||1/F===1/W)||F!==F&&W!==W}var i=typeof Object.is=="function"?Object.is:a,d=t.useSyncExternalStore,v=r.useRef,x=r.useEffect,b=r.useMemo,j=r.useDebugValue;function Y(F,W,f,m,o){var s=v(null),u;s.current===null?(u={hasValue:!1,value:null},s.current=u):u=s.current;var _=b(function(){var A=!1,C,D,w=function(z){if(!A){A=!0,C=z;var B=m(z);if(o!==void 0&&u.hasValue){var H=u.value;if(o(H,B))return D=H,H}return D=B,B}var k=C,K=D;if(i(k,z))return K;var S=m(z);return o!==void 0&&o(K,S)?K:(C=z,D=S,S)},y=f===void 0?null:f,O=function(){return w(W())},L=y===null?void 0:function(){return w(y())};return[O,L]},[W,f,m,o]),R=_[0],g=_[1],p=d(F,R,g);return x(function(){u.hasValue=!0,u.value=p},[p]),j(p),p}it.useSyncExternalStoreWithSelector=Y,typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error)})(),at.exports=it;var ln=at.exports;const fn=Ar(ln);var ut={VITE_CJS_IGNORE_WARNING:"true",BASE_URL:"/",MODE:"prod",DEV:!1,PROD:!0,SSR:!1,MANIFEST_VERSION:3,BROWSER:"chrome",CHROME:!0,FIREFOX:!1,SAFARI:!1,EDGE:!1,OPERA:!1,COMMAND:"build",ENTRYPOINT:"background"};const{useDebugValue:dn}=un,{useSyncExternalStoreWithSelector:mn}=fn;let ct=!1;const gn=r=>r;function pn(r,t=gn,a){(ut?"prod":void 0)!=="production"&&a&&!ct&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),ct=!0);const i=mn(r.subscribe,r.getState,r.getServerState||r.getInitialState,t,a);return dn(i),i}const lt=r=>{(ut?"prod":void 0)!=="production"&&typeof r!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t=typeof r=="function"?on(r):r,a=(i,d)=>pn(t,i,d);return Object.assign(a,t),a},vn=r=>r?lt(r):lt,hn=r=>(t,a,i)=>{const d=i.subscribe;return i.subscribe=(x,b,j)=>{let Y=x;if(b){const F=(j==null?void 0:j.equalityFn)||Object.is;let W=x(i.getState());Y=f=>{const m=x(f);if(!F(W,m)){const o=W;b(W=m,o)}},j!=null&&j.fireImmediately&&b(W,W)}return d(Y)},r(t,a,i)};var ft=Symbol.for("immer-nothing"),dt=Symbol.for("immer-draftable"),le=Symbol.for("immer-state"),bn=[function(r){return`The plugin for '${r}' has not been loaded into Immer. To enable the plugin, import and call \`enable${r}()\` when initializing your application.`},function(r){return`produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '${r}'`},"This object has been frozen and should not be mutated",function(r){return"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? "+r},"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.","Immer forbids circular references","The first or second argument to `produce` must be a function","The third argument to `produce` must be a function or undefined","First argument to `createDraft` must be a plain object, an array, or an immerable object","First argument to `finishDraft` must be a draft returned by `createDraft`",function(r){return`'current' expects a draft, got: ${r}`},"Object.defineProperty() cannot be used on an Immer draft","Object.setPrototypeOf() cannot be used on an Immer draft","Immer only supports deleting array indices","Immer only supports setting array indices and the 'length' property",function(r){return`'original' expects a draft, got: ${r}`}];function se(r,...t){{const a=bn[r],i=typeof a=="function"?a.apply(null,t):a;throw new Error(`[Immer] ${i}`)}}var Le=Object.getPrototypeOf;function Ne(r){return!!r&&!!r[le]}function Te(r){var t;return r?mt(r)||Array.isArray(r)||!!r[dt]||!!((t=r.constructor)!=null&&t[dt])||rr(r)||tr(r):!1}var wn=Object.prototype.constructor.toString();function mt(r){if(!r||typeof r!="object")return!1;const t=Le(r);if(t===null)return!0;const a=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return a===Object?!0:typeof a=="function"&&Function.toString.call(a)===wn}function Qe(r,t){er(r)===0?Reflect.ownKeys(r).forEach(a=>{t(a,r[a],r)}):r.forEach((a,i)=>t(i,a,r))}function er(r){const t=r[le];return t?t.type_:Array.isArray(r)?1:rr(r)?2:tr(r)?3:0}function Sr(r,t){return er(r)===2?r.has(t):Object.prototype.hasOwnProperty.call(r,t)}function gt(r,t,a){const i=er(r);i===2?r.set(t,a):i===3?r.add(a):r[t]=a}function yn(r,t){return r===t?r!==0||1/r===1/t:r!==r&&t!==t}function rr(r){return r instanceof Map}function tr(r){return r instanceof Set}function ke(r){return r.copy_||r.base_}function Or(r,t){if(rr(r))return new Map(r);if(tr(r))return new Set(r);if(Array.isArray(r))return Array.prototype.slice.call(r);const a=mt(r);if(t===!0||t==="class_only"&&!a){const i=Object.getOwnPropertyDescriptors(r);delete i[le];let d=Reflect.ownKeys(i);for(let v=0;v<d.length;v++){const x=d[v],b=i[x];b.writable===!1&&(b.writable=!0,b.configurable=!0),(b.get||b.set)&&(i[x]={configurable:!0,writable:!0,enumerable:b.enumerable,value:r[x]})}return Object.create(Le(r),i)}else{const i=Le(r);if(i!==null&&a)return{...r};const d=Object.create(i);return Object.assign(d,r)}}function Tr(r,t=!1){return nr(r)||Ne(r)||!Te(r)||(er(r)>1&&(r.set=r.add=r.clear=r.delete=_n),Object.freeze(r),t&&Object.entries(r).forEach(([a,i])=>Tr(i,!0))),r}function _n(){se(2)}function nr(r){return Object.isFrozen(r)}var An={};function Re(r){const t=An[r];return t||se(0,r),t}var Be;function pt(){return Be}function En(r,t){return{drafts_:[],parent_:r,immer_:t,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function vt(r,t){t&&(Re("Patches"),r.patches_=[],r.inversePatches_=[],r.patchListener_=t)}function kr(r){Rr(r),r.drafts_.forEach(xn),r.drafts_=null}function Rr(r){r===Be&&(Be=r.parent_)}function ht(r){return Be=En(Be,r)}function xn(r){const t=r[le];t.type_===0||t.type_===1?t.revoke_():t.revoked_=!0}function bt(r,t){t.unfinalizedDrafts_=t.drafts_.length;const a=t.drafts_[0];return r!==void 0&&r!==a?(a[le].modified_&&(kr(t),se(4)),Te(r)&&(r=ar(t,r),t.parent_||ir(t,r)),t.patches_&&Re("Patches").generateReplacementPatches_(a[le].base_,r,t.patches_,t.inversePatches_)):r=ar(t,a,[]),kr(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),r!==ft?r:void 0}function ar(r,t,a){if(nr(t))return t;const i=t[le];if(!i)return Qe(t,(d,v)=>wt(r,i,t,d,v,a)),t;if(i.scope_!==r)return t;if(!i.modified_)return ir(r,i.base_,!0),i.base_;if(!i.finalized_){i.finalized_=!0,i.scope_.unfinalizedDrafts_--;const d=i.copy_;let v=d,x=!1;i.type_===3&&(v=new Set(d),d.clear(),x=!0),Qe(v,(b,j)=>wt(r,i,d,b,j,a,x)),ir(r,d,!1),a&&r.patches_&&Re("Patches").generatePatches_(i,a,r.patches_,r.inversePatches_)}return i.copy_}function wt(r,t,a,i,d,v,x){if(d===a&&se(5),Ne(d)){const b=v&&t&&t.type_!==3&&!Sr(t.assigned_,i)?v.concat(i):void 0,j=ar(r,d,b);if(gt(a,i,j),Ne(j))r.canAutoFreeze_=!1;else return}else x&&a.add(d);if(Te(d)&&!nr(d)){if(!r.immer_.autoFreeze_&&r.unfinalizedDrafts_<1)return;ar(r,d),(!t||!t.scope_.parent_)&&typeof i!="symbol"&&Object.prototype.propertyIsEnumerable.call(a,i)&&ir(r,d)}}function ir(r,t,a=!1){!r.parent_&&r.immer_.autoFreeze_&&r.canAutoFreeze_&&Tr(t,a)}function Sn(r,t){const a=Array.isArray(r),i={type_:a?1:0,scope_:t?t.scope_:pt(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:r,draft_:null,copy_:null,revoke_:null,isManual_:!1};let d=i,v=Cr;a&&(d=[i],v=Ke);const{revoke:x,proxy:b}=Proxy.revocable(d,v);return i.draft_=b,i.revoke_=x,b}var Cr={get(r,t){if(t===le)return r;const a=ke(r);if(!Sr(a,t))return On(r,a,t);const i=a[t];return r.finalized_||!Te(i)?i:i===Pr(r.base_,t)?(Mr(r),r.copy_[t]=Dr(i,r)):i},has(r,t){return t in ke(r)},ownKeys(r){return Reflect.ownKeys(ke(r))},set(r,t,a){const i=yt(ke(r),t);if(i!=null&&i.set)return i.set.call(r.draft_,a),!0;if(!r.modified_){const d=Pr(ke(r),t),v=d==null?void 0:d[le];if(v&&v.base_===a)return r.copy_[t]=a,r.assigned_[t]=!1,!0;if(yn(a,d)&&(a!==void 0||Sr(r.base_,t)))return!0;Mr(r),Ir(r)}return r.copy_[t]===a&&(a!==void 0||t in r.copy_)||Number.isNaN(a)&&Number.isNaN(r.copy_[t])||(r.copy_[t]=a,r.assigned_[t]=!0),!0},deleteProperty(r,t){return Pr(r.base_,t)!==void 0||t in r.base_?(r.assigned_[t]=!1,Mr(r),Ir(r)):delete r.assigned_[t],r.copy_&&delete r.copy_[t],!0},getOwnPropertyDescriptor(r,t){const a=ke(r),i=Reflect.getOwnPropertyDescriptor(a,t);return i&&{writable:!0,configurable:r.type_!==1||t!=="length",enumerable:i.enumerable,value:a[t]}},defineProperty(){se(11)},getPrototypeOf(r){return Le(r.base_)},setPrototypeOf(){se(12)}},Ke={};Qe(Cr,(r,t)=>{Ke[r]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),Ke.deleteProperty=function(r,t){return isNaN(parseInt(t))&&se(13),Ke.set.call(this,r,t,void 0)},Ke.set=function(r,t,a){return t!=="length"&&isNaN(parseInt(t))&&se(14),Cr.set.call(this,r[0],t,a,r[0])};function Pr(r,t){const a=r[le];return(a?ke(a):r)[t]}function On(r,t,a){var d;const i=yt(t,a);return i?"value"in i?i.value:(d=i.get)==null?void 0:d.call(r.draft_):void 0}function yt(r,t){if(!(t in r))return;let a=Le(r);for(;a;){const i=Object.getOwnPropertyDescriptor(a,t);if(i)return i;a=Le(a)}}function Ir(r){r.modified_||(r.modified_=!0,r.parent_&&Ir(r.parent_))}function Mr(r){r.copy_||(r.copy_=Or(r.base_,r.scope_.immer_.useStrictShallowCopy_))}var Tn=class{constructor(r){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(t,a,i)=>{if(typeof t=="function"&&typeof a!="function"){const v=a;a=t;const x=this;return function(j=v,...Y){return x.produce(j,F=>a.call(this,F,...Y))}}typeof a!="function"&&se(6),i!==void 0&&typeof i!="function"&&se(7);let d;if(Te(t)){const v=ht(this),x=Dr(t,void 0);let b=!0;try{d=a(x),b=!1}finally{b?kr(v):Rr(v)}return vt(v,i),bt(d,v)}else if(!t||typeof t!="object"){if(d=a(t),d===void 0&&(d=t),d===ft&&(d=void 0),this.autoFreeze_&&Tr(d,!0),i){const v=[],x=[];Re("Patches").generateReplacementPatches_(t,d,v,x),i(v,x)}return d}else se(1,t)},this.produceWithPatches=(t,a)=>{if(typeof t=="function")return(x,...b)=>this.produceWithPatches(x,j=>t(j,...b));let i,d;return[this.produce(t,a,(x,b)=>{i=x,d=b}),i,d]},typeof(r==null?void 0:r.autoFreeze)=="boolean"&&this.setAutoFreeze(r.autoFreeze),typeof(r==null?void 0:r.useStrictShallowCopy)=="boolean"&&this.setUseStrictShallowCopy(r.useStrictShallowCopy)}createDraft(r){Te(r)||se(8),Ne(r)&&(r=kn(r));const t=ht(this),a=Dr(r,void 0);return a[le].isManual_=!0,Rr(t),a}finishDraft(r,t){const a=r&&r[le];(!a||!a.isManual_)&&se(9);const{scope_:i}=a;return vt(i,t),bt(void 0,i)}setAutoFreeze(r){this.autoFreeze_=r}setUseStrictShallowCopy(r){this.useStrictShallowCopy_=r}applyPatches(r,t){let a;for(a=t.length-1;a>=0;a--){const d=t[a];if(d.path.length===0&&d.op==="replace"){r=d.value;break}}a>-1&&(t=t.slice(a+1));const i=Re("Patches").applyPatches_;return Ne(r)?i(r,t):this.produce(r,d=>i(d,t))}};function Dr(r,t){const a=rr(r)?Re("MapSet").proxyMap_(r,t):tr(r)?Re("MapSet").proxySet_(r,t):Sn(r,t);return(t?t.scope_:pt()).drafts_.push(a),a}function kn(r){return Ne(r)||se(10,r),_t(r)}function _t(r){if(!Te(r)||nr(r))return r;const t=r[le];let a;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,a=Or(r,t.scope_.immer_.useStrictShallowCopy_)}else a=Or(r,!0);return Qe(a,(i,d)=>{gt(a,i,_t(d))}),t&&(t.finalized_=!1),a}var fe=new Tn,Rn=fe.produce;fe.produceWithPatches.bind(fe),fe.setAutoFreeze.bind(fe),fe.setUseStrictShallowCopy.bind(fe),fe.applyPatches.bind(fe),fe.createDraft.bind(fe),fe.finishDraft.bind(fe);const Cn=r=>(t,a,i)=>(i.setState=(d,v,...x)=>{const b=typeof d=="function"?Rn(d):d;return t(b,v,...x)},r(i.setState,a,i));function Pn(r){const{state:t,actions:a}=r,i=vn(hn(Cn(()=>t)));return{useState:i,getState:i.getState,resetState:()=>i.setState(t),subscribe:i.subscribe,...a==null?void 0:a({getState:i.getState,setState:i.setState})}}function In(r){return Array.isArray(r)?r:[r]}var At=Object.prototype.hasOwnProperty;function Lr(r,t){var a,i;if(r===t)return!0;if(r&&t&&(a=r.constructor)===t.constructor){if(a===Date)return r.getTime()===t.getTime();if(a===RegExp)return r.toString()===t.toString();if(a===Array){if((i=r.length)===t.length)for(;i--&&Lr(r[i],t[i]););return i===-1}if(!a||typeof r=="object"){i=0;for(a in r)if(At.call(r,a)&&++i&&!At.call(t,a)||!(a in t)||!Lr(r[a],t[a]))return!1;return Object.keys(t).length===i}}return r!==r&&t!==t}var Nr=et;function or(r,...t){if(typeof t[0]=="string"){const a=t.shift();r(`[wxt] ${a}`,...t)}else r("[wxt]",...t)}var jr={debug:(...r)=>or(console.debug,...r),log:(...r)=>or(console.log,...r),warn:(...r)=>or(console.warn,...r),error:(...r)=>or(console.error,...r)},sr=Mn();function Mn(){const r={local:ur("local"),session:ur("session"),sync:ur("sync"),managed:ur("managed")},t=o=>{const s=r[o];if(s==null){const u=Object.keys(r).join(", ");throw Error(`Invalid area "${o}". Options: ${u}`)}return s},a=o=>{const s=o.indexOf(":"),u=o.substring(0,s),_=o.substring(s+1);if(_==null)throw Error(`Storage key should be in the form of "area:key", but received "${o}"`);return{driverArea:u,driverKey:_,driver:t(u)}},i=o=>o+"$",d=(o,s)=>o??s??null,v=o=>typeof o=="object"&&!Array.isArray(o)?o:{},x=async(o,s,u)=>{const _=await o.getItem(s);return d(_,u==null?void 0:u.defaultValue)},b=async(o,s)=>{const u=i(s),_=await o.getItem(u);return v(_)},j=async(o,s,u)=>{await o.setItem(s,u??null)},Y=async(o,s,u)=>{const _=i(s),g={...v(await o.getItem(_))};Object.entries(u).forEach(([p,A])=>{A==null?delete g[p]:g[p]=A}),await o.setItem(_,g)},F=async(o,s,u)=>{if(await o.removeItem(s),u!=null&&u.removeMeta){const _=i(s);await o.removeItem(_)}},W=async(o,s,u)=>{const _=i(s);if(u==null)await o.removeItem(_);else{const R=v(await o.getItem(_));In(u).forEach(g=>delete R[g]),await o.setItem(_,R)}},f=(o,s,u)=>o.watch(s,u);return{getItem:async(o,s)=>{const{driver:u,driverKey:_}=a(o);return await x(u,_,s)},getItems:async o=>{const s=new Map,u=new Map;return o.forEach(R=>{let g,p;typeof R=="string"?g=R:(g=R.key,p=R.options);const{driverArea:A,driverKey:C}=a(g),D=s.get(A)??[];s.set(A,D.concat(C)),u.set(g,p)}),(await Promise.all(Array.from(s.entries()).map(async([R,g])=>(await r[R].getItems(g)).map(A=>{var w;const C=`${R}:${A.key}`,D=d(A.value,(w=u.get(C))==null?void 0:w.defaultValue);return{key:C,value:D}})))).flat()},getMeta:async o=>{const{driver:s,driverKey:u}=a(o);return await b(s,u)},setItem:async(o,s)=>{const{driver:u,driverKey:_}=a(o);await j(u,_,s)},setItems:async o=>{const s=new Map;o.forEach(({key:u,value:_})=>{const{driverArea:R,driverKey:g}=a(u),p=s.get(R)??[];s.set(R,p.concat({key:g,value:_}))}),await Promise.all(Array.from(s.entries()).map(async([u,_])=>{await t(u).setItems(_)}))},setMeta:async(o,s)=>{const{driver:u,driverKey:_}=a(o);await Y(u,_,s)},removeItem:async(o,s)=>{const{driver:u,driverKey:_}=a(o);await F(u,_,s)},removeItems:async o=>{const s=new Map;o.forEach(u=>{let _,R;typeof u=="string"?_=u:(_=u.key,R=u.options);const{driverArea:g,driverKey:p}=a(_),A=s.get(g)??[];A.push(p),R!=null&&R.removeMeta&&A.push(i(p)),s.set(g,A)}),await Promise.all(Array.from(s.entries()).map(async([u,_])=>{await t(u).removeItems(_)}))},removeMeta:async(o,s)=>{const{driver:u,driverKey:_}=a(o);await W(u,_,s)},snapshot:async(o,s)=>{var R;const _=await t(o).snapshot();return(R=s==null?void 0:s.excludeKeys)==null||R.forEach(g=>{delete _[g],delete _[i(g)]}),_},restoreSnapshot:async(o,s)=>{await t(o).restoreSnapshot(s)},watch:(o,s)=>{const{driver:u,driverKey:_}=a(o);return f(u,_,s)},unwatch(){Object.values(r).forEach(o=>{o.unwatch()})},defineItem:(o,s)=>{const{driver:u,driverKey:_}=a(o),{version:R=1,migrations:g={}}=s??{};if(R<1)throw Error("Storage item version cannot be less than 1. Initial versions should be set to 1, not 0.");const p=async()=>{var B;const D=i(_),[{value:w},{value:y}]=await u.getItems([_,D]);if(w==null)return;const O=(y==null?void 0:y.v)??1;if(O>R)throw Error(`Version downgrade detected (v${O} -> v${R}) for "${o}"`);jr.debug(`Running storage migration for ${o}: v${O} -> v${R}`);const L=Array.from({length:R-O},(H,k)=>O+k+1);let z=w;for(const H of L)z=await((B=g==null?void 0:g[H])==null?void 0:B.call(g,z))??z;await u.setItems([{key:_,value:z},{key:D,value:{...y,v:R}}]),jr.debug(`Storage migration completed for ${o} v${R}`,{migratedValue:z})},A=(s==null?void 0:s.migrations)==null?Promise.resolve():p().catch(D=>{jr.error(`Migration failed for ${o}`,D)}),C=()=>(s==null?void 0:s.defaultValue)??null;return{get defaultValue(){return C()},getValue:async()=>(await A,await x(u,_,s)),getMeta:async()=>(await A,await b(u,_)),setValue:async D=>(await A,await j(u,_,D)),setMeta:async D=>(await A,await Y(u,_,D)),removeValue:async D=>(await A,await F(u,_,D)),removeMeta:async D=>(await A,await W(u,_,D)),watch:D=>f(u,_,(w,y)=>D(w??C(),y??C())),migrate:p}}}}function ur(r){const t=()=>{if(Nr.runtime==null)throw Error(["'wxt/storage' must be loaded in a web extension environment",`
 - If thrown during a build, see https://github.com/wxt-dev/wxt/issues/371`,` - If thrown during tests, mock 'wxt/browser' correctly. See https://wxt.dev/guide/go-further/testing.html
`].join(`
`));if(Nr.storage==null)throw Error("You must add the 'storage' permission to your manifest to use 'wxt/storage'");const i=Nr.storage[r];if(i==null)throw Error(`"browser.storage.${r}" is undefined`);return i},a=new Set;return{getItem:async i=>(await t().get(i))[i],getItems:async i=>{const d=await t().get(i);return i.map(v=>({key:v,value:d[v]??null}))},setItem:async(i,d)=>{d==null?await t().remove(i):await t().set({[i]:d})},setItems:async i=>{const d=i.reduce((v,{key:x,value:b})=>(v[x]=b,v),{});await t().set(d)},removeItem:async i=>{await t().remove(i)},removeItems:async i=>{await t().remove(i)},snapshot:async()=>await t().get(),restoreSnapshot:async i=>{await t().set(i)},watch(i,d){const v=x=>{const b=x[i];b!=null&&(Lr(b.newValue,b.oldValue)||d(b.newValue??null,b.oldValue??null))};return t().onChanged.addListener(v),a.add(v),()=>{t().onChanged.removeListener(v),a.delete(v)}},unwatch(){a.forEach(i=>{t().onChanged.removeListener(i)}),a.clear()}}}function cr(r){return`local:${r}`}const ve={async getItem(r){return await sr.getItem(cr(r))??ae.storage[r]},async setItem(r,t){return sr.setItem(cr(r),t)},async removeItem(r){return sr.removeItem(cr(r))},watchItem(r,t){return ve.getItem(r).then(a=>t(a,null)),sr.watch(cr(r),(a,i)=>{t(a??ae.storage[r],i)})}},lr={async getToken(){return ve.getItem("access_token")},async setToken({access_token:r,refresh_token:t}){return Promise.all([ve.setItem("access_token",r),ve.setItem("refresh_token",t)])},async clearToken(){return Promise.all([ve.removeItem("access_token"),ve.removeItem("refresh_token")])},async refreshToken(){const r=await ve.getItem("refresh_token");if(!r)return Promise.reject("failed to get refresh token");const t=await fetch(ae.url+"/api/auth/token/refresh",{headers:{Authorization:`Bearer ${r}`}}),a=await t.json();return!t.ok||!(a!=null&&a.access_token)||!(a!=null&&a.refresh_token)?(await lr.clearToken(),Promise.reject("failed to refresh token")):lr.setToken(a)}},Fr={tab(r,t){var a;return r!=null&&r.id&&r.active&&r.status==="complete"&&((a=r.url)!=null&&a.startsWith("http"))?(t==null||t(r),!0):!1},authContent(r,t){const{token:a}=te.getState();return a?(t==null||t(),!0):(r&&window.location.href!==$e.login&&window.open($e.login),!1)},authSidePanel(r){const{token:t}=te.getState();return t?(r==null||r(),!0):(Z.tabs.create({url:$e.login}),!1)}},Dn={async screenshot(){const r=await navigator.mediaDevices.getDisplayMedia({video:!0,audio:!1,preferCurrentTab:!0}),t=document.createElement("video");t.srcObject=r,t.play(),await new Promise(v=>t.onloadedmetadata=v);const a=document.createElement("canvas");a.width=t.videoWidth,a.height=t.videoHeight;const i=a.getContext("2d");i==null||i.drawImage(t,0,0,a.width,a.height);const d=a.toDataURL();return r.getTracks().forEach(v=>v.stop()),t.remove(),a.remove(),d}},{watchItem:Ce}=ve,te=Pn({state:{env:"content",token:"",shortcut:void 0,pdfMap:{},browser_name:"",version:ae.storage.version,explore_toolbar:ae.storage.explore_toolbar,explore_underline:ae.storage.explore_underline,chat_float_button:ae.storage.chat_float_button,chat_window_mode:ae.storage.chat_window_mode},actions:({getState:r,setState:t})=>({checkContent(){return r().env==="content"},async initEnv(a,i){var v,x;t({env:a});const{onLogout:d}=i||{};return Ce("access_token",(b,j)=>{t({token:b}),!b&&j&&(d==null||d())}),Ce("version",b=>t({version:b})),Ce("browser_name",b=>t({browser_name:b})),Ce("explore_toolbar",b=>t({explore_toolbar:b})),Ce("explore_underline",b=>t({explore_underline:b})),Ce("chat_float_button",b=>t({chat_float_button:b})),Ce("chat_window_mode",b=>t({chat_window_mode:b})),te.checkContent()&&ve.setItem("browser_name",((x=(v=nn.UAParser())==null?void 0:v.browser)==null?void 0:x.name)||"Chrome"),await ve.getItem("access_token"),await te.getCommands(),r()},closeChangelog(){ve.setItem("version",ae.version)},closeSidePanel(){Z.runtime.sendMessage({action:ce.close_sidepanel})},openSidePanel(a){var i;return te.checkContent()?Z.runtime.sendMessage({action:ce.open_sidepanel}):(i=Z.sidePanel)==null?void 0:i.open({tabId:a})},newTab(a){te.checkContent()?Z.runtime.sendMessage({action:ce.new_tab,payload:a}):Z.tabs.create({url:a})},async getCommands(){var a,i;if(te.checkContent()){const d=await Z.runtime.sendMessage({action:ce.get_commands});return t({shortcut:d}),d}else{const d=await Z.commands.getAll(),v={commands:d,chat:((a=d.find(x=>x.name==="chat"))==null?void 0:a.shortcut)||"",explore:((i=d.find(x=>x.name==="explore"))==null?void 0:i.shortcut)||""};return t({shortcut:v}),v}},async screenshot(){return te.checkContent()?Z.runtime.sendMessage({action:ce.screenshot}):Z.tabs.captureVisibleTab()},async screenshotWithWebRTC(){if(te.checkContent())return Dn.screenshot();{const[a]=await Z.tabs.query({active:!0,currentWindow:!0});return a!=null&&a.id?Z.tabs.sendMessage(a.id,{action:ce.screenshot}):Promise.reject()}},async getWebInfo(){var a;if(te.checkContent()){const i=document.title,d=window.location.href;let v=r().pdfMap[d];if(v===void 0){const x=await fetch(d).catch(()=>null);v=((a=x==null?void 0:x.headers)==null?void 0:a.get("content-type"))==="application/pdf",t(b=>{b.pdfMap[d]=v})}return{url:d,title:i,pdf:v,content:document.body.innerText||i+`
`+d,width:window.innerWidth,height:window.innerHeight}}else{const[i]=await Z.tabs.query({active:!0,currentWindow:!0});return new Promise((d,v)=>{Fr.tab(i,({id:b})=>{d(Z.tabs.sendMessage(b,{action:ce.get_web_info}))})||v()})}}})});async function Ln(){lr.refreshToken().catch(()=>!1),setInterval(()=>lr.refreshToken().catch(()=>!1),3e5)}function Nn(){Z.commands.onCommand.addListener((r,t)=>{Fr.tab(t,({id:a})=>{const{shortcut:i,chat_window_mode:d}=te.getState();r==="chat"&&d==="sidepanel"&&te.openSidePanel(a),Z.tabs.sendMessage(a,{action:ce.command,payload:i==null?void 0:i.commands.find(v=>v.name===r)})})})}function jn(){Z.runtime.setUninstallURL($e.uninstall),Z.runtime.onInstalled.addListener(({reason:r})=>{r==="install"&&Z.tabs.create({url:$e.login})})}function Fn(){Z.omnibox.onInputEntered.addListener(r=>{const a=`/_prefill_chat?prefill_prompt=${encodeURIComponent(r)}&send_immediately=true`;Z.tabs.update({url:ae.url+a})})}function zn(){Z.tabs.onUpdated.addListener((r,t,a)=>{Fr.tab(a,({id:i})=>{Z.tabs.sendMessage(i,{action:ce.url_changed})})})}const Vn=Qt(()=>{te.initEnv("background"),Ln(),Nn(),jn(),Fn(),zn(),Z.runtime.onMessage.addListener(async(r,t)=>{var v;const a=(v=t==null?void 0:t.tab)==null?void 0:v.id,{action:i="",payload:d}=r||{};if(i===ce.open_sidepanel&&a)return te.openSidePanel(a);if(i===ce.new_tab)return te.newTab(d);if(i===ce.get_commands)return te.getCommands();if(i===ce.screenshot)return te.screenshot()})});function Va(){}function fr(r,...t){if(typeof t[0]=="string"){const a=t.shift();r(`[wxt] ${a}`,...t)}else r("[wxt]",...t)}var Un={debug:(...r)=>fr(console.debug,...r),log:(...r)=>fr(console.log,...r),warn:(...r)=>fr(console.warn,...r),error:(...r)=>fr(console.error,...r)},zr;try{zr=Vn.main(),zr instanceof Promise&&console.warn("The background's main() function return a promise, but it must be synchronous")}catch(r){throw Un.error("The background crashed on startup!"),r}var Wn=zr;return Wn}();
_background;
