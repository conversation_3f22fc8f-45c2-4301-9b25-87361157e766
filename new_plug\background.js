// Background script for AI Assistant Chrome Extension

// Import configuration and API service
importScripts('config.js', 'api-service.js');

// Initialize extension
chrome.runtime.onInstalled.addListener((details) => {
  console.log('AI Assistant extension installed/updated');

  // Set default settings on first install
  if (details.reason === 'install') {
    chrome.storage.sync.set({
      [CONFIG.STORAGE_KEYS.SETTINGS]: CONFIG.DEFAULT_SETTINGS
    });
  }
});

// Handle action button click (open sidepanel)
chrome.action.onClicked.addListener(async (tab) => {
  try {
    await chrome.sidePanel.open({ tabId: tab.id });
  } catch (error) {
    console.error('Error opening side panel:', error);
  }
});

// Handle keyboard shortcuts
chrome.commands.onCommand.addListener(async (command) => {
  console.log('Command received:', command);
  
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    
    if (command === 'chat') {
      // Open chat in sidepanel or modal based on settings
      const settings = await getSettings();
      if (settings.windowMode === 'sidepanel') {
        await chrome.sidePanel.open({ tabId: tab.id });
      } else {
        // Send message to content script to open modal
        chrome.tabs.sendMessage(tab.id, { 
          action: 'openChatModal' 
        });
      }
    } else if (command === 'explore') {
      // Send message to content script to start explore mode
      chrome.tabs.sendMessage(tab.id, { 
        action: 'startExploreMode' 
      });
    }
  } catch (error) {
    console.error('Error handling command:', error);
  }
});

// Handle messages from content scripts and popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Background received message:', message);
  
  switch (message.action) {
    case 'sendChatMessage':
      handleChatMessage(message.data, sendResponse);
      return true; // Keep message channel open for async response
      
    case 'analyzeSelectedText':
      handleAnalyzeText(message.data, sendResponse);
      return true;
      
    case 'analyzeScreenshot':
      handleAnalyzeScreenshot(message.data, sendResponse);
      return true;
      
    case 'getSettings':
      getSettings().then(sendResponse);
      return true;
      
    case 'updateSettings':
      updateSettings(message.data).then(sendResponse);
      return true;
      
    case 'openSidePanel':
      handleOpenSidePanel(sender.tab.id);
      break;
      
    case 'takeScreenshot':
      handleTakeScreenshot(sendResponse);
      return true;
  }
});

// Chat message handler
async function handleChatMessage(data, sendResponse) {
  try {
    const { message, context = [] } = data;
    const response = await openRouterService.sendTextMessage(message, context);
    sendResponse({ success: true, data: response });
  } catch (error) {
    console.error('Chat message error:', error);
    sendResponse({ 
      success: false, 
      error: error.message || CONFIG.MESSAGES.ERROR 
    });
  }
}

// Analyze selected text handler
async function handleAnalyzeText(data, sendResponse) {
  try {
    const { selectedText, pageContext } = data;
    const response = await openRouterService.analyzeSelectedText(selectedText, pageContext);
    sendResponse({ success: true, data: response });
  } catch (error) {
    console.error('Analyze text error:', error);
    sendResponse({ 
      success: false, 
      error: error.message || CONFIG.MESSAGES.ERROR 
    });
  }
}

// Analyze screenshot handler
async function handleAnalyzeScreenshot(data, sendResponse) {
  try {
    const { imageDataUrl, prompt } = data;
    const response = await openRouterService.analyzeScreenshot(imageDataUrl, prompt);
    sendResponse({ success: true, data: response });
  } catch (error) {
    console.error('Analyze screenshot error:', error);
    sendResponse({ 
      success: false, 
      error: error.message || CONFIG.MESSAGES.ERROR 
    });
  }
}

// Settings management
async function getSettings() {
  try {
    const result = await chrome.storage.sync.get([CONFIG.STORAGE_KEYS.SETTINGS]);
    return result[CONFIG.STORAGE_KEYS.SETTINGS] || CONFIG.DEFAULT_SETTINGS;
  } catch (error) {
    console.error('Error getting settings:', error);
    return CONFIG.DEFAULT_SETTINGS;
  }
}

async function updateSettings(newSettings) {
  try {
    const currentSettings = await getSettings();
    const updatedSettings = { ...currentSettings, ...newSettings };
    await chrome.storage.sync.set({
      [CONFIG.STORAGE_KEYS.SETTINGS]: updatedSettings
    });
    
    // Update API service settings
    if (newSettings.apiKey) {
      await openRouterService.updateApiKey(newSettings.apiKey);
    }
    
    return { success: true };
  } catch (error) {
    console.error('Error updating settings:', error);
    return { success: false, error: error.message };
  }
}

// Open side panel
async function handleOpenSidePanel(tabId) {
  try {
    await chrome.sidePanel.open({ tabId });
  } catch (error) {
    console.error('Error opening side panel:', error);
  }
}

// Take screenshot
async function handleTakeScreenshot(sendResponse) {
  try {
    const dataUrl = await chrome.tabs.captureVisibleTab(null, { format: 'png' });
    sendResponse({ success: true, data: { imageDataUrl: dataUrl } });
  } catch (error) {
    console.error('Screenshot error:', error);
    sendResponse({ 
      success: false, 
      error: 'Failed to capture screenshot' 
    });
  }
}
