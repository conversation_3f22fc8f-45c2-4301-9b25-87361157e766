/* Content script styles for AI Assistant Chrome Extension */

/* Float Button */
#ai-assistant-float-button {
  position: fixed !important;
  top: 50% !important;
  right: 20px !important;
  width: 56px !important;
  height: 56px !important;
  background: #4285f4 !important;
  border-radius: 50% !important;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
  cursor: pointer !important;
  z-index: 10000 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.3s ease !important;
  transform: translateY(-50%) !important;
  border: none !important;
  outline: none !important;
}

#ai-assistant-float-button:hover {
  transform: translateY(-50%) scale(1.1) !important;
  box-shadow: 0 6px 16px rgba(0,0,0,0.2) !important;
}

#ai-assistant-float-button .float-button-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Explore Mode Indicator */
#ai-assistant-explore-indicator {
  position: fixed !important;
  top: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  background: rgba(0,0,0,0.8) !important;
  color: white !important;
  padding: 12px 20px !important;
  border-radius: 24px !important;
  z-index: 10001 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-size: 14px !important;
  box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;
  border: none !important;
  outline: none !important;
}

#ai-assistant-explore-indicator .explore-indicator-content {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

#ai-assistant-explore-indicator .explore-exit {
  background: none !important;
  border: none !important;
  color: white !important;
  cursor: pointer !important;
  font-size: 18px !important;
  padding: 0 !important;
  margin-left: 8px !important;
  width: 20px !important;
  height: 20px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 50% !important;
  transition: background 0.2s !important;
}

#ai-assistant-explore-indicator .explore-exit:hover {
  background: rgba(255,255,255,0.2) !important;
}

/* Explore Toolbar */
#ai-assistant-explore-toolbar {
  position: fixed !important;
  background: white !important;
  border: 1px solid #ddd !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
  z-index: 10002 !important;
  display: flex !important;
  gap: 8px !important;
  padding: 8px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

#ai-assistant-explore-toolbar .explore-btn {
  padding: 6px 12px !important;
  border: none !important;
  border-radius: 4px !important;
  background: #f5f5f5 !important;
  cursor: pointer !important;
  font-size: 12px !important;
  transition: background 0.2s !important;
  color: #333 !important;
  font-family: inherit !important;
  outline: none !important;
}

#ai-assistant-explore-toolbar .explore-btn:hover {
  background: #e0e0e0 !important;
}

#ai-assistant-explore-toolbar .explore-btn:active {
  background: #d0d0d0 !important;
}

/* Chat Modal */
#ai-assistant-chat-modal {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: 10003 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

#ai-assistant-chat-modal .modal-overlay {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background: rgba(0,0,0,0.5) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

#ai-assistant-chat-modal .modal-content {
  background: white !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0,0,0,0.3) !important;
  width: 90% !important;
  max-width: 600px !important;
  max-height: 80% !important;
  overflow: hidden !important;
  position: relative !important;
}

#ai-assistant-chat-modal .modal-header {
  padding: 16px 20px !important;
  border-bottom: 1px solid #eee !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  background: #f8f9fa !important;
}

#ai-assistant-chat-modal .modal-header h3 {
  margin: 0 !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #333 !important;
}

#ai-assistant-chat-modal .modal-close {
  background: none !important;
  border: none !important;
  font-size: 24px !important;
  cursor: pointer !important;
  color: #666 !important;
  padding: 0 !important;
  width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 50% !important;
  transition: background 0.2s !important;
}

#ai-assistant-chat-modal .modal-close:hover {
  background: #f0f0f0 !important;
}

#ai-assistant-chat-modal .modal-body {
  padding: 20px !important;
  max-height: 400px !important;
  overflow-y: auto !important;
}

/* Text Selection Highlight */
.ai-assistant-selected-text {
  background: rgba(66, 133, 244, 0.2) !important;
  border-radius: 2px !important;
  position: relative !important;
}

.ai-assistant-selected-text.with-underline {
  border-bottom: 2px solid #4285f4 !important;
}

/* Animation for explore mode */
@keyframes ai-assistant-pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.ai-assistant-explore-mode {
  cursor: crosshair !important;
}

.ai-assistant-explore-mode * {
  cursor: crosshair !important;
}

/* Tooltip styles */
.ai-assistant-tooltip {
  position: absolute !important;
  background: rgba(0,0,0,0.8) !important;
  color: white !important;
  padding: 8px 12px !important;
  border-radius: 6px !important;
  font-size: 12px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  z-index: 10004 !important;
  pointer-events: none !important;
  white-space: nowrap !important;
}

.ai-assistant-tooltip::after {
  content: '' !important;
  position: absolute !important;
  top: 100% !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  border: 5px solid transparent !important;
  border-top-color: rgba(0,0,0,0.8) !important;
}

/* Loading indicator */
.ai-assistant-loading {
  display: inline-flex !important;
  align-items: center !important;
  gap: 8px !important;
  font-size: 12px !important;
  color: #666 !important;
}

.ai-assistant-loading-dots {
  display: flex !important;
  gap: 2px !important;
}

.ai-assistant-loading-dots .dot {
  width: 4px !important;
  height: 4px !important;
  border-radius: 50% !important;
  background: #666 !important;
  animation: ai-assistant-loading-bounce 1.4s ease-in-out infinite both !important;
}

.ai-assistant-loading-dots .dot:nth-child(1) { animation-delay: -0.32s !important; }
.ai-assistant-loading-dots .dot:nth-child(2) { animation-delay: -0.16s !important; }
.ai-assistant-loading-dots .dot:nth-child(3) { animation-delay: 0s !important; }

@keyframes ai-assistant-loading-bounce {
  0%, 80%, 100% {
    transform: scale(0) !important;
  }
  40% {
    transform: scale(1) !important;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  #ai-assistant-float-button {
    width: 48px !important;
    height: 48px !important;
    right: 16px !important;
  }
  
  #ai-assistant-explore-toolbar {
    flex-direction: column !important;
    gap: 4px !important;
    padding: 6px !important;
  }
  
  #ai-assistant-explore-toolbar .explore-btn {
    padding: 8px 12px !important;
    font-size: 14px !important;
  }
  
  #ai-assistant-chat-modal .modal-content {
    width: 95% !important;
    margin: 10px !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  #ai-assistant-explore-toolbar {
    background: #2d2d2d !important;
    border-color: #444 !important;
  }
  
  #ai-assistant-explore-toolbar .explore-btn {
    background: #404040 !important;
    color: #fff !important;
  }
  
  #ai-assistant-explore-toolbar .explore-btn:hover {
    background: #505050 !important;
  }
  
  #ai-assistant-chat-modal .modal-content {
    background: #1e1e1e !important;
    color: #fff !important;
  }
  
  #ai-assistant-chat-modal .modal-header {
    background: #2d2d2d !important;
    border-bottom-color: #444 !important;
  }
  
  #ai-assistant-chat-modal .modal-header h3 {
    color: #fff !important;
  }
  
  #ai-assistant-chat-modal .modal-close {
    color: #ccc !important;
  }
  
  #ai-assistant-chat-modal .modal-close:hover {
    background: #404040 !important;
  }
}
