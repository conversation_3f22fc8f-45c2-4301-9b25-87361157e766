/* Sidepanel styles for AI Assistant Chrome Extension */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  background: #fff;
  height: 100vh;
  overflow: hidden;
}

#sidepanel-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

/* Header */
.sidepanel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e1e5e9;
  background: #f8f9fa;
  flex-shrink: 0;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  width: 24px;
  height: 24px;
}

.header-text {
  font-weight: 600;
  font-size: 16px;
  color: #1a73e8;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.icon-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #5f6368;
  transition: background-color 0.2s;
}

.icon-btn:hover {
  background: #f1f3f4;
}

/* API Setup */
.setup-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.setup-card {
  text-align: center;
  max-width: 300px;
  width: 100%;
}

.setup-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.setup-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1a73e8;
  margin-bottom: 12px;
}

.setup-card p {
  color: #5f6368;
  margin-bottom: 20px;
  line-height: 1.6;
}

.input-group {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.api-input {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #dadce0;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.api-input:focus {
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}

.help-link {
  color: #1a73e8;
  text-decoration: none;
  font-size: 13px;
}

.help-link:hover {
  text-decoration: underline;
}

/* Buttons */
.btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.btn-primary {
  background: #1a73e8;
  color: white;
}

.btn-primary:hover {
  background: #1557b0;
}

.btn-secondary {
  background: #f8f9fa;
  color: #3c4043;
  border: 1px solid #dadce0;
}

.btn-secondary:hover {
  background: #f1f3f4;
}

/* Chat Container */
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* Chat Messages */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  scroll-behavior: smooth;
}

.message-group {
  margin-bottom: 20px;
}

.message {
  max-width: 85%;
  margin-bottom: 8px;
  word-wrap: break-word;
}

.user-message {
  margin-left: auto;
}

.assistant-message {
  margin-right: auto;
}

.message-content {
  padding: 12px 16px;
  border-radius: 18px;
  line-height: 1.4;
}

.user-message .message-content {
  background: #1a73e8;
  color: white;
  border-bottom-right-radius: 6px;
}

.assistant-message .message-content {
  background: #f1f3f4;
  color: #202124;
  border-bottom-left-radius: 6px;
}

.welcome-message .message-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.welcome-message ul {
  margin: 12px 0;
  padding-left: 20px;
}

.welcome-message li {
  margin-bottom: 4px;
}

/* Quick Actions */
.quick-actions {
  padding: 16px 20px;
  border-top: 1px solid #e8eaed;
  border-bottom: 1px solid #e8eaed;
  background: #f8f9fa;
  flex-shrink: 0;
}

.quick-action-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  margin: 0 8px 8px 0;
  border: 1px solid #dadce0;
  border-radius: 16px;
  background: white;
  color: #5f6368;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.quick-action-btn:hover {
  border-color: #1a73e8;
  color: #1a73e8;
}

.action-icon {
  font-size: 14px;
}

/* Input Area */
.input-area {
  padding: 16px 20px;
  border-top: 1px solid #e8eaed;
  background: white;
  flex-shrink: 0;
}

.input-container {
  position: relative;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid #dadce0;
  border-radius: 20px;
  background: white;
  transition: border-color 0.2s;
}

.input-wrapper:focus-within {
  border-color: #1a73e8;
}

#message-input {
  flex: 1;
  border: none;
  outline: none;
  resize: none;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.4;
  max-height: 120px;
  min-height: 20px;
  background: transparent;
}

#message-input::placeholder {
  color: #9aa0a6;
}

.input-actions {
  display: flex;
  gap: 4px;
  align-items: center;
}

.send-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: #1a73e8;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.send-btn:hover:not(:disabled) {
  background: #1557b0;
}

.send-btn:disabled {
  background: #dadce0;
  cursor: not-allowed;
}

.input-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 11px;
  color: #9aa0a6;
}

.char-count {
  font-family: monospace;
}

.shortcuts-hint {
  font-style: italic;
}

/* Loading Indicator */
.loading-indicator {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  background: #f8f9fa;
  border-top: 1px solid #e8eaed;
}

.loading-dots {
  display: flex;
  gap: 4px;
}

.loading-dots .dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #1a73e8;
  animation: loading-bounce 1.4s ease-in-out infinite both;
}

.loading-dots .dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dots .dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dots .dot:nth-child(3) { animation-delay: 0s; }

.loading-text {
  color: #5f6368;
  font-size: 13px;
}

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Settings Panel */
.settings-panel {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: white;
  z-index: 100;
  display: flex;
  flex-direction: column;
}

.settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e1e5e9;
  background: #f8f9fa;
}

.settings-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #202124;
}

.settings-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.setting-group {
  margin-bottom: 20px;
}

.setting-group label {
  display: block;
  font-weight: 500;
  margin-bottom: 8px;
  color: #202124;
}

.setting-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #dadce0;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  outline: none;
  transition: border-color 0.2s;
}

.setting-select:focus {
  border-color: #1a73e8;
}

.checkbox-label {
  display: flex !important;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-weight: normal !important;
  margin-bottom: 0 !important;
}

.checkbox-label input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #1a73e8;
}

.setting-actions {
  display: flex;
  gap: 8px;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e8eaed;
}

.setting-actions .btn {
  flex: 1;
}

/* Scrollbar styling */
.chat-messages::-webkit-scrollbar,
.settings-content::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track,
.settings-content::-webkit-scrollbar-track {
  background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb,
.settings-content::-webkit-scrollbar-thumb {
  background: #dadce0;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover,
.settings-content::-webkit-scrollbar-thumb:hover {
  background: #bdc1c6;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  body {
    background: #202124;
    color: #e8eaed;
  }

  .sidepanel-header,
  .quick-actions,
  .settings-header {
    background: #2d2e30;
    border-bottom-color: #3c4043;
  }

  .header-text,
  .settings-header h3 {
    color: #8ab4f8;
  }

  .icon-btn {
    color: #9aa0a6;
  }

  .icon-btn:hover {
    background: #3c4043;
  }

  .setup-card h3 {
    color: #8ab4f8;
  }

  .setup-card p {
    color: #9aa0a6;
  }

  .api-input,
  .setting-select {
    background: #2d2e30;
    border-color: #3c4043;
    color: #e8eaed;
  }

  .api-input:focus,
  .setting-select:focus {
    border-color: #8ab4f8;
  }

  .chat-messages {
    background: #202124;
  }

  .assistant-message .message-content {
    background: #2d2e30;
    color: #e8eaed;
  }

  .quick-action-btn {
    background: #2d2e30;
    border-color: #3c4043;
    color: #9aa0a6;
  }

  .quick-action-btn:hover {
    border-color: #8ab4f8;
    color: #8ab4f8;
  }

  .input-area {
    background: #202124;
    border-top-color: #3c4043;
  }

  .input-wrapper {
    background: #2d2e30;
    border-color: #3c4043;
  }

  .input-wrapper:focus-within {
    border-color: #8ab4f8;
  }

  #message-input {
    color: #e8eaed;
  }

  #message-input::placeholder {
    color: #5f6368;
  }

  .send-btn {
    background: #8ab4f8;
  }

  .send-btn:hover:not(:disabled) {
    background: #669df6;
  }

  .loading-indicator {
    background: #2d2e30;
    border-top-color: #3c4043;
  }

  .loading-dots .dot {
    background: #8ab4f8;
  }

  .loading-text {
    color: #9aa0a6;
  }

  .settings-panel {
    background: #202124;
  }

  .setting-group label {
    color: #e8eaed;
  }

  .setting-actions {
    border-top-color: #3c4043;
  }

  .btn-secondary {
    background: #3c4043;
    color: #e8eaed;
    border-color: #5f6368;
  }

  .btn-secondary:hover {
    background: #48494a;
  }

  .chat-messages::-webkit-scrollbar-thumb,
  .settings-content::-webkit-scrollbar-thumb {
    background: #3c4043;
  }

  .chat-messages::-webkit-scrollbar-thumb:hover,
  .settings-content::-webkit-scrollbar-thumb:hover {
    background: #5f6368;
  }
}
