/* Sidepanel styles for AI Assistant Chrome Extension */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  background: #fff;
  height: 100vh;
  overflow: hidden;
}

#sidepanel-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

/* Header */
.sidepanel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e1e5e9;
  background: #f8f9fa;
  flex-shrink: 0;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  width: 24px;
  height: 24px;
}

.header-text {
  font-weight: 600;
  font-size: 16px;
  color: #1a73e8;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.icon-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #5f6368;
  transition: background-color 0.2s;
}

.icon-btn:hover {
  background: #f1f3f4;
}

/* API Setup */
.setup-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.setup-card {
  text-align: center;
  max-width: 300px;
  width: 100%;
}

.setup-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.setup-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1a73e8;
  margin-bottom: 12px;
}

.setup-card p {
  color: #5f6368;
  margin-bottom: 20px;
  line-height: 1.6;
}

.input-group {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.password-input-wrapper {
  position: relative;
  flex: 1;
}

.password-input-wrapper .api-input {
  padding-right: 40px;
}

.toggle-visibility-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #5f6368;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.toggle-visibility-btn:hover {
  background: #f1f3f4;
  color: #202124;
}

.api-input {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #dadce0;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.api-input:focus {
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}

.help-link {
  color: #1a73e8;
  text-decoration: none;
  font-size: 13px;
}

.help-link:hover {
  text-decoration: underline;
}

/* Buttons */
.btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.btn-primary {
  background: #1a73e8;
  color: white;
}

.btn-primary:hover {
  background: #1557b0;
}

.btn-secondary {
  background: #f8f9fa;
  color: #3c4043;
  border: 1px solid #dadce0;
}

.btn-secondary:hover {
  background: #f1f3f4;
}

/* Chat Container */
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* Chat Header */
.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #e8eaed;
  background: #f8f9fa;
}

.chat-title {
  font-size: 16px;
  font-weight: 500;
  color: #202124;
}

.chat-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

#toggle-markdown {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: 1px solid #dadce0;
  border-radius: 16px;
  background: white;
  color: #5f6368;
  font-size: 12px;
  transition: all 0.2s;
}

#toggle-markdown:hover {
  border-color: #1a73e8;
  color: #1a73e8;
}

#toggle-markdown.active {
  background: #1a73e8;
  border-color: #1a73e8;
  color: white;
}

/* Chat Messages */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  scroll-behavior: smooth;
}

.message-group {
  margin-bottom: 20px;
}

.message {
  max-width: 85%;
  margin-bottom: 8px;
  word-wrap: break-word;
}

.user-message {
  margin-left: auto;
}

.assistant-message {
  margin-right: auto;
}

.message-content {
  padding: 12px 16px;
  border-radius: 18px;
  line-height: 1.4;
}

.user-message .message-content {
  background: #1a73e8;
  color: white;
  border-bottom-right-radius: 6px;
}

.assistant-message .message-content {
  background: #f1f3f4;
  color: #202124;
  border-bottom-left-radius: 6px;
}

.welcome-message .message-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.welcome-message ul {
  margin: 12px 0;
  padding-left: 20px;
}

.welcome-message li {
  margin-bottom: 4px;
}

/* Markdown content styles */
.message-content h1,
.message-content h2,
.message-content h3 {
  margin: 16px 0 8px 0;
  font-weight: 600;
}

.message-content h1 {
  font-size: 18px;
  border-bottom: 2px solid #e8eaed;
  padding-bottom: 4px;
}

.message-content h2 {
  font-size: 16px;
}

.message-content h3 {
  font-size: 14px;
}

.message-content p {
  margin: 8px 0;
  line-height: 1.5;
}

.message-content strong {
  font-weight: 600;
}

.message-content em {
  font-style: italic;
}

.message-content code {
  background: rgba(0,0,0,0.1);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.message-content pre {
  background: #f8f9fa;
  border: 1px solid #e8eaed;
  border-radius: 6px;
  padding: 12px;
  margin: 12px 0;
  overflow-x: auto;
}

.message-content pre code {
  background: none;
  padding: 0;
  font-size: 12px;
  line-height: 1.4;
}

.message-content ul,
.message-content ol {
  margin: 8px 0;
  padding-left: 20px;
}

.message-content li {
  margin-bottom: 4px;
  line-height: 1.4;
}

.message-content a {
  color: #1a73e8;
  text-decoration: none;
}

.message-content a:hover {
  text-decoration: underline;
}

.message-content blockquote {
  border-left: 4px solid #e8eaed;
  margin: 12px 0;
  padding: 8px 16px;
  background: #f8f9fa;
  font-style: italic;
}

/* Explore message styles */
.action-badge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  margin-bottom: 8px;
  font-weight: 500;
}

.selected-text-display {
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 12px;
  border-radius: 8px;
  border-left: 3px solid rgba(255, 255, 255, 0.3);
  font-style: italic;
  margin-top: 4px;
  word-wrap: break-word;
}

/* Quick Actions */
.quick-actions {
  padding: 16px 20px;
  border-top: 1px solid #e8eaed;
  border-bottom: 1px solid #e8eaed;
  background: #f8f9fa;
  flex-shrink: 0;
}

.quick-action-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  margin: 0 8px 8px 0;
  border: 1px solid #dadce0;
  border-radius: 16px;
  background: white;
  color: #5f6368;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.quick-action-btn:hover {
  border-color: #1a73e8;
  color: #1a73e8;
}

.action-icon {
  font-size: 14px;
}

/* Input Area */
.input-area {
  padding: 16px 20px;
  border-top: 1px solid #e8eaed;
  background: white;
  flex-shrink: 0;
}

.input-container {
  position: relative;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid #dadce0;
  border-radius: 20px;
  background: white;
  transition: border-color 0.2s;
}

.input-wrapper:focus-within {
  border-color: #1a73e8;
}

#message-input {
  flex: 1;
  border: none;
  outline: none;
  resize: none;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.4;
  max-height: 120px;
  min-height: 20px;
  background: transparent;
}

#message-input::placeholder {
  color: #9aa0a6;
}

.input-actions {
  display: flex;
  gap: 4px;
  align-items: center;
}

.send-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: #1a73e8;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.send-btn:hover:not(:disabled) {
  background: #1557b0;
}

.send-btn:disabled {
  background: #dadce0;
  cursor: not-allowed;
}

.input-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 11px;
  color: #9aa0a6;
}

.char-count {
  font-family: monospace;
}

.shortcuts-hint {
  font-style: italic;
}

/* Loading Indicator */
.loading-indicator {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  background: #f8f9fa;
  border-top: 1px solid #e8eaed;
}

/* Loading message in chat */
.loading-content {
  display: flex;
  align-items: center;
  gap: 8px;
  opacity: 0.8;
}

.loading-dots {
  display: flex;
  gap: 4px;
}

.loading-dots .dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #1a73e8;
  animation: loading-bounce 1.4s ease-in-out infinite both;
}

.loading-dots .dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dots .dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dots .dot:nth-child(3) { animation-delay: 0s; }

.loading-text {
  color: #5f6368;
  font-size: 13px;
}

.assistant-message .loading-content .loading-dots .dot {
  background: #5f6368;
}

.assistant-message .loading-content .loading-text {
  color: #5f6368;
}

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Settings Panel & History Panel */
.settings-panel {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: white;
  z-index: 100;
  display: flex;
  flex-direction: column;
}

/* History List */
.history-list {
  flex: 1;
  overflow-y: auto;
}

.no-history {
  text-align: center;
  color: #9aa0a6;
  padding: 40px 20px;
  font-style: italic;
}

.history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #e8eaed;
  transition: background-color 0.2s;
}

.history-item:hover {
  background: #f8f9fa;
}

.history-item-content {
  flex: 1;
  min-width: 0;
}

.history-title {
  font-weight: 500;
  color: #202124;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.history-meta {
  font-size: 12px;
  color: #5f6368;
}

.history-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.history-load-btn,
.history-delete-btn {
  padding: 4px 8px;
  border: 1px solid #dadce0;
  border-radius: 4px;
  background: white;
  color: #5f6368;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.history-load-btn:hover {
  border-color: #1a73e8;
  color: #1a73e8;
}

.history-delete-btn:hover {
  border-color: #ea4335;
  color: #ea4335;
}

.settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e1e5e9;
  background: #f8f9fa;
}

.settings-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #202124;
}

.settings-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.setting-group {
  margin-bottom: 20px;
}

.setting-group label {
  display: block;
  font-weight: 500;
  margin-bottom: 8px;
  color: #202124;
}

.setting-select,
.setting-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #dadce0;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  outline: none;
  transition: border-color 0.2s;
}

.setting-select:focus,
.setting-input:focus {
  border-color: #1a73e8;
}

.setting-help {
  display: block;
  margin-top: 4px;
  font-size: 12px;
  color: #5f6368;
}

.setting-help a {
  color: #1a73e8;
  text-decoration: none;
}

.setting-help a:hover {
  text-decoration: underline;
}

.model-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 8px;
}

.model-suggestion {
  padding: 4px 8px;
  border: 1px solid #dadce0;
  border-radius: 12px;
  background: white;
  color: #5f6368;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s;
}

.model-suggestion:hover {
  border-color: #1a73e8;
  color: #1a73e8;
  background: #f8f9fa;
}

/* Explore Actions Config */
.explore-action-item {
  border: 1px solid #e8eaed;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  background: #f8f9fa;
}

.explore-action-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.explore-action-title {
  font-weight: 500;
  color: #202124;
}

.explore-action-remove {
  background: none;
  border: none;
  color: #ea4335;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  font-size: 12px;
  transition: background-color 0.2s;
}

.explore-action-remove:hover {
  background: #fce8e6;
}

.explore-action-inputs {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.explore-action-inputs input,
.explore-action-inputs textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #dadce0;
  border-radius: 6px;
  font-size: 13px;
  outline: none;
  transition: border-color 0.2s;
}

.explore-action-inputs input:focus,
.explore-action-inputs textarea:focus {
  border-color: #1a73e8;
}

.explore-action-inputs textarea {
  resize: vertical;
  min-height: 60px;
  font-family: inherit;
}

/* Explore Actions Summary */
.explore-actions-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e8eaed;
}

#explore-actions-count {
  font-size: 13px;
  color: #5f6368;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e8eaed;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: #202124;
}

.modal-body {
  flex: 1;
  padding: 20px 24px;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px 20px;
  border-top: 1px solid #e8eaed;
}

/* Table Styles */
.table-container {
  overflow-x: auto;
  margin-bottom: 20px;
}

#explore-actions-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

#explore-actions-table th,
#explore-actions-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e8eaed;
}

#explore-actions-table th {
  background: #f8f9fa;
  font-weight: 500;
  color: #202124;
}

#explore-actions-table input,
#explore-actions-table textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #dadce0;
  border-radius: 4px;
  font-size: 13px;
  outline: none;
  transition: border-color 0.2s;
}

#explore-actions-table input:focus,
#explore-actions-table textarea:focus {
  border-color: #1a73e8;
}

#explore-actions-table textarea {
  resize: vertical;
  min-height: 60px;
  font-family: inherit;
}

.action-delete-btn {
  background: none;
  border: none;
  color: #ea4335;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.action-delete-btn:hover {
  background: #fce8e6;
}

.modal-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.checkbox-label {
  display: flex !important;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-weight: normal !important;
  margin-bottom: 0 !important;
}

.checkbox-label input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #1a73e8;
}

.setting-actions {
  display: flex;
  gap: 8px;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e8eaed;
}

.setting-actions .btn {
  flex: 1;
}

/* Scrollbar styling */
.chat-messages::-webkit-scrollbar,
.settings-content::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track,
.settings-content::-webkit-scrollbar-track {
  background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb,
.settings-content::-webkit-scrollbar-thumb {
  background: #dadce0;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover,
.settings-content::-webkit-scrollbar-thumb:hover {
  background: #bdc1c6;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  body {
    background: #202124;
    color: #e8eaed;
  }

  .sidepanel-header,
  .quick-actions,
  .settings-header {
    background: #2d2e30;
    border-bottom-color: #3c4043;
  }

  .header-text,
  .settings-header h3 {
    color: #8ab4f8;
  }

  .icon-btn {
    color: #9aa0a6;
  }

  .icon-btn:hover {
    background: #3c4043;
  }

  .setup-card h3 {
    color: #8ab4f8;
  }

  .setup-card p {
    color: #9aa0a6;
  }

  .api-input,
  .setting-select,
  .setting-input {
    background: #2d2e30;
    border-color: #3c4043;
    color: #e8eaed;
  }

  .api-input:focus,
  .setting-select:focus,
  .setting-input:focus {
    border-color: #8ab4f8;
  }

  .model-suggestion {
    background: #3c4043;
    border-color: #5f6368;
    color: #9aa0a6;
  }

  .model-suggestion:hover {
    border-color: #8ab4f8;
    color: #8ab4f8;
    background: #48494a;
  }

  .toggle-visibility-btn {
    color: #9aa0a6;
  }

  .toggle-visibility-btn:hover {
    background: #3c4043;
    color: #e8eaed;
  }

  .explore-action-item {
    background: #2d2e30;
    border-color: #3c4043;
  }

  .explore-action-title {
    color: #e8eaed;
  }

  .explore-action-inputs input,
  .explore-action-inputs textarea {
    background: #2d2e30;
    border-color: #3c4043;
    color: #e8eaed;
  }

  .explore-action-inputs input:focus,
  .explore-action-inputs textarea:focus {
    border-color: #8ab4f8;
  }

  .explore-actions-summary {
    background: #2d2e30;
    border-color: #3c4043;
  }

  #explore-actions-count {
    color: #9aa0a6;
  }

  .modal-content {
    background: #2d2e30;
  }

  .modal-header {
    border-bottom-color: #3c4043;
  }

  .modal-header h3 {
    color: #e8eaed;
  }

  .modal-footer {
    border-top-color: #3c4043;
  }

  #explore-actions-table th {
    background: #3c4043;
    color: #e8eaed;
  }

  #explore-actions-table th,
  #explore-actions-table td {
    border-bottom-color: #3c4043;
  }

  #explore-actions-table input,
  #explore-actions-table textarea {
    background: #2d2e30;
    border-color: #3c4043;
    color: #e8eaed;
  }

  #explore-actions-table input:focus,
  #explore-actions-table textarea:focus {
    border-color: #8ab4f8;
  }

  .chat-header {
    background: #2d2e30;
    border-bottom-color: #3c4043;
  }

  .chat-title {
    color: #e8eaed;
  }

  #toggle-markdown {
    background: #3c4043;
    border-color: #5f6368;
    color: #9aa0a6;
  }

  #toggle-markdown:hover {
    border-color: #8ab4f8;
    color: #8ab4f8;
  }

  #toggle-markdown.active {
    background: #8ab4f8;
    border-color: #8ab4f8;
    color: #202124;
  }

  .chat-messages {
    background: #202124;
  }

  .assistant-message .message-content {
    background: #2d2e30;
    color: #e8eaed;
  }

  .quick-action-btn {
    background: #2d2e30;
    border-color: #3c4043;
    color: #9aa0a6;
  }

  .quick-action-btn:hover {
    border-color: #8ab4f8;
    color: #8ab4f8;
  }

  .input-area {
    background: #202124;
    border-top-color: #3c4043;
  }

  .input-wrapper {
    background: #2d2e30;
    border-color: #3c4043;
  }

  .input-wrapper:focus-within {
    border-color: #8ab4f8;
  }

  #message-input {
    color: #e8eaed;
  }

  #message-input::placeholder {
    color: #5f6368;
  }

  .send-btn {
    background: #8ab4f8;
  }

  .send-btn:hover:not(:disabled) {
    background: #669df6;
  }

  .loading-indicator {
    background: #2d2e30;
    border-top-color: #3c4043;
  }

  .loading-dots .dot {
    background: #8ab4f8;
  }

  .loading-text {
    color: #9aa0a6;
  }

  .settings-panel {
    background: #202124;
  }

  .setting-group label {
    color: #e8eaed;
  }

  .setting-actions {
    border-top-color: #3c4043;
  }

  .btn-secondary {
    background: #3c4043;
    color: #e8eaed;
    border-color: #5f6368;
  }

  .btn-secondary:hover {
    background: #48494a;
  }

  .chat-messages::-webkit-scrollbar-thumb,
  .settings-content::-webkit-scrollbar-thumb {
    background: #3c4043;
  }

  .chat-messages::-webkit-scrollbar-thumb:hover,
  .settings-content::-webkit-scrollbar-thumb:hover {
    background: #5f6368;
  }

  /* Dark mode markdown styles */
  .message-content h1 {
    border-bottom-color: #3c4043;
  }

  .message-content code {
    background: rgba(255,255,255,0.1);
  }

  .message-content pre {
    background: #2d2e30;
    border-color: #3c4043;
  }

  .message-content blockquote {
    border-left-color: #3c4043;
    background: #2d2e30;
  }

  .history-item {
    border-bottom-color: #3c4043;
  }

  .history-item:hover {
    background: #3c4043;
  }

  .history-title {
    color: #e8eaed;
  }

  .history-load-btn,
  .history-delete-btn {
    background: #3c4043;
    border-color: #5f6368;
    color: #9aa0a6;
  }

  .history-load-btn:hover {
    border-color: #8ab4f8;
    color: #8ab4f8;
  }

  .history-delete-btn:hover {
    border-color: #f28b82;
    color: #f28b82;
  }
}
