<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Assistant 插件测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #1a73e8;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #1a73e8;
            padding-bottom: 5px;
        }
        
        .test-text {
            background: #fff;
            padding: 15px;
            border-left: 4px solid #1a73e8;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .highlight {
            background: yellow;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .code-block {
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        
        .instructions {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .instructions h3 {
            color: #1976d2;
            margin-top: 0;
        }
        
        .test-list {
            list-style-type: none;
            padding: 0;
        }
        
        .test-list li {
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #4caf50;
        }
        
        .test-list li:before {
            content: "✓ ";
            color: #4caf50;
            font-weight: bold;
        }
        
        .image-placeholder {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            border: 2px dashed #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 18px;
            margin: 20px 0;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI Assistant 插件测试页面</h1>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <p>这个页面用于测试 AI Assistant Chrome 插件的各项功能。请按照以下步骤进行测试：</p>
            <ol>
                <li>确保已安装并配置好插件</li>
                <li>点击插件图标打开侧边栏</li>
                <li>尝试选择下面的文本内容</li>
                <li>使用快捷键 Ctrl+K 或 Ctrl+J</li>
                <li>测试截图和页面总结功能</li>
            </ol>
        </div>

        <div class="section">
            <h2>📝 文本选择测试</h2>
            <div class="test-text">
                <p>这是一段用于测试文本选择功能的内容。请尝试选择这段文字，然后使用插件的探索功能来解释、翻译或总结这段内容。</p>
                <p>人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。</p>
            </div>
            
            <div class="test-text">
                <p><span class="highlight">Machine Learning</span> is a subset of artificial intelligence that provides systems the ability to automatically learn and improve from experience without being explicitly programmed.</p>
                <p>Deep learning is part of a broader family of machine learning methods based on artificial neural networks with representation learning.</p>
            </div>
        </div>

        <div class="section">
            <h2>🔍 探索模式测试</h2>
            <p>使用快捷键 <code>Ctrl+J</code> 启动探索模式，然后：</p>
            <ul class="test-list">
                <li>选择任意文本查看工具栏</li>
                <li>点击页面任意位置进行截图</li>
                <li>测试解释、翻译、总结功能</li>
            </ul>
            <p><strong>最新功能更新：</strong></p>
            <ul>
                <li>🎯 <strong>每条消息独立切换</strong>：每个AI回答右上角有切换图标，可单独切换显示模式</li>
                <li>⚡ <strong>真正的流式输出</strong>：AI回答时实时显示内容，不等待完整回答</li>
                <li>🔄 <strong>智能完成检测</strong>：自动识别AI回答完成标志 [DONE]</li>
                <li>💫 <strong>流畅的用户体验</strong>：打字指示器、流式光标、悬停显示切换按钮</li>
                <li>📝 Markdown渲染优化：修复下划线斜体问题</li>
            </ul>
            <p><strong>新功能测试：</strong></p>
            <ul>
                <li><strong>流式输出</strong>：发送消息后观察AI回答逐字显示</li>
                <li><strong>独立切换</strong>：悬停在AI回答上，点击右上角图标切换显示模式</li>
                <li><strong>完成检测</strong>：观察AI回答完成时光标消失</li>
                <li>测试长回答是否能完整显示（不会被截断）</li>
            </ul>
        </div>

        <div class="section">
            <h2>💬 对话功能测试</h2>
            <div class="test-text">
                <p>点击插件图标打开侧边栏，或使用快捷键 <code>Ctrl+K</code>，尝试以下问题：</p>
                <ul>
                    <li>"请解释什么是人工智能"</li>
                    <li>"总结这个页面的内容"</li>
                    <li>"翻译一段中文到英文"</li>
                    <li>"帮我分析一下机器学习的应用"</li>
                </ul>
                <p><strong>新功能：</strong>现在支持 Markdown 格式显示，AI 回复会有更好的排版效果！</p>
            </div>
        </div>

        <div class="section">
            <h2>📸 截图分析测试</h2>
            <div class="image-placeholder">
                📊 图表示例区域 - 用于截图测试
            </div>
            <p>在侧边栏中点击"截图分析"按钮，或在探索模式下点击上方区域进行截图分析测试。</p>
        </div>

        <div class="section">
            <h2>🌐 多语言内容测试</h2>
            <div class="test-text">
                <p><strong>中文：</strong>这是一段中文内容，用于测试插件的多语言处理能力。</p>
                <p><strong>English:</strong> This is an English paragraph for testing the plugin's multilingual capabilities.</p>
                <p><strong>日本語：</strong>これは、プラグインの多言語機能をテストするための日本語の段落です。</p>
                <p><strong>Français:</strong> Ceci est un paragraphe français pour tester les capacités multilingues du plugin.</p>
            </div>
        </div>

        <div class="section">
            <h2>💻 代码内容测试</h2>
            <div class="code-block">
function fibonacci(n) {
    if (n <= 1) return n;
    return fibonacci(n - 1) + fibonacci(n - 2);
}

// 计算斐波那契数列的第10项
console.log(fibonacci(10)); // 输出: 55
            </div>
            <p>选择上面的代码，测试插件对代码内容的解释能力。</p>
        </div>

        <div class="section">
            <h2>📊 数据表格测试</h2>
            <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                <thead>
                    <tr style="background: #f5f5f5;">
                        <th style="border: 1px solid #ddd; padding: 12px;">产品</th>
                        <th style="border: 1px solid #ddd; padding: 12px;">价格</th>
                        <th style="border: 1px solid #ddd; padding: 12px;">销量</th>
                        <th style="border: 1px solid #ddd; padding: 12px;">评分</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 12px;">iPhone 15</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">¥5999</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">10000</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">4.8</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 12px;">Samsung Galaxy S24</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">¥4999</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">8000</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">4.6</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 12px;">Google Pixel 8</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">¥3999</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">5000</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">4.5</td>
                    </tr>
                </tbody>
            </table>
            <p>选择表格内容，测试插件对结构化数据的分析能力。</p>
        </div>

        <div class="section">
            <h2>📚 历史记录测试</h2>
            <div class="test-text">
                <p>测试历史记录功能：</p>
                <ul>
                    <li>进行几次对话后，点击侧边栏的历史记录按钮</li>
                    <li>查看历史对话列表</li>
                    <li>点击"加载"按钮重新加载历史对话</li>
                    <li>测试导出和删除功能</li>
                </ul>
                <p><strong>新功能：</strong>现在会自动保存对话历史，支持导出和管理！</p>
            </div>
        </div>

        <div class="section">
            <h2>✅ 功能检查清单</h2>
            <ul class="test-list">
                <li>插件图标显示正常</li>
                <li>点击图标打开侧边栏</li>
                <li>API Key 配置和修改功能</li>
                <li>自定义模型名称输入</li>
                <li>快捷键 Ctrl+K 打开对话</li>
                <li>快捷键 Ctrl+J 启动探索模式</li>
                <li>文本选择显示工具栏</li>
                <li>截图功能显示图片预览</li>
                <li>页面总结功能可用</li>
                <li>Markdown 格式正确显示</li>
                <li>AI思考状态显示在对话中</li>
                <li>历史记录保存和加载</li>
                <li>探索模式指示器自动消失</li>
            </ul>
        </div>

        <div class="instructions">
            <h3>🔧 故障排除</h3>
            <p>如果遇到问题，请检查：</p>
            <ul>
                <li><strong>API Key 格式</strong>：必须以 sk-or- 开头，只包含英文字母、数字和符号</li>
                <li><strong>字符编码</strong>：API Key 不能包含中文或特殊字符</li>
                <li>网络连接是否正常</li>
                <li>浏览器控制台是否有错误信息</li>
                <li>插件是否已正确加载</li>
            </ul>
            <p><strong>常见错误：</strong></p>
            <ul>
                <li>"Failed to read the 'headers' property" → API Key 包含非ASCII字符</li>
                <li>"Invalid API key format" → API Key 格式不正确</li>
            </ul>
        </div>
    </div>
</body>
</html>
