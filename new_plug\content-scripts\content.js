// Content script for AI Assistant Chrome Extension

let isExploreMode = false;
let selectedText = '';
let floatButton = null;
let chatModal = null;

// Initialize content script
(function() {
  console.log('AI Assistant content script loaded');
  
  // Setup functionality
  setupTextSelection();
  setupMessageListener();
})();

// Load settings from storage
async function loadSettings() {
  try {
    const result = await chrome.storage.sync.get(['ai_assistant_settings']);
    return result.ai_assistant_settings || {
      enableFloatButton: true,
      enableUnderline: true,
      windowMode: 'sidepanel'
    };
  } catch (error) {
    console.error('Error loading settings:', error);
    return {
      enableFloatButton: true,
      enableUnderline: true,
      windowMode: 'sidepanel'
    };
  }
}

// Setup message listener for communication with background script
function setupMessageListener() {
  chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
    console.log('Content script received message:', message);
    
    switch (message.action) {
      case 'openChatModal':
        // Redirect to sidepanel instead
        chrome.runtime.sendMessage({ action: 'openSidePanel' });
        break;

      case 'startExploreMode':
        startExploreMode();
        break;
        
      case 'analyzeScreenshot':
        handleScreenshotAnalysis(message.data);
        break;
        
      case 'summarizePage':
        handlePageSummary(message.data);
        break;
        
      case 'getPageContent':
        sendResponse(getPageContent());
        break;
        
      case 'analyzeSelectedText':
        if (selectedText) {
          analyzeSelectedText(selectedText);
        }
        break;
    }
  });
}

// Create floating button
function createFloatButton() {
  if (floatButton) return;
  
  floatButton = document.createElement('div');
  floatButton.id = 'ai-assistant-float-button';
  floatButton.innerHTML = `
    <div class="float-button-icon">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
      </svg>
    </div>
  `;
  
  floatButton.style.cssText = `
    position: fixed;
    top: 50%;
    right: 20px;
    width: 56px;
    height: 56px;
    background: #4285f4;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    cursor: pointer;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    transform: translateY(-50%);
  `;
  
  floatButton.addEventListener('click', () => {
    chrome.runtime.sendMessage({ action: 'openSidePanel' });
  });
  
  floatButton.addEventListener('mouseenter', () => {
    floatButton.style.transform = 'translateY(-50%) scale(1.1)';
    floatButton.style.boxShadow = '0 6px 16px rgba(0,0,0,0.2)';
  });
  
  floatButton.addEventListener('mouseleave', () => {
    floatButton.style.transform = 'translateY(-50%) scale(1)';
    floatButton.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
  });
  
  document.body.appendChild(floatButton);
}

// Setup text selection handling
function setupTextSelection() {
  let selectionTimeout;
  
  document.addEventListener('mouseup', () => {
    clearTimeout(selectionTimeout);
    selectionTimeout = setTimeout(() => {
      handleTextSelection();
    }, 100);
  });
  
  document.addEventListener('keyup', (e) => {
    if (e.key === 'ArrowLeft' || e.key === 'ArrowRight' || 
        e.key === 'ArrowUp' || e.key === 'ArrowDown') {
      clearTimeout(selectionTimeout);
      selectionTimeout = setTimeout(() => {
        handleTextSelection();
      }, 100);
    }
  });
}

// Handle text selection
function handleTextSelection() {
  const selection = window.getSelection();
  const text = selection.toString().trim();

  if (text && text.length > 0) {
    selectedText = text;

    // Always show toolbar when text is selected, not just in explore mode
    showExploreToolbar(selection);
  } else {
    selectedText = '';
    hideExploreToolbar();
  }
}

// Start explore mode
function startExploreMode() {
  isExploreMode = true;
  document.body.style.cursor = 'crosshair';
  
  // Show explore mode indicator
  showExploreIndicator();
  
  // Add click handler for screenshots
  document.addEventListener('click', handleExploreClick, true);
  
  // Auto-exit after 30 seconds
  setTimeout(() => {
    if (isExploreMode) {
      exitExploreMode();
    }
  }, 30000);
}

// Exit explore mode
function exitExploreMode() {
  isExploreMode = false;
  document.body.style.cursor = '';
  hideExploreIndicator();
  hideExploreToolbar();
  document.removeEventListener('click', handleExploreClick, true);
}

// Handle explore mode clicks
function handleExploreClick(e) {
  if (!isExploreMode) return;
  
  e.preventDefault();
  e.stopPropagation();
  
  // Take screenshot of clicked area
  chrome.runtime.sendMessage({ action: 'takeScreenshot' });
  exitExploreMode();
}

// Show explore mode indicator
function showExploreIndicator() {
  const indicator = document.createElement('div');
  indicator.id = 'ai-assistant-explore-indicator';
  indicator.innerHTML = `
    <div class="explore-indicator-content">
      <span class="explore-icon">🔍</span>
      <span class="explore-text">探索模式已启动</span>
    </div>
  `;

  indicator.style.cssText = `
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
    color: white;
    padding: 12px 20px;
    border-radius: 24px;
    z-index: 10001;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 16px rgba(66, 133, 244, 0.4);
    transition: opacity 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.2);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  `;

  document.body.appendChild(indicator);

  // Hide on click anywhere
  const hideIndicator = () => {
    if (indicator.parentElement) {
      indicator.style.opacity = '0';
      setTimeout(() => {
        if (indicator.parentElement) {
          indicator.remove();
        }
      }, 300);
      document.removeEventListener('click', hideIndicator);
    }
  };

  // Add click listener to hide indicator
  setTimeout(() => {
    document.addEventListener('click', hideIndicator);
  }, 100); // Small delay to prevent immediate hiding
}

// Hide explore mode indicator
function hideExploreIndicator() {
  const indicator = document.getElementById('ai-assistant-explore-indicator');
  if (indicator) {
    indicator.remove();
  }
}

// Show explore toolbar for selected text
function showExploreToolbar(selection) {
  hideExploreToolbar();

  if (selection.rangeCount === 0) return;

  const range = selection.getRangeAt(0);
  const rect = range.getBoundingClientRect();

  // Don't show toolbar if selection is too small or empty
  if (rect.width < 10 || rect.height < 10) return;

  const toolbar = document.createElement('div');
  toolbar.id = 'ai-assistant-explore-toolbar';
  toolbar.innerHTML = `
    <button class="explore-btn" data-action="explain">解释</button>
    <button class="explore-btn" data-action="translate">翻译</button>
    <button class="explore-btn" data-action="summarize">总结</button>
  `;

  // Calculate position - show above or below selection
  const viewportHeight = window.innerHeight;
  const spaceAbove = rect.top;
  const spaceBelow = viewportHeight - rect.bottom;

  let toolbarTop;
  if (spaceAbove > 60) {
    // Show above selection
    toolbarTop = rect.top - 60;
  } else if (spaceBelow > 60) {
    // Show below selection
    toolbarTop = rect.bottom + 10;
  } else {
    // Show at top of viewport
    toolbarTop = 10;
  }

  const toolbarLeft = Math.min(Math.max(150, rect.left + rect.width / 2), window.innerWidth - 150);

  toolbar.style.cssText = `
    position: fixed;
    top: ${toolbarTop}px;
    left: ${toolbarLeft}px;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid #4285f4;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(66, 133, 244, 0.3);
    z-index: 10002;
    display: flex;
    gap: 8px;
    padding: 12px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    backdrop-filter: blur(10px);
  `;
  
  // Add event listeners to buttons
  toolbar.querySelectorAll('.explore-btn').forEach(btn => {
    btn.style.cssText = `
      padding: 6px 12px;
      border: none;
      border-radius: 4px;
      background: #f5f5f5;
      cursor: pointer;
      font-size: 12px;
      transition: background 0.2s;
    `;
    
    btn.addEventListener('click', (e) => {
      const action = e.target.dataset.action;
      handleExploreAction(action, selectedText);
      hideExploreToolbar();
    });
    
    btn.addEventListener('mouseenter', () => {
      btn.style.background = '#e0e0e0';
    });
    
    btn.addEventListener('mouseleave', () => {
      btn.style.background = '#f5f5f5';
    });
  });
  
  document.body.appendChild(toolbar);
  
  // Auto-hide after 10 seconds
  setTimeout(() => {
    hideExploreToolbar();
  }, 10000);
}

// Hide explore toolbar
function hideExploreToolbar() {
  const toolbar = document.getElementById('ai-assistant-explore-toolbar');
  if (toolbar) {
    toolbar.remove();
  }
}

// Handle explore actions
function handleExploreAction(action, text) {
  let actionText = '';
  let prompt = '';

  switch (action) {
    case 'explain':
      actionText = '解释';
      prompt = `请解释以下文本的含义：\n\n"${text}"`;
      break;
    case 'translate':
      actionText = '翻译';
      prompt = `请将以下文本翻译成中文：\n\n"${text}"`;
      break;
    case 'summarize':
      actionText = '总结';
      prompt = `请总结以下文本的要点：\n\n"${text}"`;
      break;
  }

  if (prompt) {
    // Open sidepanel first
    chrome.runtime.sendMessage({ action: 'openSidePanel' });

    // Send message with user action context
    setTimeout(() => {
      chrome.runtime.sendMessage({
        action: 'addExploreMessage',
        data: {
          action: actionText,
          selectedText: text,
          prompt: prompt
        }
      });
    }, 300);
  }
}

// Analyze selected text
function analyzeSelectedText(text) {
  chrome.runtime.sendMessage({
    action: 'analyzeSelectedText',
    data: {
      selectedText: text,
      pageContext: getPageContext()
    }
  });
}

// Handle screenshot analysis
function handleScreenshotAnalysis(data) {
  // This would typically open the result in the sidepanel or modal
  console.log('Screenshot analysis result:', data);
}

// Handle page summary
function handlePageSummary(_data) {
  const content = getPageContent();
  
  chrome.runtime.sendMessage({
    action: 'sendChatMessage',
    data: {
      message: `请总结以下网页内容：\n\n${content}`,
      context: []
    }
  });
}

// Get page content for analysis
function getPageContent() {
  // Extract main content from the page
  const content = [];
  
  // Try to get main content area
  const mainSelectors = ['main', 'article', '.content', '#content', '.post', '.entry'];
  let mainContent = null;
  
  for (const selector of mainSelectors) {
    mainContent = document.querySelector(selector);
    if (mainContent) break;
  }
  
  if (!mainContent) {
    mainContent = document.body;
  }
  
  // Extract text from headings and paragraphs
  const elements = mainContent.querySelectorAll('h1, h2, h3, h4, h5, h6, p, li');
  
  elements.forEach(el => {
    const text = el.textContent.trim();
    if (text && text.length > 10) {
      content.push(text);
    }
  });
  
  return content.slice(0, 50).join('\n\n'); // Limit content length
}

// Get page context for better analysis
function getPageContext() {
  const title = document.title;
  const url = window.location.href;
  const description = document.querySelector('meta[name="description"]')?.content || '';
  
  return `页面标题: ${title}\n页面URL: ${url}\n页面描述: ${description}`;
}

// Open chat modal (for modal mode)
function openChatModal() {
  if (chatModal) {
    chatModal.style.display = 'flex';
    return;
  }
  
  // Create modal (simplified version)
  chatModal = document.createElement('div');
  chatModal.id = 'ai-assistant-chat-modal';
  chatModal.innerHTML = `
    <div class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h3>AI Assistant</h3>
          <button class="modal-close">×</button>
        </div>
        <div class="modal-body">
          <p>Chat modal would be implemented here...</p>
        </div>
      </div>
    </div>
  `;
  
  chatModal.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10003;
    display: flex;
    align-items: center;
    justify-content: center;
  `;
  
  // Add close functionality
  chatModal.querySelector('.modal-close').addEventListener('click', () => {
    chatModal.style.display = 'none';
  });
  
  chatModal.querySelector('.modal-overlay').addEventListener('click', (e) => {
    if (e.target === e.currentTarget) {
      chatModal.style.display = 'none';
    }
  });
  
  document.body.appendChild(chatModal);
}

// Expose functions for global access
window.exitExploreMode = exitExploreMode;
