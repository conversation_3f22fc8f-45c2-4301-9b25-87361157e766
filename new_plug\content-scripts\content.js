// Content script for AI Assistant Chrome Extension

let isExploreMode = false;
let selectedText = '';
let floatButton = null;
let chatModal = null;

// Initialize content script
(function() {
  console.log('AI Assistant content script loaded');
  
  // Setup functionality
  setupTextSelection();
  setupMessageListener();
})();

// Load settings from storage
async function loadSettings() {
  try {
    const result = await chrome.storage.sync.get(['ai_assistant_settings']);
    return result.ai_assistant_settings || {
      enableFloatButton: true,
      enableUnderline: true,
      windowMode: 'sidepanel'
    };
  } catch (error) {
    console.error('Error loading settings:', error);
    return {
      enableFloatButton: true,
      enableUnderline: true,
      windowMode: 'sidepanel'
    };
  }
}

// Setup message listener for communication with background script
function setupMessageListener() {
  chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
    console.log('Content script received message:', message);
    
    switch (message.action) {
      case 'openChatModal':
        // Redirect to sidepanel instead
        chrome.runtime.sendMessage({ action: 'openSidePanel' });
        break;

      case 'startExploreMode':
        startExploreMode();
        break;
        
      case 'analyzeScreenshot':
        handleScreenshotAnalysis(message.data);
        break;
        
      case 'summarizePage':
        handlePageSummary(message.data);
        break;
        
      case 'getPageContent':
        sendResponse(getPageContent());
        break;
        
      case 'analyzeSelectedText':
        if (selectedText) {
          analyzeSelectedText(selectedText);
        }
        break;
    }
  });
}

// Create floating button
function createFloatButton() {
  if (floatButton) return;
  
  floatButton = document.createElement('div');
  floatButton.id = 'ai-assistant-float-button';
  floatButton.innerHTML = `
    <div class="float-button-icon">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
      </svg>
    </div>
  `;
  
  floatButton.style.cssText = `
    position: fixed;
    top: 50%;
    right: 20px;
    width: 56px;
    height: 56px;
    background: #4285f4;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    cursor: pointer;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    transform: translateY(-50%);
  `;
  
  floatButton.addEventListener('click', () => {
    chrome.runtime.sendMessage({ action: 'openSidePanel' });
  });
  
  floatButton.addEventListener('mouseenter', () => {
    floatButton.style.transform = 'translateY(-50%) scale(1.1)';
    floatButton.style.boxShadow = '0 6px 16px rgba(0,0,0,0.2)';
  });
  
  floatButton.addEventListener('mouseleave', () => {
    floatButton.style.transform = 'translateY(-50%) scale(1)';
    floatButton.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
  });
  
  document.body.appendChild(floatButton);
}

// Setup text selection handling
function setupTextSelection() {
  let selectionTimeout;
  
  document.addEventListener('mouseup', () => {
    clearTimeout(selectionTimeout);
    selectionTimeout = setTimeout(() => {
      handleTextSelection();
    }, 100);
  });
  
  document.addEventListener('keyup', (e) => {
    if (e.key === 'ArrowLeft' || e.key === 'ArrowRight' || 
        e.key === 'ArrowUp' || e.key === 'ArrowDown') {
      clearTimeout(selectionTimeout);
      selectionTimeout = setTimeout(() => {
        handleTextSelection();
      }, 100);
    }
  });
}

// Handle text selection
function handleTextSelection() {
  const selection = window.getSelection();
  const text = selection.toString().trim();

  if (text && text.length > 0) {
    selectedText = text;

    // Always show toolbar when text is selected, not just in explore mode
    showExploreToolbar(selection);
  } else {
    selectedText = '';
    hideExploreToolbar();
  }
}

// Start explore mode
function startExploreMode() {
  isExploreMode = true;
  document.body.style.cursor = 'crosshair';

  // Add click handler for screenshots
  document.addEventListener('click', handleExploreClick, true);

  // Auto-exit after 30 seconds
  setTimeout(() => {
    if (isExploreMode) {
      exitExploreMode();
    }
  }, 30000);
}

// Exit explore mode
function exitExploreMode() {
  isExploreMode = false;
  document.body.style.cursor = '';
  hideExploreToolbar();
  document.removeEventListener('click', handleExploreClick, true);
}

// Handle explore mode clicks
function handleExploreClick(e) {
  if (!isExploreMode) return;
  
  e.preventDefault();
  e.stopPropagation();
  
  // Take screenshot of clicked area
  chrome.runtime.sendMessage({ action: 'takeScreenshot' });
  exitExploreMode();
}



// Show explore toolbar for selected text
async function showExploreToolbar(selection) {
  hideExploreToolbar();

  if (selection.rangeCount === 0) return;

  const range = selection.getRangeAt(0);
  const rect = range.getBoundingClientRect();

  // Don't show toolbar if selection is too small or empty
  if (rect.width < 10 || rect.height < 10) return;

  // Get explore actions from settings
  const settings = await loadSettings();
  const exploreActions = settings.exploreActions || [
    { title: "解释", prompt: "请解释以下文本的含义：\n\n\"{text}\"" },
    { title: "翻译", prompt: "请将以下文本翻译成中文：\n\n\"{text}\"" },
    { title: "总结", prompt: "请总结以下文本的要点：\n\n\"{text}\"" }
  ];

  const toolbar = document.createElement('div');
  toolbar.id = 'ai-assistant-explore-toolbar';

  // Generate buttons from settings
  const buttonsHTML = exploreActions.map((action, index) =>
    `<button class="explore-btn" data-action-index="${index}">${action.title}</button>`
  ).join('');

  toolbar.innerHTML = buttonsHTML;

  // Calculate position - show above or below selection
  const viewportHeight = window.innerHeight;
  const spaceAbove = rect.top;
  const spaceBelow = viewportHeight - rect.bottom;

  let toolbarTop;
  if (spaceAbove > 60) {
    // Show above selection
    toolbarTop = rect.top - 60;
  } else if (spaceBelow > 60) {
    // Show below selection
    toolbarTop = rect.bottom + 10;
  } else {
    // Show at top of viewport
    toolbarTop = 10;
  }

  const toolbarLeft = Math.min(Math.max(150, rect.left + rect.width / 2), window.innerWidth - 150);

  toolbar.style.cssText = `
    position: fixed;
    top: ${toolbarTop}px;
    left: ${toolbarLeft}px;
    transform: translateX(-50%);
    background: #ffffff;
    border: 2px solid #1a73e8;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
    z-index: 10002;
    display: flex;
    gap: 6px;
    padding: 8px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  `;
  
  // Add event listeners to buttons
  toolbar.querySelectorAll('.explore-btn').forEach(btn => {
    btn.style.cssText = `
      padding: 8px 16px;
      border: 1px solid #1a73e8;
      border-radius: 6px;
      background: #1a73e8;
      color: #ffffff;
      cursor: pointer;
      font-size: 13px;
      font-weight: 500;
      transition: all 0.2s;
      outline: none;
    `;

    btn.addEventListener('click', (e) => {
      const actionIndex = parseInt(e.target.dataset.actionIndex);
      const action = exploreActions[actionIndex];
      handleExploreAction(action, selectedText);
      hideExploreToolbar();
    });

    btn.addEventListener('mouseenter', () => {
      btn.style.background = '#1557b0';
      btn.style.borderColor = '#1557b0';
    });

    btn.addEventListener('mouseleave', () => {
      btn.style.background = '#1a73e8';
      btn.style.borderColor = '#1a73e8';
    });
  });
  
  document.body.appendChild(toolbar);
  
  // Auto-hide after 10 seconds
  setTimeout(() => {
    hideExploreToolbar();
  }, 10000);
}

// Hide explore toolbar
function hideExploreToolbar() {
  const toolbar = document.getElementById('ai-assistant-explore-toolbar');
  if (toolbar) {
    toolbar.remove();
  }
}

// Handle explore actions
function handleExploreAction(action, text) {
  if (!action || !action.title || !action.prompt) {
    console.error('Invalid action configuration:', action);
    return;
  }

  // Replace {text} placeholder with actual selected text
  const prompt = action.prompt.replace(/\{text\}/g, text);

  // Open sidepanel first
  chrome.runtime.sendMessage({ action: 'openSidePanel' });

  // Send message with user action context
  setTimeout(() => {
    chrome.runtime.sendMessage({
      action: 'addExploreMessage',
      data: {
        action: action.title,
        selectedText: text,
        prompt: prompt
      }
    });
  }, 300);
}

// Analyze selected text
function analyzeSelectedText(text) {
  chrome.runtime.sendMessage({
    action: 'analyzeSelectedText',
    data: {
      selectedText: text,
      pageContext: getPageContext()
    }
  });
}

// Handle screenshot analysis
function handleScreenshotAnalysis(data) {
  // This would typically open the result in the sidepanel or modal
  console.log('Screenshot analysis result:', data);
}

// Handle page summary
function handlePageSummary(_data) {
  const content = getPageContent();
  
  chrome.runtime.sendMessage({
    action: 'sendChatMessage',
    data: {
      message: `请总结以下网页内容：\n\n${content}`,
      context: []
    }
  });
}

// Get page content for analysis
function getPageContent() {
  // Extract main content from the page
  const content = [];
  
  // Try to get main content area
  const mainSelectors = ['main', 'article', '.content', '#content', '.post', '.entry'];
  let mainContent = null;
  
  for (const selector of mainSelectors) {
    mainContent = document.querySelector(selector);
    if (mainContent) break;
  }
  
  if (!mainContent) {
    mainContent = document.body;
  }
  
  // Extract text from headings and paragraphs
  const elements = mainContent.querySelectorAll('h1, h2, h3, h4, h5, h6, p, li');
  
  elements.forEach(el => {
    const text = el.textContent.trim();
    if (text && text.length > 10) {
      content.push(text);
    }
  });
  
  return content.slice(0, 50).join('\n\n'); // Limit content length
}

// Get page context for better analysis
function getPageContext() {
  const title = document.title;
  const url = window.location.href;
  const description = document.querySelector('meta[name="description"]')?.content || '';
  
  return `页面标题: ${title}\n页面URL: ${url}\n页面描述: ${description}`;
}

// Open chat modal (for modal mode)
function openChatModal() {
  if (chatModal) {
    chatModal.style.display = 'flex';
    return;
  }
  
  // Create modal (simplified version)
  chatModal = document.createElement('div');
  chatModal.id = 'ai-assistant-chat-modal';
  chatModal.innerHTML = `
    <div class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h3>AI Assistant</h3>
          <button class="modal-close">×</button>
        </div>
        <div class="modal-body">
          <p>Chat modal would be implemented here...</p>
        </div>
      </div>
    </div>
  `;
  
  chatModal.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10003;
    display: flex;
    align-items: center;
    justify-content: center;
  `;
  
  // Add close functionality
  chatModal.querySelector('.modal-close').addEventListener('click', () => {
    chatModal.style.display = 'none';
  });
  
  chatModal.querySelector('.modal-overlay').addEventListener('click', (e) => {
    if (e.target === e.currentTarget) {
      chatModal.style.display = 'none';
    }
  });
  
  document.body.appendChild(chatModal);
}

// Expose functions for global access
window.exitExploreMode = exitExploreMode;
