// Popup script for AI Assistant Chrome Extension

document.addEventListener('DOMContentLoaded', async () => {
  await initializePopup();
  setupEventListeners();
});

// Initialize popup
async function initializePopup() {
  try {
    // Load current settings
    const settings = await getSettings();
    
    // Check if API key is configured
    if (!settings.apiKey) {
      showApiSetup();
    } else {
      showMainContent();
    }
    
    // Update UI with current settings
    updateSettingsUI(settings);
    
  } catch (error) {
    console.error('Error initializing popup:', error);
    showError('初始化失败，请刷新重试');
  }
}

// Setup event listeners
function setupEventListeners() {
  // Header actions
  document.getElementById('settings-btn').addEventListener('click', toggleSettings);
  
  // API setup
  document.getElementById('save-api-key').addEventListener('click', saveApiKey);
  document.getElementById('api-key-input').addEventListener('keypress', (e) => {
    if (e.key === 'Enter') saveApiKey();
  });
  
  // Quick actions
  document.getElementById('open-chat').addEventListener('click', openChat);
  document.getElementById('explore-page').addEventListener('click', explorePage);
  document.getElementById('take-screenshot').addEventListener('click', takeScreenshot);
  document.getElementById('summarize-page').addEventListener('click', summarizePage);
  
  // Settings
  document.getElementById('save-settings').addEventListener('click', saveSettings);
  document.getElementById('reset-settings').addEventListener('click', resetSettings);
}

// Show API setup section
function showApiSetup() {
  document.getElementById('api-setup').style.display = 'block';
  document.getElementById('quick-actions').style.display = 'none';
  document.getElementById('shortcuts-info').style.display = 'none';
}

// Show main content
function showMainContent() {
  document.getElementById('api-setup').style.display = 'none';
  document.getElementById('quick-actions').style.display = 'block';
  document.getElementById('shortcuts-info').style.display = 'block';
}

// Toggle settings section
function toggleSettings() {
  const settingsSection = document.getElementById('settings-section');
  const isVisible = settingsSection.style.display !== 'none';
  settingsSection.style.display = isVisible ? 'none' : 'block';
}

// Save API key
async function saveApiKey() {
  const apiKeyInput = document.getElementById('api-key-input');
  const apiKey = apiKeyInput.value.trim();
  
  if (!apiKey) {
    showError('请输入有效的 API Key');
    return;
  }
  
  showLoading('保存中...');
  
  try {
    const result = await updateSettings({ apiKey });
    
    if (result.success) {
      showMainContent();
      showSuccess('API Key 保存成功');
      apiKeyInput.value = '';
    } else {
      showError(result.error || '保存失败');
    }
  } catch (error) {
    console.error('Error saving API key:', error);
    showError('保存失败，请重试');
  } finally {
    hideLoading();
  }
}

// Open chat
async function openChat() {
  try {
    const settings = await getSettings();
    
    if (settings.windowMode === 'sidepanel') {
      // Open side panel
      await sendMessage({ action: 'openSidePanel' });
    } else {
      // Send message to content script to open modal
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      await chrome.tabs.sendMessage(tab.id, { action: 'openChatModal' });
    }
    
    window.close();
  } catch (error) {
    console.error('Error opening chat:', error);
    showError('无法打开对话窗口');
  }
}

// Explore page
async function explorePage() {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    await chrome.tabs.sendMessage(tab.id, { action: 'startExploreMode' });
    window.close();
  } catch (error) {
    console.error('Error starting explore mode:', error);
    showError('无法启动探索模式');
  }
}

// Take screenshot
async function takeScreenshot() {
  showLoading('截图中...');
  
  try {
    const result = await sendMessage({ action: 'takeScreenshot' });
    
    if (result.success) {
      // Send screenshot to content script for analysis
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      await chrome.tabs.sendMessage(tab.id, {
        action: 'analyzeScreenshot',
        data: {
          imageDataUrl: result.data.imageDataUrl,
          prompt: CONFIG.MESSAGES.SCREENSHOT_PROMPT
        }
      });
      window.close();
    } else {
      showError(result.error || '截图失败');
    }
  } catch (error) {
    console.error('Error taking screenshot:', error);
    showError('截图失败，请重试');
  } finally {
    hideLoading();
  }
}

// Summarize page
async function summarizePage() {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    await chrome.tabs.sendMessage(tab.id, { 
      action: 'summarizePage',
      data: { prompt: CONFIG.MESSAGES.SUMMARY_PROMPT }
    });
    window.close();
  } catch (error) {
    console.error('Error summarizing page:', error);
    showError('无法总结页面');
  }
}

// Save settings
async function saveSettings() {
  showLoading('保存中...');
  
  try {
    const settings = {
      model: document.getElementById('model-select').value,
      windowMode: document.getElementById('window-mode').value,
      enableFloatButton: document.getElementById('enable-float-button').checked,
      enableUnderline: document.getElementById('enable-underline').checked
    };
    
    const result = await updateSettings(settings);
    
    if (result.success) {
      showSuccess('设置保存成功');
      setTimeout(() => {
        document.getElementById('settings-section').style.display = 'none';
      }, 1000);
    } else {
      showError(result.error || '保存失败');
    }
  } catch (error) {
    console.error('Error saving settings:', error);
    showError('保存失败，请重试');
  } finally {
    hideLoading();
  }
}

// Reset settings
async function resetSettings() {
  if (!confirm('确定要重置所有设置吗？')) {
    return;
  }
  
  showLoading('重置中...');
  
  try {
    const result = await updateSettings(CONFIG.DEFAULT_SETTINGS);
    
    if (result.success) {
      updateSettingsUI(CONFIG.DEFAULT_SETTINGS);
      showSuccess('设置已重置');
    } else {
      showError(result.error || '重置失败');
    }
  } catch (error) {
    console.error('Error resetting settings:', error);
    showError('重置失败，请重试');
  } finally {
    hideLoading();
  }
}

// Update settings UI
function updateSettingsUI(settings) {
  document.getElementById('model-select').value = settings.model || CONFIG.DEFAULT_SETTINGS.model;
  document.getElementById('window-mode').value = settings.windowMode || CONFIG.DEFAULT_SETTINGS.windowMode;
  document.getElementById('enable-float-button').checked = settings.enableFloatButton !== false;
  document.getElementById('enable-underline').checked = settings.enableUnderline !== false;
}

// Utility functions
async function getSettings() {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ action: 'getSettings' }, resolve);
  });
}

async function updateSettings(settings) {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ action: 'updateSettings', data: settings }, resolve);
  });
}

async function sendMessage(message) {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage(message, resolve);
  });
}

function showLoading(text = '处理中...') {
  const overlay = document.getElementById('loading-overlay');
  const loadingText = overlay.querySelector('.loading-text');
  loadingText.textContent = text;
  overlay.style.display = 'flex';
}

function hideLoading() {
  document.getElementById('loading-overlay').style.display = 'none';
}

function showError(message) {
  // Simple error display - could be enhanced with a proper notification system
  alert('错误: ' + message);
}

function showSuccess(message) {
  // Simple success display - could be enhanced with a proper notification system
  alert('成功: ' + message);
}
