import{j as e,d as b,b as _,A as f,C as d,a as r,c as N,e as S,r as u,s as w,i as h,f as k}from"./collect-Y551xQiu.js";const C="_waiting_866n9_14",A="_dot_866n9_21",P="_spread_866n9_1",g={waiting:C,dot:A,spread:P},y=[0,1,2];function E(){return e.jsx("div",{className:g.waiting,children:y.map(i=>e.jsx("div",{className:g.dot},i))})}const H="_slider_1fbm9_15",v={switch:"_switch_1fbm9_1",slider:H};function m(i){const{checked:a,onChange:s}=i;return e.jsxs("label",{className:v.switch,onClick:n=>n.stopPropagation(),children:[e.jsx("input",{type:"checkbox",checked:a,onChange:()=>s==null?void 0:s(!a),onClick:n=>n.stopPropagation()}),e.jsx("span",{className:v.slider})]})}const o=b({state:{loading:!1,user:void 0,chat_window_mode_visible:!1},actions:({getState:i,setState:a})=>({closeSidePanel(){const n=_.extension.getViews().find(c=>c.document.title==="Kimi SidePanel");n==null||n.close()},logout(){const{user:s}=i();a({user:void 0}),f.clearToken(),o.closeSidePanel(),d.event("msh_logout",{user_unique_id:s==null?void 0:s.id})},async initApp(){var n,c;a({loading:!0});const s=await r.initEnv("popup");if(s.token){const p=await N.auth.getUser().catch(()=>{o.logout()});a({user:p})}d.profile({msh_ext_shortcut:(n=s.shortcut)==null?void 0:n.chat,msh_ext_shortcut_explore:(c=s.shortcut)==null?void 0:c.explore,msh_ext_toolbar:s.explore_toolbar?"true":"false",msh_ext_explore_underline:s.explore_underline?"true":"false",msh_ext_float_button:s.chat_float_button?"true":"false",msh_ext_window_mode:s.chat_window_mode}),a({loading:!1,chat_window_mode_visible:S.feature.sidepanel.includes(s.browser_name)})},toHome(s="button"){d.event("msh_click_jump_master_web",{msh_jump_type:s}),_.tabs.create({url:u.home})},toLogin(){if(i().user)return o.toHome("avatar");d.event("msh_click_jump_master_web",{msh_jump_type:"avatar"}),_.tabs.create({url:u.login})},toShortcuts(){_.tabs.create({url:u.shortcuts})},setChatWindowMode(s){w.setItem("chat_window_mode",s),d.profile({msh_ext_window_mode:s}),s==="modal"&&o.closeSidePanel()},toggleSwitch(s){const n={explore_toolbar:"msh_ext_toolbar",explore_underline:"msh_ext_explore_underline",chat_float_button:"msh_ext_float_button",chat_window_mode:"msh_ext_window_mode"},c=!r.getState()[s];w.setItem(s,c),d.profile({[n[s]]:c?"true":"false"})}})});o.initApp();const t=_.i18n.getMessage;function I(){var x,j;const i=o.useState(),a=r.useState(l=>l.shortcut),s=r.useState(l=>l.explore_toolbar),n=r.useState(l=>l.explore_underline),c=r.useState(l=>l.chat_float_button),p=r.useState(l=>l.chat_window_mode);return i.loading?e.jsx("div",{className:"loading",children:e.jsx(E,{})}):e.jsxs("div",{className:"popup",children:[e.jsxs("div",{className:"user",onClick:o.toLogin,children:[(x=i.user)!=null&&x.avatar?e.jsx("img",{src:i.user.avatar,alt:"avatar",className:"avatar"}):e.jsx(h.user,{}),e.jsx("span",{className:"name",children:((j=i.user)==null?void 0:j.name)||t("popup_login")})]}),e.jsxs("div",{className:"main",children:[e.jsx("span",{className:"title",children:t("popup_explore_title")}),e.jsxs("div",{className:"menu",children:[e.jsxs("div",{className:"item",onClick:o.toShortcuts,children:[e.jsx("span",{children:t("popup_explore_shortcut")}),e.jsx("span",{className:"shortcut",children:(a==null?void 0:a.explore)||t("shortcut")})]}),e.jsxs("div",{className:"item",children:[e.jsx("span",{children:t("popup_explore_toolbar")}),e.jsx(m,{checked:s,onChange:()=>o.toggleSwitch("explore_toolbar")})]}),e.jsxs("div",{className:"item",children:[e.jsx("span",{children:t("popup_explore_underline")}),e.jsx(m,{checked:n,onChange:()=>o.toggleSwitch("explore_underline")})]})]}),e.jsx("span",{className:"title",children:t("popup_chat_title")}),e.jsxs("div",{className:"menu",children:[e.jsxs("div",{className:"item",onClick:o.toShortcuts,children:[e.jsx("span",{children:t("popup_chat_shortcut")}),e.jsx("span",{className:"shortcut",children:(a==null?void 0:a.chat)||t("shortcut")})]}),e.jsxs("div",{className:"item",children:[e.jsx("span",{children:t("popup_chat_float_button")}),e.jsx(m,{checked:c,onChange:()=>o.toggleSwitch("chat_float_button")})]}),i.chat_window_mode_visible&&e.jsxs("div",{className:"item",children:[e.jsx("span",{children:t("popup_chat_window_mode")}),e.jsxs("select",{className:"select",value:p,onChange:l=>{o.setChatWindowMode(l.target.value)},children:[e.jsx("option",{value:"modal",children:t("popup_chat_window_mode_modal")}),e.jsx("option",{value:"sidepanel",children:t("popup_chat_window_mode_sidepanel")})]})]})]})]}),e.jsxs("div",{className:"footer",children:[e.jsxs("div",{className:"btn",onClick:()=>o.toHome(),children:[e.jsx(h.home,{}),e.jsx("span",{children:t("visit_kimi")})]}),!!i.user&&e.jsxs("div",{className:"btn",onClick:o.logout,children:[e.jsx(h.logout,{}),e.jsx("span",{children:t("popup_logout")})]})]})]})}k.createRoot(document.getElementById("root")).render(e.jsx(I,{}));
