// Sidepanel script for AI Assistant Chrome Extension - Clean Version

let chatHistory = [];
let isLoading = false;
let isMarkdownMode = true;

// Global error handler
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error);
  console.error('Error details:', {
    message: event.message,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno
  });
});

// Unhandled promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
});

// Configuration object
const APP_CONFIG = {
  STORAGE_KEYS: {
    CHAT_HISTORY: 'chatHistory',
    SETTINGS: 'settings'
  },
  DEFAULT_SETTINGS: {
    model: 'anthropic/claude-3.5-sonnet',
    enableUnderline: false,
    enableMarkdown: true,
    exploreActions: [
      { title: '解释', prompt: '请解释以下内容：{text}' },
      { title: '翻译', prompt: '请将以下内容翻译成中文：{text}' },
      { title: '总结', prompt: '请总结以下内容：{text}' },
      { title: '分析', prompt: '请分析以下内容：{text}' }
    ]
  },
  MESSAGES: {
    SCREENSHOT_PROMPT: '请分析这张截图的内容',
    ERROR: '抱歉，处理您的请求时出现了错误，请重试'
  }
};

document.addEventListener('DOMContentLoaded', async () => {
  console.log('DOM Content Loaded, starting initialization...');
  try {
    await initializeSidepanel();
    setupEventListeners();
    setupMessageListener();
    console.log('Initialization completed successfully');
  } catch (error) {
    console.error('Initialization failed:', error);
  }
});

// Initialize sidepanel
async function initializeSidepanel() {
  try {
    const settings = await getSettings();

    // Initialize markdown mode from settings
    isMarkdownMode = settings.enableMarkdown !== false;
    updateMarkdownButton();

    // Check if API key is configured
    if (!settings.apiKey) {
      showApiSetup();
    } else {
      showChatContainer();
    }

    // Update settings UI
    updateSettingsUI(settings);

    // Load current session
    await loadCurrentSession();

  } catch (error) {
    console.error('Error initializing sidepanel:', error);
    showError('初始化失败，请刷新重试');
  }
}

// Update markdown button state
function updateMarkdownButton() {
  const button = document.getElementById('global-markdown-toggle');
  
  if (!button) {
    console.warn('Global markdown toggle button not found');
    return;
  }

  if (isMarkdownMode) {
    button.classList.add('active');
    button.title = '切换到纯文本模式';
  } else {
    button.classList.remove('active');
    button.title = '切换到Markdown模式';
  }
  
  console.log(`Markdown mode updated: ${isMarkdownMode}`);
}

// Setup message listener for background script communication
function setupMessageListener() {
  chrome.runtime.onMessage.addListener(handleBackgroundMessage);
}

// Setup event listeners
function setupEventListeners() {
  console.log('Setting up event listeners...');
  
  // Helper function to safely add event listener
  function safeAddEventListener(id, event, handler) {
    const element = document.getElementById(id);
    if (element) {
      element.addEventListener(event, handler);
      console.log(`✓ Added listener for ${id}`);
    } else {
      console.warn(`✗ Element not found: ${id}`);
    }
  }

  // Header actions
  safeAddEventListener('history-btn', 'click', toggleHistory);
  safeAddEventListener('new-chat-btn', 'click', startNewChat);
  safeAddEventListener('settings-btn', 'click', toggleSettings);

  // API setup
  safeAddEventListener('save-api-key', 'click', saveApiKey);
  
  const apiKeyInput = document.getElementById('api-key-input');
  if (apiKeyInput) {
    apiKeyInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') saveApiKey();
    });
    console.log('✓ Added keypress listener for api-key-input');
  } else {
    console.warn('✗ Element not found: api-key-input');
  }
  
  // Chat input
  const messageInput = document.getElementById('message-input');
  if (messageInput) {
    messageInput.addEventListener('input', handleInputChange);
    messageInput.addEventListener('keydown', handleKeyDown);
    console.log('✓ Added listeners for message-input');
  } else {
    console.warn('✗ Element not found: message-input');
  }
  
  // Send button
  safeAddEventListener('send-btn', 'click', sendMessage);
  
  // Quick actions
  const quickActionBtns = document.querySelectorAll('.quick-action-btn');
  if (quickActionBtns.length > 0) {
    quickActionBtns.forEach(btn => {
      btn.addEventListener('click', handleQuickAction);
    });
    console.log(`✓ Added listeners for ${quickActionBtns.length} quick action buttons`);
  } else {
    console.warn('✗ No quick action buttons found');
  }
  
  // File upload
  safeAddEventListener('attach-btn', 'click', () => {
    const fileInput = document.getElementById('file-input');
    if (fileInput) {
      fileInput.click();
    }
  });
  safeAddEventListener('file-input', 'change', handleFileUpload);

  // Screenshot
  safeAddEventListener('screenshot-btn', 'click', startAreaScreenshot);
  
  // History
  safeAddEventListener('close-history', 'click', () => {
    const historyPanel = document.getElementById('history-panel');
    if (historyPanel) {
      historyPanel.style.display = 'none';
    }
  });
  safeAddEventListener('export-history', 'click', exportHistory);
  safeAddEventListener('clear-all-history', 'click', clearAllHistory);

  // Settings
  safeAddEventListener('close-settings', 'click', () => {
    const settingsPanel = document.getElementById('settings-panel');
    if (settingsPanel) {
      settingsPanel.style.display = 'none';
    }
  });
  safeAddEventListener('update-api-key', 'click', updateApiKey);
  safeAddEventListener('save-settings', 'click', saveSettings);
  safeAddEventListener('clear-history', 'click', clearChatHistory);

  // API Key visibility toggles
  safeAddEventListener('toggle-api-key-visibility', 'click', () => {
    togglePasswordVisibility('api-key-setting');
  });
  safeAddEventListener('toggle-initial-api-key-visibility', 'click', () => {
    togglePasswordVisibility('api-key-input');
  });

  // Global Markdown toggle
  safeAddEventListener('global-markdown-toggle', 'click', toggleGlobalMarkdownMode);
  
  // Test button
  safeAddEventListener('test-btn', 'click', () => {
    console.log('Test button clicked!');
    showSuccess('测试按钮工作正常！');
  });

  // Model suggestions
  const modelSuggestions = document.querySelectorAll('.model-suggestion');
  if (modelSuggestions.length > 0) {
    modelSuggestions.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const model = e.target.dataset.model;
        const modelInput = document.getElementById('model-input');
        if (modelInput) {
          modelInput.value = model;
        }
      });
    });
    console.log(`✓ Added listeners for ${modelSuggestions.length} model suggestion buttons`);
  } else {
    console.warn('✗ No model suggestion buttons found');
  }

  // Explore actions config
  safeAddEventListener('manage-explore-actions', 'click', openExploreActionsModal);
  safeAddEventListener('close-explore-modal', 'click', closeExploreActionsModal);
  safeAddEventListener('cancel-explore-actions', 'click', closeExploreActionsModal);
  safeAddEventListener('save-explore-actions', 'click', saveExploreActionsFromModal);
  safeAddEventListener('add-action-row', 'click', addActionRow);
  safeAddEventListener('reset-default-actions', 'click', resetDefaultActions);
  
  console.log('Event listeners setup completed');
  
  // Add a test button click handler for debugging
  setTimeout(() => {
    testButtonFunctionality();
  }, 1000);
}

// Test button functionality
function testButtonFunctionality() {
  console.log('Testing button functionality...');
  
  // Test if buttons exist
  const testButtons = [
    'history-btn',
    'new-chat-btn', 
    'settings-btn',
    'global-markdown-toggle',
    'send-btn',
    'attach-btn',
    'screenshot-btn'
  ];
  
  testButtons.forEach(id => {
    const btn = document.getElementById(id);
    if (btn) {
      console.log(`✓ Button found: ${id}`);
    } else {
      console.error(`✗ Button not found: ${id}`);
    }
  });
}

// Show API setup
function showApiSetup() {
  document.getElementById('api-setup').style.display = 'block';
  document.getElementById('chat-container').style.display = 'none';
}

// Show chat container
function showChatContainer() {
  document.getElementById('api-setup').style.display = 'none';
  document.getElementById('chat-container').style.display = 'flex';
}

// Save API key
async function saveApiKey() {
  console.log('saveApiKey function called');

  const apiKeyInput = document.getElementById('api-key-input');
  if (!apiKeyInput) {
    console.error('api-key-input element not found');
    showError('页面元素未找到，请刷新页面重试');
    return;
  }

  const apiKey = apiKeyInput.value.trim();
  console.log('API Key length:', apiKey.length);

  if (!apiKey) {
    showError('请输入有效的 API Key');
    return;
  }

  // Validate API key format
  if (!/^[\x00-\x7F]+$/.test(apiKey)) {
    showError('API Key 包含无效字符，请确保只包含英文字母、数字和符号');
    return;
  }

  if (!apiKey.startsWith('sk-or-')) {
    showError('请输入有效的 OpenRouter API Key（应以 sk-or- 开头）');
    return;
  }

  try {
    const result = await updateSettings({ apiKey });

    if (result.success) {
      showChatContainer();
      showSuccess('API Key 保存成功');
      apiKeyInput.value = '';
    } else {
      showError(result.error || '保存失败');
    }
  } catch (error) {
    console.error('Error saving API key:', error);
    showError('保存失败，请重试');
  }
}

// Toggle history panel
function toggleHistory() {
  const historyPanel = document.getElementById('history-panel');
  if (historyPanel.style.display === 'none' || !historyPanel.style.display) {
    historyPanel.style.display = 'block';
    loadHistoryList();
  } else {
    historyPanel.style.display = 'none';
  }
}

// Toggle settings panel
function toggleSettings() {
  const settingsPanel = document.getElementById('settings-panel');
  if (settingsPanel.style.display === 'none' || !settingsPanel.style.display) {
    settingsPanel.style.display = 'block';
  } else {
    settingsPanel.style.display = 'none';
  }
}

// Start new chat
async function startNewChat() {
  if (chatHistory.length > 0) {
    if (confirm('开始新对话将保存当前会话到历史记录，是否继续？')) {
      // Save current session to history
      await saveToHistoricalSessions();

      // Clear current chat
      chatHistory = [];
      document.getElementById('chat-messages').innerHTML = `
        <div class="message-group welcome-message">
          <div class="message assistant-message">
            <div class="message-content">
              <p>👋 你好！我是 AI 助手，有什么可以帮助你的吗？</p>
            </div>
          </div>
        </div>
      `;

      showSuccess('当前会话已保存，已开始新对话');
    }
  }
}

// Toggle global markdown mode
function toggleGlobalMarkdownMode() {
  isMarkdownMode = !isMarkdownMode;

  const button = document.getElementById('global-markdown-toggle');

  if (isMarkdownMode) {
    button.classList.add('active');
    button.title = '切换到纯文本模式';
  } else {
    button.classList.remove('active');
    button.title = '切换到Markdown模式';
  }

  // Re-render all assistant messages
  rerenderAllMessages();
}

// Re-render all messages
function rerenderAllMessages() {
  const assistantMessages = document.querySelectorAll('.assistant-message .message-content');
  assistantMessages.forEach(messageContent => {
    const originalContent = messageContent.dataset.originalContent;
    if (originalContent) {
      messageContent.dataset.isMarkdown = isMarkdownMode.toString();

      if (isMarkdownMode) {
        const parsedContent = parseMarkdown(originalContent);
        messageContent.innerHTML = `<p>${parsedContent}</p>`;
      } else {
        messageContent.innerHTML = originalContent.replace(/\n/g, '<br>');
      }
    }
  });
}

// Parse markdown (simplified)
function parseMarkdown(text) {
  return text
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
    .replace(/\n/g, '<br>');
}

// Utility functions
async function getSettings() {
  console.log('Getting settings from background...');
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ action: 'getSettings' }, (response) => {
      console.log('Settings received:', response);
      resolve(response || APP_CONFIG.DEFAULT_SETTINGS);
    });
  });
}

async function updateSettings(settings) {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ action: 'updateSettings', data: settings }, resolve);
  });
}

function updateSettingsUI(settings) {
  // Update API key display
  const apiKeyDisplay = document.getElementById('api-key-setting');
  if (apiKeyDisplay && settings.apiKey) {
    apiKeyDisplay.value = settings.apiKey;
  }

  // Update model input
  const modelInput = document.getElementById('model-input');
  if (modelInput) {
    modelInput.value = settings.model || APP_CONFIG.DEFAULT_SETTINGS.model;
  }

  // Update markdown setting
  const markdownCheckbox = document.getElementById('enable-markdown');
  if (markdownCheckbox) {
    markdownCheckbox.checked = settings.enableMarkdown !== false;
  }
}

// Show/hide functions
function showLoading(message = '处理中...') {
  isLoading = true;
  const sendBtn = document.getElementById('send-btn');
  if (sendBtn) {
    sendBtn.disabled = true;
  }
}

function hideLoading() {
  isLoading = false;
  const sendBtn = document.getElementById('send-btn');
  if (sendBtn) {
    sendBtn.disabled = false;
  }
}

function showSuccess(message) {
  showToast(message, 'success');
}

function showError(message) {
  showToast(message, 'error');
}

function showToast(message, type = 'info') {
  const toast = document.createElement('div');
  toast.className = `toast toast-${type}`;
  toast.textContent = message;
  toast.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-size: 14px;
    z-index: 10000;
    max-width: 300px;
    word-wrap: break-word;
  `;

  if (type === 'success') {
    toast.style.backgroundColor = '#4caf50';
  } else if (type === 'error') {
    toast.style.backgroundColor = '#f44336';
  } else {
    toast.style.backgroundColor = '#2196f3';
  }

  document.body.appendChild(toast);

  setTimeout(() => {
    toast.remove();
  }, 3000);
}

// Save current session (not to historical sessions)
async function saveCurrentSession() {
  try {
    await chrome.storage.local.set({
      currentChatHistory: chatHistory
    });
  } catch (error) {
    console.error('Error saving current session:', error);
  }
}

// Save current session to historical sessions
async function saveToHistoricalSessions() {
  if (chatHistory.length === 0) return;

  try {
    // Create session object
    const session = {
      id: Date.now(),
      title: generateSessionTitle(),
      messages: [...chatHistory],
      timestamp: Date.now()
    };

    // Get existing sessions and add new one
    const result = await chrome.storage.local.get(['sessionHistory']);
    const sessions = result.sessionHistory || [];
    sessions.unshift(session); // Add to beginning

    // Keep only last 50 sessions
    if (sessions.length > 50) {
      sessions.splice(50);
    }

    await chrome.storage.local.set({ sessionHistory: sessions });
  } catch (error) {
    console.error('Error saving to historical sessions:', error);
  }
}

// Generate session title from first user message
function generateSessionTitle() {
  const firstUserMessage = chatHistory.find(msg => msg.role === 'user');
  if (firstUserMessage) {
    const content = firstUserMessage.content;
    // Take first 30 characters and add ellipsis if longer
    return content.length > 30 ? content.substring(0, 30) + '...' : content;
  }
  return '新对话';
}

// Load current session
async function loadCurrentSession() {
  try {
    const result = await chrome.storage.local.get(['currentChatHistory']);
    const history = result.currentChatHistory || [];

    if (history.length > 0) {
      chatHistory = history;
      const messagesContainer = document.getElementById('chat-messages');
      messagesContainer.innerHTML = '';

      history.forEach(msg => {
        addMessageToUI(msg.role, msg.content, msg.imageDataUrl);
      });
    }
  } catch (error) {
    console.error('Error loading current session:', error);
  }
}

// Add message to UI only (without saving to history)
async function addMessageToUI(role, content, imageDataUrl = null) {
  const messagesContainer = document.getElementById('chat-messages');
  const messageGroup = document.createElement('div');
  messageGroup.className = 'message-group';

  const message = document.createElement('div');
  message.className = `message ${role}-message`;

  const messageContent = document.createElement('div');
  messageContent.className = 'message-content';

  // Handle image display for user messages
  if (role === 'user' && imageDataUrl) {
    const imageContainer = document.createElement('div');
    imageContainer.className = 'message-image-container';

    const img = document.createElement('img');
    img.src = imageDataUrl;
    img.className = 'message-image';
    img.alt = '上传的图片';

    imageContainer.appendChild(img);
    messageContent.appendChild(imageContainer);

    // Add text content if exists
    if (content && content.trim()) {
      const textContent = document.createElement('div');
      textContent.className = 'message-text';
      textContent.textContent = content;
      messageContent.appendChild(textContent);
    }
  } else if (role === 'assistant') {
    // Store original content for re-rendering
    messageContent.dataset.originalContent = content;
    messageContent.dataset.isMarkdown = isMarkdownMode.toString();

    // Use global markdown mode setting
    if (isMarkdownMode) {
      // Parse markdown for assistant messages
      const parsedContent = parseMarkdown(content);
      messageContent.innerHTML = `<p>${parsedContent}</p>`;
    } else {
      // Plain text with line breaks
      messageContent.innerHTML = content.replace(/\n/g, '<br>');
    }
  } else {
    // Plain text for user messages
    messageContent.textContent = content;
  }

  message.appendChild(messageContent);
  messageGroup.appendChild(message);
  messagesContainer.appendChild(messageGroup);

  // Scroll to bottom
  messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Placeholder functions for missing functionality
function handleInputChange(e) {
  console.log('Input change:', e.target.value.length);
}

function handleKeyDown(e) {
  if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
    e.preventDefault();
    sendMessage();
  }
}

function sendMessage() {
  console.log('Send message triggered');
  showSuccess('发送消息功能开发中...');
}

function handleQuickAction(e) {
  console.log('Quick action clicked:', e.currentTarget.dataset.action);
  showSuccess('快速操作功能开发中...');
}

function handleFileUpload(e) {
  console.log('File upload triggered');
  showSuccess('文件上传功能开发中...');
}

function startAreaScreenshot() {
  console.log('Area screenshot triggered');
  showSuccess('区域截图功能开发中...');
}

function exportHistory() {
  console.log('Export history triggered');
  showSuccess('导出历史功能开发中...');
}

function clearAllHistory() {
  console.log('Clear all history triggered');
  showSuccess('清除历史功能开发中...');
}

function clearChatHistory() {
  console.log('Clear chat history triggered');
  showSuccess('清除聊天记录功能开发中...');
}

function updateApiKey() {
  console.log('Update API key triggered');
  showSuccess('更新API Key功能开发中...');
}

function saveSettings() {
  console.log('Save settings triggered');
  showSuccess('保存设置功能开发中...');
}

function togglePasswordVisibility(inputId) {
  console.log('Toggle password visibility for:', inputId);
  const input = document.getElementById(inputId);
  if (input) {
    input.type = input.type === 'password' ? 'text' : 'password';
  }
}

function openExploreActionsModal() {
  console.log('Open explore actions modal');
  showSuccess('探索动作配置功能开发中...');
}

function closeExploreActionsModal() {
  console.log('Close explore actions modal');
}

function saveExploreActionsFromModal() {
  console.log('Save explore actions');
  showSuccess('保存探索动作功能开发中...');
}

function addActionRow() {
  console.log('Add action row');
  showSuccess('添加动作行功能开发中...');
}

function resetDefaultActions() {
  console.log('Reset default actions');
  showSuccess('重置默认动作功能开发中...');
}

function loadHistoryList() {
  console.log('Load history list');
  showSuccess('加载历史列表功能开发中...');
}

function handleBackgroundMessage(message, sender, sendResponse) {
  console.log('Background message received:', message);
  return true;
}

function sendToBackground(message) {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage(message, resolve);
  });
}

function createStreamingMessage() {
  console.log('Create streaming message');
  return Date.now().toString();
}

function updateStreamingMessage(id, content, isComplete) {
  console.log('Update streaming message:', id, content, isComplete);
}

function getRecentContext() {
  return chatHistory.slice(-5);
}

// Save session when page is about to unload
window.addEventListener('beforeunload', async () => {
  if (chatHistory.length > 0) {
    await saveToHistoricalSessions();
  }
});
