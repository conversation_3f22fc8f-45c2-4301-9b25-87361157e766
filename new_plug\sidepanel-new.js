// Sidepanel script for AI Assistant Chrome Extension - Clean Version

let chatHistory = [];
let isLoading = false;
let isMarkdownMode = true;

// Global error handler
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error);
  console.error('Error details:', {
    message: event.message,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno
  });
});

// Unhandled promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
});

// Configuration object
const APP_CONFIG = {
  STORAGE_KEYS: {
    CHAT_HISTORY: 'chatHistory',
    SETTINGS: 'settings'
  },
  DEFAULT_SETTINGS: {
    model: 'anthropic/claude-3.5-sonnet',
    enableUnderline: false,
    enableMarkdown: true,
    exploreActions: [
      { title: '解释', prompt: '请解释以下内容：{text}' },
      { title: '翻译', prompt: '请将以下内容翻译成中文：{text}' },
      { title: '总结', prompt: '请总结以下内容：{text}' },
      { title: '分析', prompt: '请分析以下内容：{text}' }
    ]
  },
  MESSAGES: {
    SCREENSHOT_PROMPT: '请分析这张截图的内容',
    ERROR: '抱歉，处理您的请求时出现了错误，请重试'
  }
};

document.addEventListener('DOMContentLoaded', async () => {
  console.log('DOM Content Loaded, starting initialization...');
  try {
    await initializeSidepanel();
    setupEventListeners();
    setupMessageListener();
    console.log('Initialization completed successfully');
  } catch (error) {
    console.error('Initialization failed:', error);
  }
});

// Initialize sidepanel
async function initializeSidepanel() {
  try {
    const settings = await getSettings();

    // Initialize markdown mode from settings
    isMarkdownMode = settings.enableMarkdown !== false;
    updateMarkdownButton();

    // Check if API key is configured
    if (!settings.apiKey) {
      showApiSetup();
    } else {
      showChatContainer();
    }

    // Update settings UI
    updateSettingsUI(settings);

    // Load current session
    await loadCurrentSession();

  } catch (error) {
    console.error('Error initializing sidepanel:', error);
    showError('初始化失败，请刷新重试');
  }
}

// Update markdown button state
function updateMarkdownButton() {
  const button = document.getElementById('global-markdown-toggle');
  
  if (!button) {
    console.warn('Global markdown toggle button not found');
    return;
  }

  if (isMarkdownMode) {
    button.classList.add('active');
    button.title = '切换到纯文本模式';
  } else {
    button.classList.remove('active');
    button.title = '切换到Markdown模式';
  }
  
  console.log(`Markdown mode updated: ${isMarkdownMode}`);
}

// Setup message listener for background script communication
function setupMessageListener() {
  chrome.runtime.onMessage.addListener(handleBackgroundMessage);
}

// Setup event listeners
function setupEventListeners() {
  console.log('Setting up event listeners...');
  
  // Helper function to safely add event listener
  function safeAddEventListener(id, event, handler) {
    const element = document.getElementById(id);
    if (element) {
      element.addEventListener(event, handler);
      console.log(`✓ Added listener for ${id}`);
    } else {
      console.warn(`✗ Element not found: ${id}`);
    }
  }

  // Header actions
  safeAddEventListener('history-btn', 'click', toggleHistory);
  safeAddEventListener('new-chat-btn', 'click', startNewChat);
  safeAddEventListener('settings-btn', 'click', toggleSettings);

  // API setup
  safeAddEventListener('save-api-key', 'click', saveApiKey);
  
  const apiKeyInput = document.getElementById('api-key-input');
  if (apiKeyInput) {
    apiKeyInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') saveApiKey();
    });
    console.log('✓ Added keypress listener for api-key-input');
  } else {
    console.warn('✗ Element not found: api-key-input');
  }
  
  // Chat input
  const messageInput = document.getElementById('message-input');
  if (messageInput) {
    messageInput.addEventListener('input', handleInputChange);
    messageInput.addEventListener('keydown', handleKeyDown);
    console.log('✓ Added listeners for message-input');
  } else {
    console.warn('✗ Element not found: message-input');
  }
  
  // Send button
  safeAddEventListener('send-btn', 'click', sendMessage);
  
  // Quick actions
  const quickActionBtns = document.querySelectorAll('.quick-action-btn');
  if (quickActionBtns.length > 0) {
    quickActionBtns.forEach(btn => {
      btn.addEventListener('click', handleQuickAction);
    });
    console.log(`✓ Added listeners for ${quickActionBtns.length} quick action buttons`);
  } else {
    console.warn('✗ No quick action buttons found');
  }
  
  // File upload
  safeAddEventListener('attach-btn', 'click', () => {
    const fileInput = document.getElementById('file-input');
    if (fileInput) {
      fileInput.click();
    }
  });
  safeAddEventListener('file-input', 'change', handleFileUpload);

  // Screenshot
  safeAddEventListener('screenshot-btn', 'click', startAreaScreenshot);
  
  // History
  safeAddEventListener('close-history', 'click', () => {
    const historyPanel = document.getElementById('history-panel');
    if (historyPanel) {
      historyPanel.style.display = 'none';
    }
  });
  safeAddEventListener('export-history', 'click', exportHistory);
  safeAddEventListener('clear-all-history', 'click', clearAllHistory);

  // Settings
  safeAddEventListener('close-settings', 'click', () => {
    const settingsPanel = document.getElementById('settings-panel');
    if (settingsPanel) {
      settingsPanel.style.display = 'none';
    }
  });
  safeAddEventListener('update-api-key', 'click', updateApiKey);
  safeAddEventListener('save-settings', 'click', saveSettings);
  safeAddEventListener('clear-history', 'click', clearChatHistory);

  // API Key visibility toggles
  safeAddEventListener('toggle-api-key-visibility', 'click', () => {
    togglePasswordVisibility('api-key-setting');
  });
  safeAddEventListener('toggle-initial-api-key-visibility', 'click', () => {
    togglePasswordVisibility('api-key-input');
  });

  // Global Markdown toggle
  safeAddEventListener('global-markdown-toggle', 'click', toggleGlobalMarkdownMode);
  
  // Test button
  safeAddEventListener('test-btn', 'click', () => {
    console.log('Test button clicked!');
    showSuccess('测试按钮工作正常！');
  });

  // Model suggestions
  const modelSuggestions = document.querySelectorAll('.model-suggestion');
  if (modelSuggestions.length > 0) {
    modelSuggestions.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const model = e.target.dataset.model;
        const modelInput = document.getElementById('model-input');
        if (modelInput) {
          modelInput.value = model;
        }
      });
    });
    console.log(`✓ Added listeners for ${modelSuggestions.length} model suggestion buttons`);
  } else {
    console.warn('✗ No model suggestion buttons found');
  }

  // Explore actions config
  safeAddEventListener('manage-explore-actions', 'click', openExploreActionsModal);
  safeAddEventListener('close-explore-modal', 'click', closeExploreActionsModal);
  safeAddEventListener('cancel-explore-actions', 'click', closeExploreActionsModal);
  safeAddEventListener('save-explore-actions', 'click', saveExploreActionsFromModal);
  safeAddEventListener('add-action-row', 'click', addActionRow);
  safeAddEventListener('reset-default-actions', 'click', resetDefaultActions);
  
  console.log('Event listeners setup completed');
  
  // Add a test button click handler for debugging
  setTimeout(() => {
    testButtonFunctionality();
  }, 1000);
}

// Test button functionality
function testButtonFunctionality() {
  console.log('Testing button functionality...');
  
  // Test if buttons exist
  const testButtons = [
    'history-btn',
    'new-chat-btn', 
    'settings-btn',
    'global-markdown-toggle',
    'send-btn',
    'attach-btn',
    'screenshot-btn'
  ];
  
  testButtons.forEach(id => {
    const btn = document.getElementById(id);
    if (btn) {
      console.log(`✓ Button found: ${id}`);
    } else {
      console.error(`✗ Button not found: ${id}`);
    }
  });
}

// Show API setup
function showApiSetup() {
  document.getElementById('api-setup').style.display = 'block';
  document.getElementById('chat-container').style.display = 'none';
}

// Show chat container
function showChatContainer() {
  document.getElementById('api-setup').style.display = 'none';
  document.getElementById('chat-container').style.display = 'flex';
}

// Save API key
async function saveApiKey() {
  console.log('saveApiKey function called');

  const apiKeyInput = document.getElementById('api-key-input');
  if (!apiKeyInput) {
    console.error('api-key-input element not found');
    showError('页面元素未找到，请刷新页面重试');
    return;
  }

  const apiKey = apiKeyInput.value.trim();
  console.log('API Key length:', apiKey.length);

  if (!apiKey) {
    showError('请输入有效的 API Key');
    return;
  }

  // Validate API key format
  if (!/^[\x00-\x7F]+$/.test(apiKey)) {
    showError('API Key 包含无效字符，请确保只包含英文字母、数字和符号');
    return;
  }

  if (!apiKey.startsWith('sk-or-')) {
    showError('请输入有效的 OpenRouter API Key（应以 sk-or- 开头）');
    return;
  }

  try {
    const result = await updateSettings({ apiKey });

    if (result.success) {
      showChatContainer();
      showSuccess('API Key 保存成功');
      apiKeyInput.value = '';
    } else {
      showError(result.error || '保存失败');
    }
  } catch (error) {
    console.error('Error saving API key:', error);
    showError('保存失败，请重试');
  }
}

// Toggle history panel
function toggleHistory() {
  const historyPanel = document.getElementById('history-panel');
  if (historyPanel.style.display === 'none' || !historyPanel.style.display) {
    historyPanel.style.display = 'block';
    loadHistoryList();
  } else {
    historyPanel.style.display = 'none';
  }
}

// Toggle settings panel
function toggleSettings() {
  const settingsPanel = document.getElementById('settings-panel');
  if (settingsPanel.style.display === 'none' || !settingsPanel.style.display) {
    settingsPanel.style.display = 'block';
  } else {
    settingsPanel.style.display = 'none';
  }
}

// Start new chat
async function startNewChat() {
  if (chatHistory.length > 0) {
    if (confirm('开始新对话将保存当前会话到历史记录，是否继续？')) {
      // Save current session to history
      await saveToHistoricalSessions();

      // Clear current chat
      chatHistory = [];
      document.getElementById('chat-messages').innerHTML = `
        <div class="message-group welcome-message">
          <div class="message assistant-message">
            <div class="message-content">
              <p>👋 你好！我是 AI 助手，有什么可以帮助你的吗？</p>
            </div>
          </div>
        </div>
      `;

      showSuccess('当前会话已保存，已开始新对话');
    }
  }
}

// Toggle global markdown mode
function toggleGlobalMarkdownMode() {
  isMarkdownMode = !isMarkdownMode;

  const button = document.getElementById('global-markdown-toggle');

  if (isMarkdownMode) {
    button.classList.add('active');
    button.title = '切换到纯文本模式';
  } else {
    button.classList.remove('active');
    button.title = '切换到Markdown模式';
  }

  // Re-render all assistant messages
  rerenderAllMessages();
}

// Re-render all messages
function rerenderAllMessages() {
  const assistantMessages = document.querySelectorAll('.assistant-message .message-content');
  assistantMessages.forEach(messageContent => {
    const originalContent = messageContent.dataset.originalContent;
    if (originalContent) {
      messageContent.dataset.isMarkdown = isMarkdownMode.toString();

      if (isMarkdownMode) {
        const parsedContent = parseMarkdown(originalContent);
        messageContent.innerHTML = `<p>${parsedContent}</p>`;
      } else {
        messageContent.innerHTML = originalContent.replace(/\n/g, '<br>');
      }
    }
  });
}

// Parse markdown (simplified)
function parseMarkdown(text) {
  return text
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
    .replace(/\n/g, '<br>');
}

// Utility functions
async function getSettings() {
  console.log('Getting settings from background...');
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ action: 'getSettings' }, (response) => {
      console.log('Settings received:', response);
      resolve(response || APP_CONFIG.DEFAULT_SETTINGS);
    });
  });
}

async function updateSettings(settings) {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ action: 'updateSettings', data: settings }, resolve);
  });
}

function updateSettingsUI(settings) {
  // Update API key display
  const apiKeyDisplay = document.getElementById('api-key-setting');
  if (apiKeyDisplay && settings.apiKey) {
    apiKeyDisplay.value = settings.apiKey;
  }

  // Update model input
  const modelInput = document.getElementById('model-input');
  if (modelInput) {
    modelInput.value = settings.model || APP_CONFIG.DEFAULT_SETTINGS.model;
  }

  // Update markdown setting
  const markdownCheckbox = document.getElementById('enable-markdown');
  if (markdownCheckbox) {
    markdownCheckbox.checked = settings.enableMarkdown !== false;
  }
}

// Show/hide functions
function showLoading(message = '处理中...') {
  isLoading = true;
  const sendBtn = document.getElementById('send-btn');
  if (sendBtn) {
    sendBtn.disabled = true;
  }
}

function hideLoading() {
  isLoading = false;
  const sendBtn = document.getElementById('send-btn');
  if (sendBtn) {
    sendBtn.disabled = false;
  }
}

function showSuccess(message) {
  showToast(message, 'success');
}

function showError(message) {
  showToast(message, 'error');
}

function showToast(message, type = 'info') {
  const toast = document.createElement('div');
  toast.className = `toast toast-${type}`;
  toast.textContent = message;
  toast.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-size: 14px;
    z-index: 10000;
    max-width: 300px;
    word-wrap: break-word;
  `;

  if (type === 'success') {
    toast.style.backgroundColor = '#4caf50';
  } else if (type === 'error') {
    toast.style.backgroundColor = '#f44336';
  } else {
    toast.style.backgroundColor = '#2196f3';
  }

  document.body.appendChild(toast);

  setTimeout(() => {
    toast.remove();
  }, 3000);
}

// Save current session (not to historical sessions)
async function saveCurrentSession() {
  try {
    await chrome.storage.local.set({
      currentChatHistory: chatHistory
    });
  } catch (error) {
    console.error('Error saving current session:', error);
  }
}

// Save current session to historical sessions
async function saveToHistoricalSessions() {
  if (chatHistory.length === 0) return;

  try {
    // Create session object
    const session = {
      id: Date.now(),
      title: generateSessionTitle(),
      messages: [...chatHistory],
      timestamp: Date.now()
    };

    // Get existing sessions and add new one
    const result = await chrome.storage.local.get(['sessionHistory']);
    const sessions = result.sessionHistory || [];
    sessions.unshift(session); // Add to beginning

    // Keep only last 50 sessions
    if (sessions.length > 50) {
      sessions.splice(50);
    }

    await chrome.storage.local.set({ sessionHistory: sessions });
  } catch (error) {
    console.error('Error saving to historical sessions:', error);
  }
}

// Generate session title from first user message
function generateSessionTitle() {
  const firstUserMessage = chatHistory.find(msg => msg.role === 'user');
  if (firstUserMessage) {
    const content = firstUserMessage.content;
    // Take first 30 characters and add ellipsis if longer
    return content.length > 30 ? content.substring(0, 30) + '...' : content;
  }
  return '新对话';
}

// Load current session
async function loadCurrentSession() {
  try {
    const result = await chrome.storage.local.get(['currentChatHistory']);
    const history = result.currentChatHistory || [];

    if (history.length > 0) {
      chatHistory = history;
      const messagesContainer = document.getElementById('chat-messages');
      messagesContainer.innerHTML = '';

      history.forEach(msg => {
        addMessageToUI(msg.role, msg.content, msg.imageDataUrl);
      });
    }
  } catch (error) {
    console.error('Error loading current session:', error);
  }
}

// Add message to UI only (without saving to history)
async function addMessageToUI(role, content, imageDataUrl = null) {
  const messagesContainer = document.getElementById('chat-messages');
  const messageGroup = document.createElement('div');
  messageGroup.className = 'message-group';

  const message = document.createElement('div');
  message.className = `message ${role}-message`;

  const messageContent = document.createElement('div');
  messageContent.className = 'message-content';

  // Handle image display for user messages
  if (role === 'user' && imageDataUrl) {
    const imageContainer = document.createElement('div');
    imageContainer.className = 'message-image-container';

    const img = document.createElement('img');
    img.src = imageDataUrl;
    img.className = 'message-image';
    img.alt = '上传的图片';

    imageContainer.appendChild(img);
    messageContent.appendChild(imageContainer);

    // Add text content if exists
    if (content && content.trim()) {
      const textContent = document.createElement('div');
      textContent.className = 'message-text';
      textContent.textContent = content;
      messageContent.appendChild(textContent);
    }
  } else if (role === 'assistant') {
    // Store original content for re-rendering
    messageContent.dataset.originalContent = content;
    messageContent.dataset.isMarkdown = isMarkdownMode.toString();

    // Use global markdown mode setting
    if (isMarkdownMode) {
      // Parse markdown for assistant messages
      const parsedContent = parseMarkdown(content);
      messageContent.innerHTML = `<p>${parsedContent}</p>`;
    } else {
      // Plain text with line breaks
      messageContent.innerHTML = content.replace(/\n/g, '<br>');
    }
  } else {
    // Plain text for user messages
    messageContent.textContent = content;
  }

  message.appendChild(messageContent);
  messageGroup.appendChild(message);
  messagesContainer.appendChild(messageGroup);

  // Scroll to bottom
  messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Handle input change
function handleInputChange(e) {
  const input = e.target;
  const charCount = input.value.length;
  const charCountElement = document.querySelector('.char-count');
  if (charCountElement) {
    charCountElement.textContent = `${charCount}/2000`;
  }

  // Auto-resize textarea
  input.style.height = 'auto';
  input.style.height = Math.min(input.scrollHeight, 120) + 'px';

  // Update send button state
  const sendBtn = document.getElementById('send-btn');
  if (sendBtn) {
    sendBtn.disabled = !input.value.trim() || isLoading;
  }
}

function handleKeyDown(e) {
  if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
    e.preventDefault();
    sendMessage();
  }
}

// Send message
async function sendMessage() {
  const messageInput = document.getElementById('message-input');
  const message = messageInput.value.trim();

  if (!message || isLoading) return;

  // Add user message to chat
  addMessage('user', message);

  // Clear input
  messageInput.value = '';
  messageInput.style.height = 'auto';
  const charCountElement = document.querySelector('.char-count');
  if (charCountElement) {
    charCountElement.textContent = '0/2000';
  }

  // Show loading
  showLoading();

  // Create placeholder for streaming response
  const streamingMessageId = createStreamingMessage();

  try {
    // Send to background script with streaming support
    const response = await sendToBackground({
      action: 'sendChatMessageStream',
      data: {
        message: message,
        context: getRecentContext(),
        streamingMessageId: streamingMessageId
      }
    });

    if (!response.success) {
      updateStreamingMessage(streamingMessageId, response.error || APP_CONFIG.MESSAGES.ERROR, true);
    }
  } catch (error) {
    console.error('Error sending message:', error);
    updateStreamingMessage(streamingMessageId, APP_CONFIG.MESSAGES.ERROR, true);
  } finally {
    hideLoading();
  }
}

// Add message to chat
async function addMessage(role, content, imageDataUrl = null) {
  const messagesContainer = document.getElementById('chat-messages');
  const messageGroup = document.createElement('div');
  messageGroup.className = 'message-group';

  const message = document.createElement('div');
  message.className = `message ${role}-message`;

  // Add message header with toggle button for assistant messages
  if (role === 'assistant') {
    const messageHeader = document.createElement('div');
    messageHeader.className = 'message-header';

    const toggleBtn = document.createElement('button');
    toggleBtn.className = 'message-toggle-btn';
    toggleBtn.title = '切换显示模式';
    toggleBtn.innerHTML = `
      <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7H14A7,7 0 0,1 21,14H22A1,1 0 0,1 23,15V18A1,1 0 0,1 22,19H21V20A2,2 0 0,1 19,22H5A2,2 0 0,1 3,20V19H2A1,1 0 0,1 1,18V15A1,1 0 0,1 2,14H3A7,7 0 0,1 10,7H11V5.73C10.4,5.39 10,4.74 10,4A2,2 0 0,1 12,2M7.5,13A0.5,0.5 0 0,0 7,13.5A0.5,0.5 0 0,0 7.5,14A0.5,0.5 0 0,0 8,13.5A0.5,0.5 0 0,0 7.5,13M16.5,13A0.5,0.5 0 0,0 16,13.5A0.5,0.5 0 0,0 16.5,14A0.5,0.5 0 0,0 17,13.5A0.5,0.5 0 0,0 16.5,13Z"/>
      </svg>
    `;

    messageHeader.appendChild(toggleBtn);
    message.appendChild(messageHeader);

    // Add click handler to toggle button
    toggleBtn.addEventListener('click', () => toggleMessageMode(messageContent));
  }

  const messageContent = document.createElement('div');
  messageContent.className = 'message-content';

  // Handle image display for user messages
  if (role === 'user' && imageDataUrl) {
    const imageContainer = document.createElement('div');
    imageContainer.className = 'message-image-container';

    const img = document.createElement('img');
    img.src = imageDataUrl;
    img.className = 'message-image';
    img.alt = '上传的图片';

    imageContainer.appendChild(img);
    messageContent.appendChild(imageContainer);

    // Add text content if exists
    if (content && content.trim()) {
      const textContent = document.createElement('div');
      textContent.className = 'message-text';
      textContent.textContent = content;
      messageContent.appendChild(textContent);
    }
  } else if (role === 'assistant') {
    // Store original content for re-rendering
    messageContent.dataset.originalContent = content;
    messageContent.dataset.isMarkdown = isMarkdownMode.toString();

    // Use global markdown mode setting
    if (isMarkdownMode) {
      // Parse markdown for assistant messages
      const parsedContent = parseMarkdown(content);
      messageContent.innerHTML = `<p>${parsedContent}</p>`;
    } else {
      // Plain text with line breaks
      messageContent.innerHTML = content.replace(/\n/g, '<br>');
    }
  } else {
    // Plain text for user messages
    messageContent.textContent = content;
  }

  message.appendChild(messageContent);
  messageGroup.appendChild(message);
  messagesContainer.appendChild(messageGroup);

  // Save to current session
  const historyEntry = { role, content, timestamp: Date.now() };
  if (imageDataUrl) {
    historyEntry.imageDataUrl = imageDataUrl;
  }
  chatHistory.push(historyEntry);
  saveCurrentSession();

  // Scroll to bottom
  messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Toggle single message mode
function toggleMessageMode(messageContent) {
  const originalContent = messageContent.dataset.originalContent;
  const currentIsMarkdown = messageContent.dataset.isMarkdown === 'true';
  const newIsMarkdown = !currentIsMarkdown;

  messageContent.dataset.isMarkdown = newIsMarkdown.toString();

  if (newIsMarkdown) {
    // Parse markdown
    const parsedContent = parseMarkdown(originalContent);
    messageContent.innerHTML = `<p>${parsedContent}</p>`;
  } else {
    // Plain text with line breaks
    messageContent.innerHTML = originalContent.replace(/\n/g, '<br>');
  }
}

// Handle quick actions
async function handleQuickAction(e) {
  const action = e.currentTarget.dataset.action;

  try {
    switch (action) {
      case 'screenshot':
        await takeScreenshot();
        break;
      case 'summarize':
        await summarizePage();
        break;
      case 'explore':
        await startExploreMode();
        break;
      default:
        showSuccess(`快速操作 "${action}" 功能开发中...`);
    }
  } catch (error) {
    console.error('Error handling quick action:', error);
    showError('操作失败，请重试');
  }
}

// Summarize page
async function summarizePage() {
  showLoading('获取页面内容...');

  try {
    const result = await sendToBackground({ action: 'summarizePage' });

    if (result.success) {
      addMessage('user', '📄 请总结当前页面内容');

      const response = await sendToBackground({
        action: 'sendChatMessage',
        data: {
          message: `请总结以下页面内容：\n\n${result.data.content}`,
          context: getRecentContext()
        }
      });

      if (response.success) {
        addMessage('assistant', response.data.content);
      } else {
        addMessage('assistant', response.error || '页面总结失败');
      }
    } else {
      showError(result.error || '获取页面内容失败');
    }
  } catch (error) {
    console.error('Error summarizing page:', error);
    showError('页面总结失败，请重试');
  } finally {
    hideLoading();
  }
}

// Start explore mode
async function startExploreMode() {
  try {
    const result = await sendToBackground({ action: 'startExploreMode' });

    if (result.success) {
      showSuccess('探索模式已启动，请在页面上选择文本');
    } else {
      showError(result.error || '启动探索模式失败');
    }
  } catch (error) {
    console.error('Error starting explore mode:', error);
    showError('启动探索模式失败，请重试');
  }
}

// Handle file upload
function handleFileUpload(e) {
  const file = e.target.files[0];
  if (!file) return;

  if (!file.type.startsWith('image/')) {
    showError('请选择图片文件');
    return;
  }

  const reader = new FileReader();
  reader.onload = async (event) => {
    const imageDataUrl = event.target.result;

    addMessage('user', '🖼️ 请分析这张图片', imageDataUrl);
    showLoading();

    try {
      const response = await sendToBackground({
        action: 'analyzeScreenshot',
        data: {
          imageDataUrl: imageDataUrl,
          prompt: '请分析这张图片的内容'
        }
      });

      if (response.success) {
        addMessage('assistant', response.data.content);
      } else {
        addMessage('assistant', response.error || '图片分析失败');
      }
    } catch (error) {
      console.error('Error analyzing image:', error);
      addMessage('assistant', '图片分析失败，请重试');
    } finally {
      hideLoading();
    }
  };

  reader.readAsDataURL(file);
  e.target.value = ''; // Reset file input
}

// Start area screenshot
async function startAreaScreenshot() {
  showLoading('准备截图...');

  try {
    const result = await sendToBackground({ action: 'startAreaScreenshot' });

    if (result.success) {
      showSuccess('请在页面上拖拽选择截图区域');
    } else {
      showError(result.error || '启动截图失败');
    }
  } catch (error) {
    console.error('Error starting area screenshot:', error);
    showError('启动截图失败，请重试');
  } finally {
    hideLoading();
  }
}

// Take screenshot (for quick action)
async function takeScreenshot() {
  showLoading('截图中...');

  try {
    const result = await sendToBackground({ action: 'takeScreenshot' });

    if (result.success) {
      // Add screenshot to chat with image preview
      addScreenshotMessage(result.data.imageDataUrl);

      const response = await sendToBackground({
        action: 'analyzeScreenshot',
        data: {
          imageDataUrl: result.data.imageDataUrl,
          prompt: APP_CONFIG.MESSAGES.SCREENSHOT_PROMPT
        }
      });

      if (response.success) {
        addMessage('assistant', response.data.content);
      } else {
        addMessage('assistant', response.error || '截图分析失败');
      }
    } else {
      showError(result.error || '截图失败');
    }
  } catch (error) {
    console.error('Error taking screenshot:', error);
    showError('截图失败，请重试');
  } finally {
    hideLoading();
  }
}

// Add screenshot message with image preview
function addScreenshotMessage(imageDataUrl) {
  const messagesContainer = document.getElementById('chat-messages');
  const messageGroup = document.createElement('div');
  messageGroup.className = 'message-group';

  const message = document.createElement('div');
  message.className = 'message user-message';

  const messageContent = document.createElement('div');
  messageContent.className = 'message-content';

  const imagePreview = document.createElement('img');
  imagePreview.src = imageDataUrl;
  imagePreview.className = 'screenshot-preview';
  imagePreview.style.cssText = `
    max-width: 200px;
    max-height: 150px;
    border-radius: 8px;
    margin-bottom: 8px;
    cursor: pointer;
  `;

  const text = document.createElement('div');
  text.textContent = '📸 请分析这张截图';

  messageContent.appendChild(imagePreview);
  messageContent.appendChild(text);

  // Click to view full size
  imagePreview.addEventListener('click', () => {
    const modal = document.createElement('div');
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      cursor: pointer;
    `;

    const fullImage = document.createElement('img');
    fullImage.src = imageDataUrl;
    fullImage.style.cssText = `
      max-width: 90%;
      max-height: 90%;
      border-radius: 8px;
    `;

    modal.appendChild(fullImage);
    document.body.appendChild(modal);

    modal.addEventListener('click', () => {
      modal.remove();
    });
  });

  message.appendChild(messageContent);
  messageGroup.appendChild(message);
  messagesContainer.appendChild(messageGroup);

  // Save to history
  const historyEntry = {
    role: 'user',
    content: '📸 请分析这张截图',
    imageDataUrl: imageDataUrl,
    timestamp: Date.now()
  };
  chatHistory.push(historyEntry);
  saveCurrentSession();

  // Scroll to bottom
  messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Export history
async function exportHistory() {
  try {
    const result = await chrome.storage.local.get(['sessionHistory']);
    const sessions = result.sessionHistory || [];

    if (sessions.length === 0) {
      showError('没有历史记录可导出');
      return;
    }

    const exportData = {
      exportTime: new Date().toISOString(),
      sessions: sessions
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `ai-assistant-history-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showSuccess('历史记录已导出');
  } catch (error) {
    console.error('Error exporting history:', error);
    showError('导出历史记录失败');
  }
}

// Clear all history
async function clearAllHistory() {
  if (!confirm('确定要清除所有历史记录吗？此操作不可恢复。')) {
    return;
  }

  try {
    await chrome.storage.local.set({ sessionHistory: [] });
    loadHistoryList(); // Refresh the list
    showSuccess('所有历史记录已清除');
  } catch (error) {
    console.error('Error clearing all history:', error);
    showError('清除历史记录失败');
  }
}

// Clear chat history
async function clearChatHistory() {
  if (confirm('确定要清除所有历史记录吗？这将删除所有保存的会话。')) {
    try {
      // Clear all historical sessions
      await chrome.storage.local.set({ sessionHistory: [] });

      // Clear current session
      chatHistory = [];
      await chrome.storage.local.set({ currentChatHistory: [] });

      // Clear UI
      document.getElementById('chat-messages').innerHTML = `
        <div class="message-group welcome-message">
          <div class="message assistant-message">
            <div class="message-content">
              <p>👋 你好！我是 AI 助手，有什么可以帮助你的吗？</p>
            </div>
          </div>
        </div>
      `;

      showSuccess('所有历史记录已清除');
    } catch (error) {
      console.error('Error clearing history:', error);
      showError('清除历史记录失败');
    }
  }
}

// Update API key
async function updateApiKey() {
  const apiKeyInput = document.getElementById('api-key-setting');
  if (!apiKeyInput) {
    showError('API Key输入框未找到');
    return;
  }

  const apiKey = apiKeyInput.value.trim();
  if (!apiKey) {
    showError('请输入有效的 API Key');
    return;
  }

  // Validate API key format
  if (!/^[\x00-\x7F]+$/.test(apiKey)) {
    showError('API Key 包含无效字符，请确保只包含英文字母、数字和符号');
    return;
  }

  if (!apiKey.startsWith('sk-or-')) {
    showError('请输入有效的 OpenRouter API Key（应以 sk-or- 开头）');
    return;
  }

  try {
    const result = await updateSettings({ apiKey });

    if (result.success) {
      showSuccess('API Key 更新成功');
    } else {
      showError(result.error || '更新失败');
    }
  } catch (error) {
    console.error('Error updating API key:', error);
    showError('更新失败，请重试');
  }
}

// Save settings
async function saveSettings() {
  try {
    const settings = {};

    // Get model setting
    const modelInput = document.getElementById('model-input');
    if (modelInput) {
      settings.model = modelInput.value.trim() || APP_CONFIG.DEFAULT_SETTINGS.model;
    }

    // Get markdown setting
    const markdownCheckbox = document.getElementById('enable-markdown');
    if (markdownCheckbox) {
      settings.enableMarkdown = markdownCheckbox.checked;
    }

    // Get API key if changed
    const apiKeyInput = document.getElementById('api-key-setting');
    if (apiKeyInput && apiKeyInput.value.trim()) {
      const apiKey = apiKeyInput.value.trim();

      // Validate API key format
      if (!/^[\x00-\x7F]+$/.test(apiKey)) {
        showError('API Key 包含无效字符，请确保只包含英文字母、数字和符号');
        return;
      }

      if (!apiKey.startsWith('sk-or-')) {
        showError('请输入有效的 OpenRouter API Key（应以 sk-or- 开头）');
        return;
      }

      settings.apiKey = apiKey;
    }

    const result = await updateSettings(settings);

    if (result.success) {
      // Update global markdown mode if changed
      if (settings.enableMarkdown !== undefined) {
        isMarkdownMode = settings.enableMarkdown;
        updateMarkdownButton();
        rerenderAllMessages();
      }

      showSuccess('设置保存成功');

      // Close settings panel
      const settingsPanel = document.getElementById('settings-panel');
      if (settingsPanel) {
        settingsPanel.style.display = 'none';
      }
    } else {
      showError(result.error || '保存失败');
    }
  } catch (error) {
    console.error('Error saving settings:', error);
    showError('保存失败，请重试');
  }
}

function togglePasswordVisibility(inputId) {
  console.log('Toggle password visibility for:', inputId);
  const input = document.getElementById(inputId);
  if (input) {
    input.type = input.type === 'password' ? 'text' : 'password';
  }
}

function openExploreActionsModal() {
  console.log('Open explore actions modal');
  showSuccess('探索动作配置功能开发中...');
}

function closeExploreActionsModal() {
  console.log('Close explore actions modal');
}

function saveExploreActionsFromModal() {
  console.log('Save explore actions');
  showSuccess('保存探索动作功能开发中...');
}

function addActionRow() {
  console.log('Add action row');
  showSuccess('添加动作行功能开发中...');
}

function resetDefaultActions() {
  console.log('Reset default actions');
  showSuccess('重置默认动作功能开发中...');
}

// Load history list
async function loadHistoryList() {
  try {
    const result = await chrome.storage.local.get(['sessionHistory']);
    const sessions = result.sessionHistory || [];

    const historyList = document.getElementById('history-list');
    if (!historyList) {
      console.warn('History list element not found');
      return;
    }

    if (sessions.length === 0) {
      historyList.innerHTML = '<div class="no-history">暂无历史记录</div>';
      return;
    }

    historyList.innerHTML = sessions.map(session => `
      <div class="history-item" data-session-id="${session.id}">
        <div class="history-title">${session.title}</div>
        <div class="history-time">${new Date(session.timestamp).toLocaleString()}</div>
        <div class="history-actions">
          <button class="history-load-btn" onclick="loadHistorySession(${session.id})">加载</button>
          <button class="history-delete-btn" onclick="deleteHistorySession(${session.id})">删除</button>
        </div>
      </div>
    `).join('');
  } catch (error) {
    console.error('Error loading history list:', error);
    showError('加载历史列表失败');
  }
}

// Load history session
async function loadHistorySession(sessionId) {
  try {
    const result = await chrome.storage.local.get(['sessionHistory']);
    const sessions = result.sessionHistory || [];
    const session = sessions.find(s => s.id === sessionId);

    if (!session) {
      showError('历史记录未找到');
      return;
    }

    // Save current session if it has content
    if (chatHistory.length > 0) {
      if (confirm('加载历史记录将保存当前会话，是否继续？')) {
        await saveToHistoricalSessions();
      } else {
        return;
      }
    }

    // Load the session
    chatHistory = [...session.messages];

    // Clear and rebuild UI
    const messagesContainer = document.getElementById('chat-messages');
    messagesContainer.innerHTML = '';

    session.messages.forEach(msg => {
      addMessageToUI(msg.role, msg.content, msg.imageDataUrl);
    });

    // Save as current session
    await saveCurrentSession();

    // Close history panel
    const historyPanel = document.getElementById('history-panel');
    if (historyPanel) {
      historyPanel.style.display = 'none';
    }

    showSuccess('历史记录已加载');
  } catch (error) {
    console.error('Error loading history session:', error);
    showError('加载历史记录失败');
  }
}

// Delete history session
async function deleteHistorySession(sessionId) {
  if (!confirm('确定要删除这条历史记录吗？')) {
    return;
  }

  try {
    const result = await chrome.storage.local.get(['sessionHistory']);
    const sessions = result.sessionHistory || [];
    const filteredSessions = sessions.filter(s => s.id !== sessionId);

    await chrome.storage.local.set({ sessionHistory: filteredSessions });
    loadHistoryList(); // Refresh the list
    showSuccess('历史记录已删除');
  } catch (error) {
    console.error('Error deleting history session:', error);
    showError('删除历史记录失败');
  }
}

// Handle messages from background script
function handleBackgroundMessage(message, sender, sendResponse) {
  console.log('Sidepanel received message:', message);

  switch (message.action) {
    case 'displayExploreAction':
      handleDisplayExploreAction(message.data);
      break;

    case 'streamingChunk':
      handleStreamingChunk(message.data);
      break;

    case 'streamingError':
      handleStreamingError(message.data);
      break;

    case 'screenshotTaken':
      handleScreenshotTaken(message.data);
      break;

    default:
      console.log('Unknown message action:', message.action);
  }

  return true;
}

// Handle display explore action
async function handleDisplayExploreAction(data) {
  const { action, selectedText, prompt } = data;

  // Add user message showing the action and selected text
  addExploreUserMessage(action, selectedText);

  // Show loading
  showLoading();

  try {
    // Send to AI
    const response = await sendToBackground({
      action: 'sendChatMessage',
      data: {
        message: prompt,
        context: getRecentContext()
      }
    });

    if (response.success) {
      addMessage('assistant', response.data.content);
    } else {
      addMessage('assistant', response.error || '处理失败');
    }
  } catch (error) {
    console.error('Error processing explore action:', error);
    addMessage('assistant', '处理失败，请重试');
  } finally {
    hideLoading();
  }
}

// Add explore user message
function addExploreUserMessage(action, selectedText) {
  const messagesContainer = document.getElementById('chat-messages');
  const messageGroup = document.createElement('div');
  messageGroup.className = 'message-group';

  const message = document.createElement('div');
  message.className = 'message user-message';

  const messageContent = document.createElement('div');
  messageContent.className = 'message-content';

  const actionBadge = document.createElement('span');
  actionBadge.className = 'action-badge';
  actionBadge.textContent = action;

  const textContent = document.createElement('div');
  textContent.className = 'selected-text';
  textContent.textContent = selectedText;

  messageContent.appendChild(actionBadge);
  messageContent.appendChild(textContent);

  message.appendChild(messageContent);
  messageGroup.appendChild(message);
  messagesContainer.appendChild(messageGroup);

  // Save to history
  const historyEntry = {
    role: 'user',
    content: `${action}: ${selectedText}`,
    timestamp: Date.now()
  };
  chatHistory.push(historyEntry);
  saveCurrentSession();

  // Scroll to bottom
  messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Handle streaming chunk
function handleStreamingChunk(data) {
  const { messageId, chunk } = data;

  if (chunk.type === 'chunk') {
    // Update message with new content
    updateStreamingMessage(messageId, chunk.fullContent, false);
  } else if (chunk.type === 'done') {
    // Mark message as complete
    updateStreamingMessage(messageId, chunk.content, true);
  }
}

// Handle streaming error
function handleStreamingError(data) {
  const { messageId, error } = data;
  updateStreamingMessage(messageId, error, true);
}

// Handle screenshot taken
function handleScreenshotTaken(data) {
  const { imageDataUrl } = data;

  // Add screenshot message
  addScreenshotMessage(imageDataUrl);

  // Analyze screenshot
  showLoading('分析截图中...');

  sendToBackground({
    action: 'analyzeScreenshot',
    data: {
      imageDataUrl: imageDataUrl,
      prompt: APP_CONFIG.MESSAGES.SCREENSHOT_PROMPT
    }
  }).then(response => {
    if (response.success) {
      addMessage('assistant', response.data.content);
    } else {
      addMessage('assistant', response.error || '截图分析失败');
    }
  }).catch(error => {
    console.error('Error analyzing screenshot:', error);
    addMessage('assistant', '截图分析失败，请重试');
  }).finally(() => {
    hideLoading();
  });
}

function sendToBackground(message) {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage(message, resolve);
  });
}

// Create streaming message placeholder
function createStreamingMessage() {
  const messageId = Date.now().toString();
  const messagesContainer = document.getElementById('chat-messages');

  const messageGroup = document.createElement('div');
  messageGroup.className = 'message-group';
  messageGroup.id = `streaming-message-${messageId}`;

  const message = document.createElement('div');
  message.className = 'message assistant-message';

  const messageContent = document.createElement('div');
  messageContent.className = 'message-content streaming-content';
  messageContent.dataset.messageId = messageId;
  messageContent.innerHTML = '<div class="typing-indicator">AI 正在思考...</div>';

  message.appendChild(messageContent);
  messageGroup.appendChild(message);
  messagesContainer.appendChild(messageGroup);

  // Scroll to bottom
  messagesContainer.scrollTop = messagesContainer.scrollHeight;

  return messageId;
}

// Update streaming message content
function updateStreamingMessage(messageId, content, isComplete = false) {
  const messageContent = document.querySelector(`[data-message-id="${messageId}"]`);
  if (!messageContent) {
    console.warn('Streaming message not found:', messageId);
    return;
  }

  if (isComplete) {
    // Remove streaming class and finalize content
    messageContent.classList.remove('streaming-content');

    // Store original content for re-rendering
    messageContent.dataset.originalContent = content;
    messageContent.dataset.isMarkdown = isMarkdownMode.toString();

    // Render content based on markdown mode
    if (isMarkdownMode) {
      const parsedContent = parseMarkdown(content);
      messageContent.innerHTML = `<p>${parsedContent}</p>`;
    } else {
      messageContent.innerHTML = content.replace(/\n/g, '<br>');
    }

    // Add to chat history
    const historyEntry = {
      role: 'assistant',
      content: content,
      timestamp: Date.now()
    };
    chatHistory.push(historyEntry);
    saveCurrentSession();

    // Add toggle button for assistant messages
    const message = messageContent.closest('.message');
    if (message && !message.querySelector('.message-header')) {
      const messageHeader = document.createElement('div');
      messageHeader.className = 'message-header';

      const toggleBtn = document.createElement('button');
      toggleBtn.className = 'message-toggle-btn';
      toggleBtn.title = '切换显示模式';
      toggleBtn.innerHTML = `
        <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7H14A7,7 0 0,1 21,14H22A1,1 0 0,1 23,15V18A1,1 0 0,1 22,19H21V20A2,2 0 0,1 19,22H5A2,2 0 0,1 3,20V19H2A1,1 0 0,1 1,18V15A1,1 0 0,1 2,14H3A7,7 0 0,1 10,7H11V5.73C10.4,5.39 10,4.74 10,4A2,2 0 0,1 12,2M7.5,13A0.5,0.5 0 0,0 7,13.5A0.5,0.5 0 0,0 7.5,14A0.5,0.5 0 0,0 8,13.5A0.5,0.5 0 0,0 7.5,13M16.5,13A0.5,0.5 0 0,0 16,13.5A0.5,0.5 0 0,0 16.5,14A0.5,0.5 0 0,0 17,13.5A0.5,0.5 0 0,0 16.5,13Z"/>
        </svg>
      `;

      toggleBtn.addEventListener('click', () => toggleMessageMode(messageContent));
      messageHeader.appendChild(toggleBtn);
      message.insertBefore(messageHeader, messageContent);
    }
  } else {
    // Update streaming content
    if (isMarkdownMode) {
      const parsedContent = parseMarkdown(content);
      messageContent.innerHTML = `<p>${parsedContent}</p><div class="typing-indicator">...</div>`;
    } else {
      messageContent.innerHTML = content.replace(/\n/g, '<br>') + '<div class="typing-indicator">...</div>';
    }
  }

  // Scroll to bottom
  const messagesContainer = document.getElementById('chat-messages');
  messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function getRecentContext() {
  return chatHistory.slice(-5);
}

// Save session when page is about to unload
window.addEventListener('beforeunload', async () => {
  if (chatHistory.length > 0) {
    await saveToHistoricalSessions();
  }
});
